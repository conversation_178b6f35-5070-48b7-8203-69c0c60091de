import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
  size?: 'sm' | 'md' | 'lg';
  rounded?: boolean;
  outline?: boolean;
  icon?: React.ComponentType<any>;
  className?: string;
  animate?: boolean;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  rounded = true,
  outline = false,
  icon: Icon,
  className,
  animate = false
}) => {
  const baseClasses = "inline-flex items-center font-medium transition-all duration-200";

  const variants = {
    default: outline 
      ? "border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 bg-transparent"
      : "bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-slate-200",
    primary: outline
      ? "border border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300 bg-transparent"
      : "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-800",
    secondary: outline
      ? "border border-purple-300 dark:border-purple-600 text-purple-700 dark:text-purple-300 bg-transparent"
      : "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 border border-purple-200 dark:border-purple-800",
    success: outline
      ? "border border-green-300 dark:border-green-600 text-green-700 dark:text-green-300 bg-transparent"
      : "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800",
    danger: outline
      ? "border border-red-300 dark:border-red-600 text-red-700 dark:text-red-300 bg-transparent"
      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-800",
    warning: outline
      ? "border border-yellow-300 dark:border-yellow-600 text-yellow-700 dark:text-yellow-300 bg-transparent"
      : "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800",
    info: outline
      ? "border border-cyan-300 dark:border-cyan-600 text-cyan-700 dark:text-cyan-300 bg-transparent"
      : "bg-cyan-100 dark:bg-cyan-900/30 text-cyan-800 dark:text-cyan-300 border border-cyan-200 dark:border-cyan-800"
  };

  const sizes = {
    sm: "px-2 py-0.5 text-xs gap-1",
    md: "px-2.5 py-1 text-xs gap-1.5",
    lg: "px-3 py-1.5 text-sm gap-2"
  };

  const iconSizes = {
    sm: "w-3 h-3",
    md: "w-3 h-3",
    lg: "w-4 h-4"
  };

  const Component = animate ? motion.span : 'span';
  const animationProps = animate ? {
    initial: { scale: 0.8, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    transition: { type: "spring", stiffness: 500, damping: 30 }
  } : {};

  return (
    <Component
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        rounded ? "rounded-full" : "rounded-md",
        className
      )}
      {...animationProps}
    >
      {Icon && <Icon className={iconSizes[size]} />}
      {children}
    </Component>
  );
};

// Status Badge with predefined status colors
interface StatusBadgeProps extends Omit<BadgeProps, 'variant'> {
  status: 'online' | 'offline' | 'busy' | 'away' | 'pending' | 'approved' | 'rejected' | 'active' | 'inactive';
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, ...props }) => {
  const statusVariants = {
    online: 'success',
    offline: 'default',
    busy: 'danger',
    away: 'warning',
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    active: 'success',
    inactive: 'default'
  } as const;

  const statusLabels = {
    online: 'Online',
    offline: 'Offline',
    busy: 'Busy',
    away: 'Away',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    active: 'Active',
    inactive: 'Inactive'
  };

  return (
    <Badge variant={statusVariants[status]} {...props}>
      {statusLabels[status]}
    </Badge>
  );
};

// Notification Badge (typically used for counts)
interface NotificationBadgeProps {
  count: number;
  max?: number;
  showZero?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const NotificationBadge: React.FC<NotificationBadgeProps> = ({
  count,
  max = 99,
  showZero = false,
  className,
  size = 'sm'
}) => {
  if (count === 0 && !showZero) return null;

  const displayCount = count > max ? `${max}+` : count.toString();

  const sizes = {
    sm: "min-w-[18px] h-[18px] text-[10px]",
    md: "min-w-[20px] h-[20px] text-xs",
    lg: "min-w-[24px] h-[24px] text-sm"
  };

  return (
    <motion.span
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      className={cn(
        "inline-flex items-center justify-center px-1 rounded-full",
        "bg-red-500 text-white font-bold",
        sizes[size],
        className
      )}
    >
      {displayCount}
    </motion.span>
  );
};

// Dot Badge (small indicator)
interface DotBadgeProps {
  variant?: 'default' | 'primary' | 'success' | 'danger' | 'warning' | 'info';
  size?: 'sm' | 'md' | 'lg';
  pulse?: boolean;
  className?: string;
}

export const DotBadge: React.FC<DotBadgeProps> = ({
  variant = 'default',
  size = 'md',
  pulse = false,
  className
}) => {
  const variants = {
    default: 'bg-slate-400',
    primary: 'bg-blue-500',
    success: 'bg-green-500',
    danger: 'bg-red-500',
    warning: 'bg-yellow-500',
    info: 'bg-cyan-500'
  };

  const sizes = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  return (
    <span className="relative inline-flex">
      <span
        className={cn(
          "rounded-full",
          variants[variant],
          sizes[size],
          className
        )}
      />
      {pulse && (
        <span
          className={cn(
            "absolute top-0 left-0 rounded-full animate-ping",
            variants[variant],
            sizes[size],
            "opacity-75"
          )}
        />
      )}
    </span>
  );
};

// Interactive Badge (clickable)
interface InteractiveBadgeProps extends BadgeProps {
  onClick?: () => void;
  onRemove?: () => void;
  removable?: boolean;
}

export const InteractiveBadge: React.FC<InteractiveBadgeProps> = ({
  children,
  onClick,
  onRemove,
  removable = false,
  className,
  ...props
}) => {
  return (
    <motion.span
      className={cn(
        "relative group",
        onClick && "cursor-pointer hover:scale-105",
        className
      )}
      onClick={onClick}
      whileHover={{ scale: onClick ? 1.05 : 1 }}
      whileTap={{ scale: onClick ? 0.95 : 1 }}
    >
      <Badge {...props}>
        {children}
        {removable && onRemove && (
          <motion.button
            onClick={(e) => {
              e.stopPropagation();
              onRemove();
            }}
            className="ml-1 hover:bg-black/10 dark:hover:bg-white/10 rounded-full p-0.5 transition-colors"
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.8 }}
          >
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </motion.button>
        )}
      </Badge>
    </motion.span>
  );
};
