{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\AceternityNavbar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bell, User, Settings, LogOut, Menu, X, Home, FileText, Upload, MessageCircle, BarChart3, Store, Search, Moon, Sun, Printer, DollarSign, ChevronDown } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { cn } from '../../lib/utils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FloatingNav = () => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [notifications] = useState(3);\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const getNavItems = () => {\n    if ((user === null || user === void 0 ? void 0 : user.userType) === 'Student') {\n      return [{\n        icon: Home,\n        label: 'Dashboard',\n        href: '/student-dashboard',\n        active: true\n      }, {\n        icon: Upload,\n        label: 'Upload',\n        href: '/upload'\n      }, {\n        icon: FileText,\n        label: 'My Jobs',\n        href: '/my-jobs'\n      }, {\n        icon: MessageCircle,\n        label: 'Messages',\n        href: '/messages',\n        badge: 2\n      }, {\n        icon: Store,\n        label: 'Centers',\n        href: '/xerox-centers'\n      }];\n    } else if ((user === null || user === void 0 ? void 0 : user.userType) === 'XeroxCenter') {\n      return [{\n        icon: BarChart3,\n        label: 'Dashboard',\n        href: '/xerox-dashboard',\n        active: true\n      }, {\n        icon: FileText,\n        label: 'Jobs',\n        href: '/job-queue'\n      }, {\n        icon: Printer,\n        label: 'Active',\n        href: '/active-jobs',\n        badge: 5\n      }, {\n        icon: MessageCircle,\n        label: 'Messages',\n        href: '/messages',\n        badge: 3\n      }, {\n        icon: DollarSign,\n        label: 'Revenue',\n        href: '/revenue'\n      }];\n    }\n    return [];\n  };\n  const navItems = getNavItems();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        y: -100,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      transition: {\n        duration: 0.3\n      },\n      className: cn(\"fixed top-4 left-1/2 transform -translate-x-1/2 z-50\", \"w-[95%] max-w-7xl mx-auto\"),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: cn(\"relative rounded-2xl border border-white/20 bg-white/10 backdrop-blur-md\", \"shadow-lg shadow-black/5 transition-all duration-300\", isDarkMode ? \"bg-black/20 border-white/10\" : \"bg-white/20 border-black/10\", isScrolled && \"shadow-xl shadow-black/10\"),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between px-6 py-4\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Printer, {\n                  className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden sm:block\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                children: \"XeroxHub\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                children: \"Print Solutions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden lg:flex items-center space-x-1\",\n            children: navItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n              href: item.href,\n              initial: {\n                opacity: 0,\n                y: -20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: index * 0.1\n              },\n              whileHover: {\n                y: -2\n              },\n              className: cn(\"relative flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200\", \"hover:bg-white/20 dark:hover:bg-white/10\", item.active && \"bg-white/20 dark:bg-white/10\"),\n              children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this), item.badge && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  scale: 0\n                },\n                animate: {\n                  scale: 1\n                },\n                className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                children: item.badge\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 21\n              }, this)]\n            }, item.label, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              className: \"hidden sm:flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Search, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: toggleTheme,\n              className: \"flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\",\n              children: isDarkMode ? /*#__PURE__*/_jsxDEV(Sun, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 31\n              }, this) : /*#__PURE__*/_jsxDEV(Moon, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 61\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              className: \"relative flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(Bell, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), notifications > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  scale: 0\n                },\n                animate: {\n                  scale: 1\n                },\n                className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                children: notifications\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                className: \"flex items-center space-x-2 px-3 py-2 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(User, {\n                    className: \"w-4 h-4 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hidden sm:block text-left\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium\",\n                    children: user === null || user === void 0 ? void 0 : user.username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                    children: user === null || user === void 0 ? void 0 : user.userType\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 10,\n                    scale: 0.95\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1\n                  },\n                  exit: {\n                    opacity: 0,\n                    y: 10,\n                    scale: 0.95\n                  },\n                  className: \"absolute right-0 top-full mt-2 w-48 rounded-xl bg-white/90 dark:bg-black/90 backdrop-blur-md border border-white/20 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/profile\",\n                      className: \"flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-white/20 transition-colors\",\n                      children: [/*#__PURE__*/_jsxDEV(User, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm\",\n                        children: \"Profile\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 211,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/settings\",\n                      className: \"flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-white/20 transition-colors\",\n                      children: [/*#__PURE__*/_jsxDEV(Settings, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 214,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm\",\n                        children: \"Settings\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 215,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                      className: \"my-2 border-white/20\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: logout,\n                      className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-red-500/20 text-red-500 transition-colors\",\n                      children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 222,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm\",\n                        children: \"Logout\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 223,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n              className: \"lg:hidden flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\",\n              children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(X, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 37\n              }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 65\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isMobileMenuOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        exit: {\n          opacity: 0,\n          y: -20\n        },\n        className: \"fixed top-24 left-4 right-4 z-40 lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-2xl bg-white/90 dark:bg-black/90 backdrop-blur-md border border-white/20 shadow-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: navItems.map(item => /*#__PURE__*/_jsxDEV(\"a\", {\n              href: item.href,\n              onClick: () => setIsMobileMenuOpen(false),\n              className: cn(\"flex items-center space-x-3 px-4 py-3 rounded-xl transition-colors\", \"hover:bg-white/20 dark:hover:bg-white/10\", item.active && \"bg-white/20 dark:bg-white/10\"),\n              children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 21\n              }, this), item.badge && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-auto w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                children: item.badge\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 23\n              }, this)]\n            }, item.label, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(FloatingNav, \"y2jxPDQpuXbSlVZz7hHaxg6Z5wc=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = FloatingNav;\nexport default FloatingNav;\nvar _c;\n$RefreshReg$(_c, \"FloatingNav\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "Bell", "User", "Settings", "LogOut", "<PERSON><PERSON>", "X", "Home", "FileText", "Upload", "MessageCircle", "BarChart3", "Store", "Search", "Moon", "Sun", "Printer", "DollarSign", "ChevronDown", "useAuth", "useTheme", "cn", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FloatingNav", "_s", "user", "logout", "isDarkMode", "toggleTheme", "isScrolled", "setIsScrolled", "isMobileMenuOpen", "setIsMobileMenuOpen", "notifications", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "getNavItems", "userType", "icon", "label", "href", "active", "badge", "navItems", "children", "div", "initial", "y", "opacity", "animate", "transition", "duration", "className", "whileHover", "scale", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "a", "delay", "button", "whileTap", "onClick", "username", "exit", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/AceternityNavbar.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Bell, \n  User, \n  Settings, \n  LogOut, \n  Menu, \n  X, \n  Home,\n  FileText,\n  Upload,\n  MessageCircle,\n  BarChart3,\n  Store,\n  Search,\n  Moon,\n  Sun,\n  Printer,\n  DollarSign,\n  ChevronDown\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { cn } from '../../lib/utils';\n\ninterface NavItem {\n  icon: React.ComponentType<any>;\n  label: string;\n  href: string;\n  active?: boolean;\n  badge?: number;\n}\n\nconst FloatingNav = () => {\n  const { user, logout } = useAuth();\n  const { isDarkMode, toggleTheme } = useTheme();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [notifications] = useState(3);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const getNavItems = (): NavItem[] => {\n    if (user?.userType === 'Student') {\n      return [\n        { icon: Home, label: 'Dashboard', href: '/student-dashboard', active: true },\n        { icon: Upload, label: 'Upload', href: '/upload' },\n        { icon: FileText, label: 'My Jobs', href: '/my-jobs' },\n        { icon: MessageCircle, label: 'Messages', href: '/messages', badge: 2 },\n        { icon: Store, label: 'Centers', href: '/xerox-centers' }\n      ];\n    } else if (user?.userType === 'XeroxCenter') {\n      return [\n        { icon: BarChart3, label: 'Dashboard', href: '/xerox-dashboard', active: true },\n        { icon: FileText, label: 'Jobs', href: '/job-queue' },\n        { icon: Printer, label: 'Active', href: '/active-jobs', badge: 5 },\n        { icon: MessageCircle, label: 'Messages', href: '/messages', badge: 3 },\n        { icon: DollarSign, label: 'Revenue', href: '/revenue' }\n      ];\n    }\n    return [];\n  };\n\n  const navItems = getNavItems();\n\n  return (\n    <>\n      {/* Main Floating Navbar */}\n      <motion.div\n        initial={{ y: -100, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.3 }}\n        className={cn(\n          \"fixed top-4 left-1/2 transform -translate-x-1/2 z-50\",\n          \"w-[95%] max-w-7xl mx-auto\"\n        )}\n      >\n        <div\n          className={cn(\n            \"relative rounded-2xl border border-white/20 bg-white/10 backdrop-blur-md\",\n            \"shadow-lg shadow-black/5 transition-all duration-300\",\n            isDarkMode ? \"bg-black/20 border-white/10\" : \"bg-white/20 border-black/10\",\n            isScrolled && \"shadow-xl shadow-black/10\"\n          )}\n        >\n          <div className=\"flex items-center justify-between px-6 py-4\">\n            {/* Logo */}\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"flex items-center space-x-3\"\n            >\n              <div className=\"relative\">\n                <div className=\"w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\">\n                  <Printer className=\"w-5 h-5 text-white\" />\n                </div>\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse\"></div>\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                  XeroxHub\n                </h1>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">Print Solutions</p>\n              </div>\n            </motion.div>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden lg:flex items-center space-x-1\">\n              {navItems.map((item, index) => (\n                <motion.a\n                  key={item.label}\n                  href={item.href}\n                  initial={{ opacity: 0, y: -20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                  whileHover={{ y: -2 }}\n                  className={cn(\n                    \"relative flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200\",\n                    \"hover:bg-white/20 dark:hover:bg-white/10\",\n                    item.active && \"bg-white/20 dark:bg-white/10\"\n                  )}\n                >\n                  <item.icon className=\"w-4 h-4\" />\n                  <span className=\"text-sm font-medium\">{item.label}</span>\n                  {item.badge && (\n                    <motion.div\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\"\n                    >\n                      {item.badge}\n                    </motion.div>\n                  )}\n                </motion.a>\n              ))}\n            </div>\n\n            {/* Right Actions */}\n            <div className=\"flex items-center space-x-2\">\n              {/* Search */}\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"hidden sm:flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\"\n              >\n                <Search className=\"w-4 h-4\" />\n              </motion.button>\n\n              {/* Theme Toggle */}\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={toggleTheme}\n                className=\"flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\"\n              >\n                {isDarkMode ? <Sun className=\"w-4 h-4\" /> : <Moon className=\"w-4 h-4\" />}\n              </motion.button>\n\n              {/* Notifications */}\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"relative flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\"\n              >\n                <Bell className=\"w-4 h-4\" />\n                {notifications > 0 && (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\"\n                  >\n                    {notifications}\n                  </motion.div>\n                )}\n              </motion.button>\n\n              {/* User Menu */}\n              <div className=\"relative group\">\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  className=\"flex items-center space-x-2 px-3 py-2 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\"\n                >\n                  <div className=\"w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\">\n                    <User className=\"w-4 h-4 text-white\" />\n                  </div>\n                  <div className=\"hidden sm:block text-left\">\n                    <p className=\"text-sm font-medium\">{user?.username}</p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">{user?.userType}</p>\n                  </div>\n                  <ChevronDown className=\"w-4 h-4\" />\n                </motion.button>\n\n                {/* Dropdown Menu */}\n                <AnimatePresence>\n                  <motion.div\n                    initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                    animate={{ opacity: 1, y: 0, scale: 1 }}\n                    exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                    className=\"absolute right-0 top-full mt-2 w-48 rounded-xl bg-white/90 dark:bg-black/90 backdrop-blur-md border border-white/20 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity\"\n                  >\n                    <div className=\"p-2\">\n                      <a href=\"/profile\" className=\"flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-white/20 transition-colors\">\n                        <User className=\"w-4 h-4\" />\n                        <span className=\"text-sm\">Profile</span>\n                      </a>\n                      <a href=\"/settings\" className=\"flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-white/20 transition-colors\">\n                        <Settings className=\"w-4 h-4\" />\n                        <span className=\"text-sm\">Settings</span>\n                      </a>\n                      <hr className=\"my-2 border-white/20\" />\n                      <button\n                        onClick={logout}\n                        className=\"w-full flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-red-500/20 text-red-500 transition-colors\"\n                      >\n                        <LogOut className=\"w-4 h-4\" />\n                        <span className=\"text-sm\">Logout</span>\n                      </button>\n                    </div>\n                  </motion.div>\n                </AnimatePresence>\n              </div>\n\n              {/* Mobile Menu Button */}\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                className=\"lg:hidden flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\"\n              >\n                {isMobileMenuOpen ? <X className=\"w-4 h-4\" /> : <Menu className=\"w-4 h-4\" />}\n              </motion.button>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"fixed top-24 left-4 right-4 z-40 lg:hidden\"\n          >\n            <div className=\"rounded-2xl bg-white/90 dark:bg-black/90 backdrop-blur-md border border-white/20 shadow-lg p-4\">\n              <div className=\"space-y-2\">\n                {navItems.map((item) => (\n                  <a\n                    key={item.label}\n                    href={item.href}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={cn(\n                      \"flex items-center space-x-3 px-4 py-3 rounded-xl transition-colors\",\n                      \"hover:bg-white/20 dark:hover:bg-white/10\",\n                      item.active && \"bg-white/20 dark:bg-white/10\"\n                    )}\n                  >\n                    <item.icon className=\"w-5 h-5\" />\n                    <span className=\"font-medium\">{item.label}</span>\n                    {item.badge && (\n                      <div className=\"ml-auto w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                        {item.badge}\n                      </div>\n                    )}\n                  </a>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default FloatingNav;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJC,CAAC,EACDC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,OAAO,EACPC,UAAU,EACVC,WAAW,QACN,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAUrC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGV,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEW,UAAU;IAAEC;EAAY,CAAC,GAAGX,QAAQ,CAAC,CAAC;EAC9C,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EAEnCC,SAAS,CAAC,MAAM;IACd,MAAMuC,YAAY,GAAGA,CAAA,KAAM;MACzBJ,aAAa,CAACK,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,WAAW,GAAGA,CAAA,KAAiB;IACnC,IAAI,CAAAd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,QAAQ,MAAK,SAAS,EAAE;MAChC,OAAO,CACL;QAAEC,IAAI,EAAErC,IAAI;QAAEsC,KAAK,EAAE,WAAW;QAAEC,IAAI,EAAE,oBAAoB;QAAEC,MAAM,EAAE;MAAK,CAAC,EAC5E;QAAEH,IAAI,EAAEnC,MAAM;QAAEoC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAU,CAAC,EAClD;QAAEF,IAAI,EAAEpC,QAAQ;QAAEqC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAW,CAAC,EACtD;QAAEF,IAAI,EAAElC,aAAa;QAAEmC,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE,WAAW;QAAEE,KAAK,EAAE;MAAE,CAAC,EACvE;QAAEJ,IAAI,EAAEhC,KAAK;QAAEiC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAiB,CAAC,CAC1D;IACH,CAAC,MAAM,IAAI,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,QAAQ,MAAK,aAAa,EAAE;MAC3C,OAAO,CACL;QAAEC,IAAI,EAAEjC,SAAS;QAAEkC,KAAK,EAAE,WAAW;QAAEC,IAAI,EAAE,kBAAkB;QAAEC,MAAM,EAAE;MAAK,CAAC,EAC/E;QAAEH,IAAI,EAAEpC,QAAQ;QAAEqC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAa,CAAC,EACrD;QAAEF,IAAI,EAAE5B,OAAO;QAAE6B,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE,cAAc;QAAEE,KAAK,EAAE;MAAE,CAAC,EAClE;QAAEJ,IAAI,EAAElC,aAAa;QAAEmC,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE,WAAW;QAAEE,KAAK,EAAE;MAAE,CAAC,EACvE;QAAEJ,IAAI,EAAE3B,UAAU;QAAE4B,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAW,CAAC,CACzD;IACH;IACA,OAAO,EAAE;EACX,CAAC;EAED,MAAMG,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,oBACEnB,OAAA,CAAAE,SAAA;IAAAyB,QAAA,gBAEE3B,OAAA,CAACxB,MAAM,CAACoD,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,GAAG;QAAEC,OAAO,EAAE;MAAE,CAAE;MACjCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BC,SAAS,EAAErC,EAAE,CACX,sDAAsD,EACtD,2BACF,CAAE;MAAA6B,QAAA,eAEF3B,OAAA;QACEmC,SAAS,EAAErC,EAAE,CACX,0EAA0E,EAC1E,sDAAsD,EACtDS,UAAU,GAAG,6BAA6B,GAAG,6BAA6B,EAC1EE,UAAU,IAAI,2BAChB,CAAE;QAAAkB,QAAA,eAEF3B,OAAA;UAAKmC,SAAS,EAAC,6CAA6C;UAAAR,QAAA,gBAE1D3B,OAAA,CAACxB,MAAM,CAACoD,GAAG;YACTQ,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BF,SAAS,EAAC,6BAA6B;YAAAR,QAAA,gBAEvC3B,OAAA;cAAKmC,SAAS,EAAC,UAAU;cAAAR,QAAA,gBACvB3B,OAAA;gBAAKmC,SAAS,EAAC,qGAAqG;gBAAAR,QAAA,eAClH3B,OAAA,CAACP,OAAO;kBAAC0C,SAAS,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACNzC,OAAA;gBAAKmC,SAAS,EAAC;cAA0E;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,eACNzC,OAAA;cAAKmC,SAAS,EAAC,iBAAiB;cAAAR,QAAA,gBAC9B3B,OAAA;gBAAImC,SAAS,EAAC,8FAA8F;gBAAAR,QAAA,EAAC;cAE7G;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzC,OAAA;gBAAGmC,SAAS,EAAC,0CAA0C;gBAAAR,QAAA,EAAC;cAAe;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbzC,OAAA;YAAKmC,SAAS,EAAC,uCAAuC;YAAAR,QAAA,EACnDD,QAAQ,CAACgB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxB5C,OAAA,CAACxB,MAAM,CAACqE,CAAC;cAEPtB,IAAI,EAAEoB,IAAI,CAACpB,IAAK;cAChBM,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BG,UAAU,EAAE;gBAAEa,KAAK,EAAEF,KAAK,GAAG;cAAI,CAAE;cACnCR,UAAU,EAAE;gBAAEN,CAAC,EAAE,CAAC;cAAE,CAAE;cACtBK,SAAS,EAAErC,EAAE,CACX,uFAAuF,EACvF,0CAA0C,EAC1C6C,IAAI,CAACnB,MAAM,IAAI,8BACjB,CAAE;cAAAG,QAAA,gBAEF3B,OAAA,CAAC2C,IAAI,CAACtB,IAAI;gBAACc,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjCzC,OAAA;gBAAMmC,SAAS,EAAC,qBAAqB;gBAAAR,QAAA,EAAEgB,IAAI,CAACrB;cAAK;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACxDE,IAAI,CAAClB,KAAK,iBACTzB,OAAA,CAACxB,MAAM,CAACoD,GAAG;gBACTC,OAAO,EAAE;kBAAEQ,KAAK,EAAE;gBAAE,CAAE;gBACtBL,OAAO,EAAE;kBAAEK,KAAK,EAAE;gBAAE,CAAE;gBACtBF,SAAS,EAAC,8GAA8G;gBAAAR,QAAA,EAEvHgB,IAAI,CAAClB;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACb;YAAA,GAtBIE,IAAI,CAACrB,KAAK;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBP,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNzC,OAAA;YAAKmC,SAAS,EAAC,6BAA6B;YAAAR,QAAA,gBAE1C3B,OAAA,CAACxB,MAAM,CAACuE,MAAM;cACZX,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BF,SAAS,EAAC,iHAAiH;cAAAR,QAAA,eAE3H3B,OAAA,CAACV,MAAM;gBAAC6C,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eAGhBzC,OAAA,CAACxB,MAAM,CAACuE,MAAM;cACZX,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BY,OAAO,EAAEzC,WAAY;cACrB2B,SAAS,EAAC,uGAAuG;cAAAR,QAAA,EAEhHpB,UAAU,gBAAGP,OAAA,CAACR,GAAG;gBAAC2C,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGzC,OAAA,CAACT,IAAI;gBAAC4C,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eAGhBzC,OAAA,CAACxB,MAAM,CAACuE,MAAM;cACZX,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BF,SAAS,EAAC,gHAAgH;cAAAR,QAAA,gBAE1H3B,OAAA,CAACtB,IAAI;gBAACyD,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC3B5B,aAAa,GAAG,CAAC,iBAChBb,OAAA,CAACxB,MAAM,CAACoD,GAAG;gBACTC,OAAO,EAAE;kBAAEQ,KAAK,EAAE;gBAAE,CAAE;gBACtBL,OAAO,EAAE;kBAAEK,KAAK,EAAE;gBAAE,CAAE;gBACtBF,SAAS,EAAC,8GAA8G;gBAAAR,QAAA,EAEvHd;cAAa;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACY,CAAC,eAGhBzC,OAAA;cAAKmC,SAAS,EAAC,gBAAgB;cAAAR,QAAA,gBAC7B3B,OAAA,CAACxB,MAAM,CAACuE,MAAM;gBACZX,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BF,SAAS,EAAC,kGAAkG;gBAAAR,QAAA,gBAE5G3B,OAAA;kBAAKmC,SAAS,EAAC,mGAAmG;kBAAAR,QAAA,eAChH3B,OAAA,CAACrB,IAAI;oBAACwD,SAAS,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACNzC,OAAA;kBAAKmC,SAAS,EAAC,2BAA2B;kBAAAR,QAAA,gBACxC3B,OAAA;oBAAGmC,SAAS,EAAC,qBAAqB;oBAAAR,QAAA,EAAEtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C;kBAAQ;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvDzC,OAAA;oBAAGmC,SAAS,EAAC,0CAA0C;oBAAAR,QAAA,EAAEtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe;kBAAQ;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACNzC,OAAA,CAACL,WAAW;kBAACwC,SAAS,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eAGhBzC,OAAA,CAACvB,eAAe;gBAAAkD,QAAA,eACd3B,OAAA,CAACxB,MAAM,CAACoD,GAAG;kBACTC,OAAO,EAAE;oBAAEE,OAAO,EAAE,CAAC;oBAAED,CAAC,EAAE,EAAE;oBAAEO,KAAK,EAAE;kBAAK,CAAE;kBAC5CL,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAED,CAAC,EAAE,CAAC;oBAAEO,KAAK,EAAE;kBAAE,CAAE;kBACxCc,IAAI,EAAE;oBAAEpB,OAAO,EAAE,CAAC;oBAAED,CAAC,EAAE,EAAE;oBAAEO,KAAK,EAAE;kBAAK,CAAE;kBACzCF,SAAS,EAAC,oLAAoL;kBAAAR,QAAA,eAE9L3B,OAAA;oBAAKmC,SAAS,EAAC,KAAK;oBAAAR,QAAA,gBAClB3B,OAAA;sBAAGuB,IAAI,EAAC,UAAU;sBAACY,SAAS,EAAC,sFAAsF;sBAAAR,QAAA,gBACjH3B,OAAA,CAACrB,IAAI;wBAACwD,SAAS,EAAC;sBAAS;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC5BzC,OAAA;wBAAMmC,SAAS,EAAC,SAAS;wBAAAR,QAAA,EAAC;sBAAO;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACJzC,OAAA;sBAAGuB,IAAI,EAAC,WAAW;sBAACY,SAAS,EAAC,sFAAsF;sBAAAR,QAAA,gBAClH3B,OAAA,CAACpB,QAAQ;wBAACuD,SAAS,EAAC;sBAAS;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAChCzC,OAAA;wBAAMmC,SAAS,EAAC,SAAS;wBAAAR,QAAA,EAAC;sBAAQ;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,eACJzC,OAAA;sBAAImC,SAAS,EAAC;oBAAsB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvCzC,OAAA;sBACEiD,OAAO,EAAE3C,MAAO;sBAChB6B,SAAS,EAAC,4GAA4G;sBAAAR,QAAA,gBAEtH3B,OAAA,CAACnB,MAAM;wBAACsD,SAAS,EAAC;sBAAS;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC9BzC,OAAA;wBAAMmC,SAAS,EAAC,SAAS;wBAAAR,QAAA,EAAC;sBAAM;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAGNzC,OAAA,CAACxB,MAAM,CAACuE,MAAM;cACZX,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BY,OAAO,EAAEA,CAAA,KAAMrC,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;cACtDwB,SAAS,EAAC,iHAAiH;cAAAR,QAAA,EAE1HhB,gBAAgB,gBAAGX,OAAA,CAACjB,CAAC;gBAACoD,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGzC,OAAA,CAAClB,IAAI;gBAACqD,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbzC,OAAA,CAACvB,eAAe;MAAAkD,QAAA,EACbhB,gBAAgB,iBACfX,OAAA,CAACxB,MAAM,CAACoD,GAAG;QACTC,OAAO,EAAE;UAAEE,OAAO,EAAE,CAAC;UAAED,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCE,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAED,CAAC,EAAE;QAAE,CAAE;QAC9BqB,IAAI,EAAE;UAAEpB,OAAO,EAAE,CAAC;UAAED,CAAC,EAAE,CAAC;QAAG,CAAE;QAC7BK,SAAS,EAAC,4CAA4C;QAAAR,QAAA,eAEtD3B,OAAA;UAAKmC,SAAS,EAAC,gGAAgG;UAAAR,QAAA,eAC7G3B,OAAA;YAAKmC,SAAS,EAAC,WAAW;YAAAR,QAAA,EACvBD,QAAQ,CAACgB,GAAG,CAAEC,IAAI,iBACjB3C,OAAA;cAEEuB,IAAI,EAAEoB,IAAI,CAACpB,IAAK;cAChB0B,OAAO,EAAEA,CAAA,KAAMrC,mBAAmB,CAAC,KAAK,CAAE;cAC1CuB,SAAS,EAAErC,EAAE,CACX,oEAAoE,EACpE,0CAA0C,EAC1C6C,IAAI,CAACnB,MAAM,IAAI,8BACjB,CAAE;cAAAG,QAAA,gBAEF3B,OAAA,CAAC2C,IAAI,CAACtB,IAAI;gBAACc,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjCzC,OAAA;gBAAMmC,SAAS,EAAC,aAAa;gBAAAR,QAAA,EAAEgB,IAAI,CAACrB;cAAK;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAChDE,IAAI,CAAClB,KAAK,iBACTzB,OAAA;gBAAKmC,SAAS,EAAC,6FAA6F;gBAAAR,QAAA,EACzGgB,IAAI,CAAClB;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACN;YAAA,GAfIE,IAAI,CAACrB,KAAK;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBd,CACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA,eAClB,CAAC;AAEP,CAAC;AAACrC,EAAA,CAvPID,WAAW;EAAA,QACUP,OAAO,EACIC,QAAQ;AAAA;AAAAuD,EAAA,GAFxCjD,WAAW;AAyPjB,eAAeA,WAAW;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}