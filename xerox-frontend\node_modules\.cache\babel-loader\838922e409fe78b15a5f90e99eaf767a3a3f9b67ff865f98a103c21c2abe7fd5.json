{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const {\n    login,\n    isLoading,\n    error,\n    user\n  } = useAuth();\n  const {\n    isDarkMode\n  } = useTheme();\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const success = await login(email, password);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"mt-5\",\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-print fa-3x text-primary mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"h4 text-gray-900 mb-4\",\n                children: \"Welcome Back!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-exclamation-triangle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this), error]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              onSubmit: handleSubmit,\n              children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-envelope me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 53,\n                    columnNumber: 21\n                  }, this), \"Email Address\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"email\",\n                  placeholder: \"Enter email\",\n                  value: email,\n                  onChange: e => setEmail(e.target.value),\n                  required: true,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-lock me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 68,\n                    columnNumber: 21\n                  }, this), \"Password\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"password\",\n                  placeholder: \"Password\",\n                  value: password,\n                  onChange: e => setPassword(e.target.value),\n                  required: true,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                type: \"submit\",\n                className: \"w-100 mb-3\",\n                disabled: isLoading,\n                children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 89,\n                    columnNumber: 23\n                  }, this), \"Signing In...\"]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-sign-in-alt me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 101,\n                    columnNumber: 23\n                  }, this), \"Sign In\"]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"small\",\n                children: \"Don't have an account? Create Account!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mt-2\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/forgot-password\",\n                className: \"small\",\n                children: \"Forgot Password?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"sH/7LuAg6/my6GWwACHNz9AO03Y=\", false, function () {\n  return [useAuth, useTheme, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Link", "useNavigate", "useAuth", "useTheme", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "email", "setEmail", "password", "setPassword", "login", "isLoading", "error", "user", "isDarkMode", "navigate", "handleSubmit", "e", "preventDefault", "success", "className", "children", "md", "lg", "Body", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onSubmit", "Group", "Label", "Control", "type", "placeholder", "value", "onChange", "target", "required", "disabled", "as", "animation", "size", "role", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/Login.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, Row, Col, Card, Form, Button, Al<PERSON>, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Printer, Mail, Lock, LogIn } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport Layout from './Layout';\n\nconst Login: React.FC = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const { login, isLoading, error, user } = useAuth();\n  const { isDarkMode } = useTheme();\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    const success = await login(email, password);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n\n  return (\n    <Container className=\"mt-5\">\n      <Row className=\"justify-content-center\">\n        <Col md={6} lg={4}>\n          <Card className=\"shadow\">\n            <Card.Body className=\"p-4\">\n              <div className=\"text-center mb-4\">\n                <i className=\"fas fa-print fa-3x text-primary mb-3\"></i>\n                <h2 className=\"h4 text-gray-900 mb-4\">Welcome Back!</h2>\n              </div>\n\n              {error && (\n                <Alert variant=\"danger\" className=\"mb-3\">\n                  <i className=\"fas fa-exclamation-triangle me-2\"></i>\n                  {error}\n                </Alert>\n              )}\n\n              <Form onSubmit={handleSubmit}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>\n                    <i className=\"fas fa-envelope me-2\"></i>\n                    Email Address\n                  </Form.Label>\n                  <Form.Control\n                    type=\"email\"\n                    placeholder=\"Enter email\"\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                    required\n                    disabled={isLoading}\n                  />\n                </Form.Group>\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>\n                    <i className=\"fas fa-lock me-2\"></i>\n                    Password\n                  </Form.Label>\n                  <Form.Control\n                    type=\"password\"\n                    placeholder=\"Password\"\n                    value={password}\n                    onChange={(e) => setPassword(e.target.value)}\n                    required\n                    disabled={isLoading}\n                  />\n                </Form.Group>\n\n                <Button \n                  variant=\"primary\" \n                  type=\"submit\" \n                  className=\"w-100 mb-3\"\n                  disabled={isLoading}\n                >\n                  {isLoading ? (\n                    <>\n                      <Spinner\n                        as=\"span\"\n                        animation=\"border\"\n                        size=\"sm\"\n                        role=\"status\"\n                        aria-hidden=\"true\"\n                        className=\"me-2\"\n                      />\n                      Signing In...\n                    </>\n                  ) : (\n                    <>\n                      <i className=\"fas fa-sign-in-alt me-2\"></i>\n                      Sign In\n                    </>\n                  )}\n                </Button>\n              </Form>\n\n              <hr />\n\n              <div className=\"text-center\">\n                <Link to=\"/register\" className=\"small\">\n                  Don't have an account? Create Account!\n                </Link>\n              </div>\n\n              <div className=\"text-center mt-2\">\n                <Link to=\"/forgot-password\" className=\"small\">\n                  Forgot Password?\n                </Link>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACzF,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAGpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGpD,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM;IAAEwB,KAAK;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EACnD,MAAM;IAAEgB;EAAW,CAAC,GAAGf,QAAQ,CAAC,CAAC;EACjC,MAAMgB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAE9BV,SAAS,CAAC,MAAM;IACd,IAAI0B,IAAI,EAAE;MACRE,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACF,IAAI,EAAEE,QAAQ,CAAC,CAAC;EAEpB,MAAMC,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMC,OAAO,GAAG,MAAMT,KAAK,CAACJ,KAAK,EAAEE,QAAQ,CAAC;IAC5C,IAAIW,OAAO,EAAE;MACXJ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,oBACEd,OAAA,CAACb,SAAS;IAACgC,SAAS,EAAC,MAAM;IAAAC,QAAA,eACzBpB,OAAA,CAACZ,GAAG;MAAC+B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCpB,OAAA,CAACX,GAAG;QAACgC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eAChBpB,OAAA,CAACV,IAAI;UAAC6B,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACtBpB,OAAA,CAACV,IAAI,CAACiC,IAAI;YAACJ,SAAS,EAAC,KAAK;YAAAC,QAAA,gBACxBpB,OAAA;cAAKmB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BpB,OAAA;gBAAGmB,SAAS,EAAC;cAAsC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxD3B,OAAA;gBAAImB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,EAELhB,KAAK,iBACJX,OAAA,CAACP,KAAK;cAACmC,OAAO,EAAC,QAAQ;cAACT,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACtCpB,OAAA;gBAAGmB,SAAS,EAAC;cAAkC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACnDhB,KAAK;YAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACR,eAED3B,OAAA,CAACT,IAAI;cAACsC,QAAQ,EAAEd,YAAa;cAAAK,QAAA,gBAC3BpB,OAAA,CAACT,IAAI,CAACuC,KAAK;gBAACX,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BpB,OAAA,CAACT,IAAI,CAACwC,KAAK;kBAAAX,QAAA,gBACTpB,OAAA;oBAAGmB,SAAS,EAAC;kBAAsB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,iBAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3B,OAAA,CAACT,IAAI,CAACyC,OAAO;kBACXC,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,aAAa;kBACzBC,KAAK,EAAE9B,KAAM;kBACb+B,QAAQ,EAAGpB,CAAC,IAAKV,QAAQ,CAACU,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;kBAC1CG,QAAQ;kBACRC,QAAQ,EAAE7B;gBAAU;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEb3B,OAAA,CAACT,IAAI,CAACuC,KAAK;gBAACX,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BpB,OAAA,CAACT,IAAI,CAACwC,KAAK;kBAAAX,QAAA,gBACTpB,OAAA;oBAAGmB,SAAS,EAAC;kBAAkB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,YAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3B,OAAA,CAACT,IAAI,CAACyC,OAAO;kBACXC,IAAI,EAAC,UAAU;kBACfC,WAAW,EAAC,UAAU;kBACtBC,KAAK,EAAE5B,QAAS;kBAChB6B,QAAQ,EAAGpB,CAAC,IAAKR,WAAW,CAACQ,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;kBAC7CG,QAAQ;kBACRC,QAAQ,EAAE7B;gBAAU;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEb3B,OAAA,CAACR,MAAM;gBACLoC,OAAO,EAAC,SAAS;gBACjBK,IAAI,EAAC,QAAQ;gBACbd,SAAS,EAAC,YAAY;gBACtBoB,QAAQ,EAAE7B,SAAU;gBAAAU,QAAA,EAEnBV,SAAS,gBACRV,OAAA,CAAAE,SAAA;kBAAAkB,QAAA,gBACEpB,OAAA,CAACN,OAAO;oBACN8C,EAAE,EAAC,MAAM;oBACTC,SAAS,EAAC,QAAQ;oBAClBC,IAAI,EAAC,IAAI;oBACTC,IAAI,EAAC,QAAQ;oBACb,eAAY,MAAM;oBAClBxB,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,iBAEJ;gBAAA,eAAE,CAAC,gBAEH3B,OAAA,CAAAE,SAAA;kBAAAkB,QAAA,gBACEpB,OAAA;oBAAGmB,SAAS,EAAC;kBAAyB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,WAE7C;gBAAA,eAAE;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEP3B,OAAA;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAEN3B,OAAA;cAAKmB,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BpB,OAAA,CAACL,IAAI;gBAACiD,EAAE,EAAC,WAAW;gBAACzB,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAC;cAEvC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN3B,OAAA;cAAKmB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BpB,OAAA,CAACL,IAAI;gBAACiD,EAAE,EAAC,kBAAkB;gBAACzB,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAC;cAE9C;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACvB,EAAA,CArHID,KAAe;EAAA,QAGuBN,OAAO,EAC1BC,QAAQ,EACdF,WAAW;AAAA;AAAAiD,EAAA,GALxB1C,KAAe;AAuHrB,eAAeA,KAAK;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}