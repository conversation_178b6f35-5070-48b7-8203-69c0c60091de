{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\Layout.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useLocation } from 'react-router-dom';\nimport ProfessionalNavbar from './ProfessionalNavbar';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport '../styles/Layout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children,\n  className = ''\n}) => {\n  _s();\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  const {\n    user\n  } = useAuth();\n  const location = useLocation();\n  const backgroundVariants = {\n    light: {\n      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n      transition: {\n        duration: 0.5\n      }\n    },\n    dark: {\n      background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `layout-wrapper ${isDarkMode ? 'dark-mode' : 'light-mode'}`,\n    children: [user && /*#__PURE__*/_jsxDEV(ProfessionalNavbar, {\n      onThemeToggle: toggleTheme,\n      isDarkMode: isDarkMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(motion.main, {\n      className: `main-content ${className}`,\n      variants: backgroundVariants,\n      animate: isDarkMode ? 'dark' : 'light',\n      style: {\n        paddingTop: user ? '100px' : '0',\n        // Account for fixed navbar\n        minHeight: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20,\n            scale: 0.98\n          },\n          animate: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n          },\n          exit: {\n            opacity: 0,\n            y: -20,\n            scale: 0.98\n          },\n          transition: {\n            duration: 0.4\n          },\n          className: \"page-content\",\n          children: children\n        }, location.pathname, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"background-decorations\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"decoration decoration-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"decoration decoration-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"decoration decoration-3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"floating-action-btn\",\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      initial: {\n        opacity: 0,\n        scale: 0\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      transition: {\n        delay: 1,\n        duration: 0.3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"fab-btn\",\n        onClick: () => {\n          // Quick action based on user type\n          if (user.userType === 'Student') {\n            window.location.href = '/upload';\n          } else {\n            window.location.href = '/job-queue';\n          }\n        },\n        title: user.userType === 'Student' ? 'Quick Upload' : 'View Jobs',\n        children: user.userType === 'Student' ? '📄' : '📋'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isDarkMode !== undefined && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"theme-indicator\",\n        initial: {\n          opacity: 0,\n          x: 100\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        exit: {\n          opacity: 0,\n          x: 100\n        },\n        transition: {\n          duration: 0.3\n        },\n        children: isDarkMode ? '🌙' : '☀️'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"7ulHA6OAQHBxWQMTzJNi7B9J1QA=\", false, function () {\n  return [useTheme, useAuth, useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "useLocation", "ProfessionalNavbar", "useTheme", "useAuth", "jsxDEV", "_jsxDEV", "Layout", "children", "className", "_s", "isDarkMode", "toggleTheme", "user", "location", "backgroundVariants", "light", "background", "transition", "duration", "dark", "onThemeToggle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "main", "variants", "animate", "style", "paddingTop", "minHeight", "mode", "div", "initial", "opacity", "y", "scale", "exit", "pathname", "whileHover", "whileTap", "delay", "onClick", "userType", "window", "href", "title", "undefined", "x", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/Layout.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useLocation } from 'react-router-dom';\nimport ProfessionalNavbar from './ProfessionalNavbar';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport '../styles/Layout.css';\n\ninterface LayoutProps {\n  children: ReactNode;\n  className?: string;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children, className = '' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n  const { user } = useAuth();\n  const location = useLocation();\n\n\n\n  const backgroundVariants = {\n    light: {\n      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n      transition: { duration: 0.5 }\n    },\n    dark: {\n      background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',\n      transition: { duration: 0.5 }\n    }\n  };\n\n  return (\n    <div className={`layout-wrapper ${isDarkMode ? 'dark-mode' : 'light-mode'}`}>\n      {/* Professional Navbar */}\n      {user && (\n        <ProfessionalNavbar \n          onThemeToggle={toggleTheme} \n          isDarkMode={isDarkMode} \n        />\n      )}\n\n      {/* Main Content Area */}\n      <motion.main\n        className={`main-content ${className}`}\n        variants={backgroundVariants}\n        animate={isDarkMode ? 'dark' : 'light'}\n        style={{\n          paddingTop: user ? '100px' : '0', // Account for fixed navbar\n          minHeight: '100vh'\n        }}\n      >\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={location.pathname}\n            initial={{ opacity: 0, y: 20, scale: 0.98 }}\n            animate={{ opacity: 1, y: 0, scale: 1 }}\n            exit={{ opacity: 0, y: -20, scale: 0.98 }}\n            transition={{ duration: 0.4 }}\n            className=\"page-content\"\n          >\n            {children}\n          </motion.div>\n        </AnimatePresence>\n      </motion.main>\n\n      {/* Background Decorations */}\n      <div className=\"background-decorations\">\n        <div className=\"decoration decoration-1\"></div>\n        <div className=\"decoration decoration-2\"></div>\n        <div className=\"decoration decoration-3\"></div>\n      </div>\n\n      {/* Floating Action Button (Optional) */}\n      {user && (\n        <motion.div\n          className=\"floating-action-btn\"\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.9 }}\n          initial={{ opacity: 0, scale: 0 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ delay: 1, duration: 0.3 }}\n        >\n          <button\n            className=\"fab-btn\"\n            onClick={() => {\n              // Quick action based on user type\n              if (user.userType === 'Student') {\n                window.location.href = '/upload';\n              } else {\n                window.location.href = '/job-queue';\n              }\n            }}\n            title={user.userType === 'Student' ? 'Quick Upload' : 'View Jobs'}\n          >\n            {user.userType === 'Student' ? '📄' : '📋'}\n          </button>\n        </motion.div>\n      )}\n\n      {/* Theme Toggle Indicator */}\n      <AnimatePresence>\n        {isDarkMode !== undefined && (\n          <motion.div\n            className=\"theme-indicator\"\n            initial={{ opacity: 0, x: 100 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: 100 }}\n            transition={{ duration: 0.3 }}\n          >\n            {isDarkMode ? '🌙' : '☀️'}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO9B,MAAMC,MAA6B,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAGT,QAAQ,CAAC,CAAC;EAC9C,MAAM;IAAEU;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAMU,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAI9B,MAAMc,kBAAkB,GAAG;IACzBC,KAAK,EAAE;MACLC,UAAU,EAAE,mDAAmD;MAC/DC,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAC9B,CAAC;IACDC,IAAI,EAAE;MACJH,UAAU,EAAE,mDAAmD;MAC/DC,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;EAED,oBACEb,OAAA;IAAKG,SAAS,EAAE,kBAAkBE,UAAU,GAAG,WAAW,GAAG,YAAY,EAAG;IAAAH,QAAA,GAEzEK,IAAI,iBACHP,OAAA,CAACJ,kBAAkB;MACjBmB,aAAa,EAAET,WAAY;MAC3BD,UAAU,EAAEA;IAAW;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF,eAGDnB,OAAA,CAACP,MAAM,CAAC2B,IAAI;MACVjB,SAAS,EAAE,gBAAgBA,SAAS,EAAG;MACvCkB,QAAQ,EAAEZ,kBAAmB;MAC7Ba,OAAO,EAAEjB,UAAU,GAAG,MAAM,GAAG,OAAQ;MACvCkB,KAAK,EAAE;QACLC,UAAU,EAAEjB,IAAI,GAAG,OAAO,GAAG,GAAG;QAAE;QAClCkB,SAAS,EAAE;MACb,CAAE;MAAAvB,QAAA,eAEFF,OAAA,CAACN,eAAe;QAACgC,IAAI,EAAC,MAAM;QAAAxB,QAAA,eAC1BF,OAAA,CAACP,MAAM,CAACkC,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5CT,OAAO,EAAE;YAAEO,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UACxCC,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC1CnB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BV,SAAS,EAAC,cAAc;UAAAD,QAAA,EAEvBA;QAAQ,GAPJM,QAAQ,CAACyB,QAAQ;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGdnB,OAAA;MAAKG,SAAS,EAAC,wBAAwB;MAAAD,QAAA,gBACrCF,OAAA;QAAKG,SAAS,EAAC;MAAyB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CnB,OAAA;QAAKG,SAAS,EAAC;MAAyB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CnB,OAAA;QAAKG,SAAS,EAAC;MAAyB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,EAGLZ,IAAI,iBACHP,OAAA,CAACP,MAAM,CAACkC,GAAG;MACTxB,SAAS,EAAC,qBAAqB;MAC/B+B,UAAU,EAAE;QAAEH,KAAK,EAAE;MAAI,CAAE;MAC3BI,QAAQ,EAAE;QAAEJ,KAAK,EAAE;MAAI,CAAE;MACzBH,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEE,KAAK,EAAE;MAAE,CAAE;MAClCT,OAAO,EAAE;QAAEO,OAAO,EAAE,CAAC;QAAEE,KAAK,EAAE;MAAE,CAAE;MAClCnB,UAAU,EAAE;QAAEwB,KAAK,EAAE,CAAC;QAAEvB,QAAQ,EAAE;MAAI,CAAE;MAAAX,QAAA,eAExCF,OAAA;QACEG,SAAS,EAAC,SAAS;QACnBkC,OAAO,EAAEA,CAAA,KAAM;UACb;UACA,IAAI9B,IAAI,CAAC+B,QAAQ,KAAK,SAAS,EAAE;YAC/BC,MAAM,CAAC/B,QAAQ,CAACgC,IAAI,GAAG,SAAS;UAClC,CAAC,MAAM;YACLD,MAAM,CAAC/B,QAAQ,CAACgC,IAAI,GAAG,YAAY;UACrC;QACF,CAAE;QACFC,KAAK,EAAElC,IAAI,CAAC+B,QAAQ,KAAK,SAAS,GAAG,cAAc,GAAG,WAAY;QAAApC,QAAA,EAEjEK,IAAI,CAAC+B,QAAQ,KAAK,SAAS,GAAG,IAAI,GAAG;MAAI;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACb,eAGDnB,OAAA,CAACN,eAAe;MAAAQ,QAAA,EACbG,UAAU,KAAKqC,SAAS,iBACvB1C,OAAA,CAACP,MAAM,CAACkC,GAAG;QACTxB,SAAS,EAAC,iBAAiB;QAC3ByB,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEc,CAAC,EAAE;QAAI,CAAE;QAChCrB,OAAO,EAAE;UAAEO,OAAO,EAAE,CAAC;UAAEc,CAAC,EAAE;QAAE,CAAE;QAC9BX,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEc,CAAC,EAAE;QAAI,CAAE;QAC7B/B,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAX,QAAA,EAE7BG,UAAU,GAAG,IAAI,GAAG;MAAI;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV,CAAC;AAACf,EAAA,CAtGIH,MAA6B;EAAA,QACGJ,QAAQ,EAC3BC,OAAO,EACPH,WAAW;AAAA;AAAAiD,EAAA,GAHxB3C,MAA6B;AAwGnC,eAAeA,MAAM;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}