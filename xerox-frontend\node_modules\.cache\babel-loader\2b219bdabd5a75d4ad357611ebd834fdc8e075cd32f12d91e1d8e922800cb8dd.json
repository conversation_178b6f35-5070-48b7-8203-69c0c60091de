{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\Register.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner, Nav } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { UserPlus, GraduationCap, Store } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport Layout from './Layout';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('student');\n  const [studentData, setStudentData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    studentNumber: '',\n    firstName: '',\n    lastName: '',\n    phoneNumber: '',\n    department: '',\n    year: undefined\n  });\n  const [xeroxData, setXeroxData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    xeroxCenterName: '',\n    location: '',\n    contactPerson: '',\n    phoneNumber: '',\n    description: ''\n  });\n  const {\n    registerStudent,\n    registerXeroxCenter,\n    isLoading,\n    error,\n    user\n  } = useAuth();\n  const {\n    isDarkMode\n  } = useTheme();\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n  const handleStudentSubmit = async e => {\n    e.preventDefault();\n    const success = await registerStudent(studentData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n  const handleXeroxSubmit = async e => {\n    e.preventDefault();\n    const success = await registerXeroxCenter(xeroxData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n  const handleStudentChange = (field, value) => {\n    setStudentData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleXeroxChange = (field, value) => {\n    setXeroxData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      className: \"d-flex align-items-center justify-content-center py-5\",\n      style: {\n        minHeight: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center w-100\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              y: 0,\n              scale: 1\n            },\n            transition: {\n              duration: 0.6\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: `shadow-lg border-0 ${isDarkMode ? 'bg-dark text-light' : ''}`,\n              style: {\n                borderRadius: '20px',\n                overflow: 'hidden'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"position-relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-100 h-100 position-absolute\",\n                  style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    height: '100px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"p-4 position-relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                      initial: {\n                        scale: 0,\n                        rotate: -180\n                      },\n                      animate: {\n                        scale: 1,\n                        rotate: 0\n                      },\n                      transition: {\n                        delay: 0.3,\n                        duration: 0.5\n                      },\n                      className: \"d-inline-block p-3 rounded-circle bg-white shadow-sm mb-3\",\n                      style: {\n                        marginTop: '10px'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(UserPlus, {\n                        size: 32,\n                        className: \"text-primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 98,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 91,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"h4 mb-2\",\n                      style: {\n                        color: isDarkMode ? '#fff' : '#333',\n                        marginTop: '40px'\n                      },\n                      children: \"Create Account\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 100,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted small\",\n                      children: \"Join XeroxHub today\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 101,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Nav, {\n                    variant: \"pills\",\n                    className: \"mb-4 justify-content-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Nav.Item, {\n                      children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                        active: activeTab === 'student',\n                        onClick: () => setActiveTab('student'),\n                        className: `px-4 py-2 mx-1 ${activeTab === 'student' ? 'btn-gradient' : ''}`,\n                        style: {\n                          borderRadius: '25px',\n                          fontWeight: '500',\n                          transition: 'all 0.3s ease',\n                          background: activeTab === 'student' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                          border: activeTab === 'student' ? 'none' : '2px solid #e9ecef',\n                          color: activeTab === 'student' ? 'white' : isDarkMode ? '#fff' : '#333'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(GraduationCap, {\n                          size: 16,\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 119,\n                          columnNumber: 27\n                        }, this), \"Student\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 106,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 105,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n                      children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                        active: activeTab === 'xerox',\n                        onClick: () => setActiveTab('xerox'),\n                        className: `px-4 py-2 mx-1 ${activeTab === 'xerox' ? 'btn-gradient' : ''}`,\n                        style: {\n                          borderRadius: '25px',\n                          fontWeight: '500',\n                          transition: 'all 0.3s ease',\n                          background: activeTab === 'xerox' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                          border: activeTab === 'xerox' ? 'none' : '2px solid #e9ecef',\n                          color: activeTab === 'xerox' ? 'white' : isDarkMode ? '#fff' : '#333'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Store, {\n                          size: 16,\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 137,\n                          columnNumber: 27\n                        }, this), \"Xerox Center\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 124,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 123,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 21\n                  }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n                    variant: \"danger\",\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-exclamation-triangle me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 19\n                    }, this), error]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 17\n                  }, this), activeTab === 'student' ? /*#__PURE__*/_jsxDEV(Form, {\n                    onSubmit: handleStudentSubmit,\n                    children: [/*#__PURE__*/_jsxDEV(Row, {\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Username\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 155,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Enter username\",\n                            value: studentData.username,\n                            onChange: e => handleStudentChange('username', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 156,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 154,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 153,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Student Number\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 168,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Enter student number\",\n                            value: studentData.studentNumber,\n                            onChange: e => handleStudentChange('studentNumber', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 169,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 167,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 166,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 152,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Email Address\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 182,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"email\",\n                        placeholder: \"Enter email\",\n                        value: studentData.email,\n                        onChange: e => handleStudentChange('email', e.target.value),\n                        required: true,\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 183,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 194,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"password\",\n                        placeholder: \"Password\",\n                        value: studentData.password,\n                        onChange: e => handleStudentChange('password', e.target.value),\n                        required: true,\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 195,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Row, {\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"First Name\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 208,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"First name\",\n                            value: studentData.firstName,\n                            onChange: e => handleStudentChange('firstName', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 209,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 207,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Last Name\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 221,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Last name\",\n                            value: studentData.lastName,\n                            onChange: e => handleStudentChange('lastName', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 222,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 220,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Row, {\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Department\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 237,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Department (optional)\",\n                            value: studentData.department,\n                            onChange: e => handleStudentChange('department', e.target.value),\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 238,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 236,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Year\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 249,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"number\",\n                            placeholder: \"Year (optional)\",\n                            value: studentData.year || '',\n                            onChange: e => handleStudentChange('year', e.target.value ? parseInt(e.target.value) : undefined),\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 250,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 248,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Phone Number\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 262,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"tel\",\n                        placeholder: \"Phone number (optional)\",\n                        value: studentData.phoneNumber,\n                        onChange: e => handleStudentChange('phoneNumber', e.target.value),\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 263,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"primary\",\n                      type: \"submit\",\n                      className: \"w-100 mb-3\",\n                      disabled: isLoading,\n                      children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                          as: \"span\",\n                          animation: \"border\",\n                          size: \"sm\",\n                          role: \"status\",\n                          \"aria-hidden\": \"true\",\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 280,\n                          columnNumber: 25\n                        }, this), \"Creating Account...\"]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-user-plus me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 292,\n                          columnNumber: 25\n                        }, this), \"Register as Student\"]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 17\n                  }, this) : /*#__PURE__*/_jsxDEV(Form, {\n                    onSubmit: handleXeroxSubmit,\n                    children: [/*#__PURE__*/_jsxDEV(Row, {\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Username\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 303,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Enter username\",\n                            value: xeroxData.username,\n                            onChange: e => handleXeroxChange('username', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 304,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 302,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 301,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Xerox Center Name\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 316,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Center name\",\n                            value: xeroxData.xeroxCenterName,\n                            onChange: e => handleXeroxChange('xeroxCenterName', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 317,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 315,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 314,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Email Address\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 330,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"email\",\n                        placeholder: \"Enter email\",\n                        value: xeroxData.email,\n                        onChange: e => handleXeroxChange('email', e.target.value),\n                        required: true,\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 331,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 342,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"password\",\n                        placeholder: \"Password\",\n                        value: xeroxData.password,\n                        onChange: e => handleXeroxChange('password', e.target.value),\n                        required: true,\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 343,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Location\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"text\",\n                        placeholder: \"Center location\",\n                        value: xeroxData.location,\n                        onChange: e => handleXeroxChange('location', e.target.value),\n                        required: true,\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 355,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Row, {\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Contact Person\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 368,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Contact person (optional)\",\n                            value: xeroxData.contactPerson,\n                            onChange: e => handleXeroxChange('contactPerson', e.target.value),\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 369,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 367,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 366,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Phone Number\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 380,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"tel\",\n                            placeholder: \"Phone number\",\n                            value: xeroxData.phoneNumber,\n                            onChange: e => handleXeroxChange('phoneNumber', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 381,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 379,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 378,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Description\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 394,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        as: \"textarea\",\n                        rows: 3,\n                        placeholder: \"Center description (optional)\",\n                        value: xeroxData.description,\n                        onChange: e => handleXeroxChange('description', e.target.value),\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 395,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"primary\",\n                      type: \"submit\",\n                      className: \"w-100 mb-3\",\n                      disabled: isLoading,\n                      children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                          as: \"span\",\n                          animation: \"border\",\n                          size: \"sm\",\n                          role: \"status\",\n                          \"aria-hidden\": \"true\",\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 413,\n                          columnNumber: 25\n                        }, this), \"Creating Account...\"]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-store me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 425,\n                          columnNumber: 25\n                        }, this), \"Register as Xerox Center\"]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                    style: {\n                      margin: '2rem 0',\n                      borderColor: isDarkMode ? '#404040' : '#e9ecef'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/login\",\n                      className: \"text-decoration-none\",\n                      style: {\n                        color: '#667eea',\n                        fontWeight: '500',\n                        transition: 'all 0.3s ease'\n                      },\n                      onMouseEnter: e => e.target.style.color = '#764ba2',\n                      onMouseLeave: e => e.target.style.color = '#667eea',\n                      children: \"Already have an account? Login!\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"innhoHiNPBQ7HtBFsbfyS9Rqisk=\", false, function () {\n  return [useAuth, useTheme, useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Nav", "Link", "useNavigate", "motion", "UserPlus", "GraduationCap", "Store", "useAuth", "useTheme", "Layout", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Register", "_s", "activeTab", "setActiveTab", "studentData", "setStudentData", "username", "email", "password", "studentNumber", "firstName", "lastName", "phoneNumber", "department", "year", "undefined", "xeroxData", "setXeroxData", "xeroxCenterName", "location", "<PERSON><PERSON><PERSON>", "description", "registerStudent", "registerXeroxCenter", "isLoading", "error", "user", "isDarkMode", "navigate", "handleStudentSubmit", "e", "preventDefault", "success", "handleXeroxSubmit", "handleStudentChange", "field", "value", "prev", "handleXeroxChange", "children", "className", "style", "minHeight", "md", "lg", "div", "initial", "opacity", "y", "scale", "animate", "transition", "duration", "borderRadius", "overflow", "background", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "rotate", "delay", "marginTop", "size", "color", "variant", "<PERSON><PERSON>", "active", "onClick", "fontWeight", "border", "onSubmit", "Group", "Label", "Control", "type", "placeholder", "onChange", "target", "required", "disabled", "parseInt", "as", "animation", "role", "rows", "margin", "borderColor", "to", "onMouseEnter", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/Register.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, But<PERSON>, Al<PERSON>, Spinner, Nav } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { UserPlus, GraduationCap, Store } from 'lucide-react';\nimport { useAuth, StudentRegistrationData, XeroxCenterRegistrationData } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport Layout from './Layout';\n\nconst Register: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<'student' | 'xerox'>('student');\n  const [studentData, setStudentData] = useState<StudentRegistrationData>({\n    username: '',\n    email: '',\n    password: '',\n    studentNumber: '',\n    firstName: '',\n    lastName: '',\n    phoneNumber: '',\n    department: '',\n    year: undefined\n  });\n  const [xeroxData, setXeroxData] = useState<XeroxCenterRegistrationData>({\n    username: '',\n    email: '',\n    password: '',\n    xeroxCenterName: '',\n    location: '',\n    contactPerson: '',\n    phoneNumber: '',\n    description: ''\n  });\n\n  const { registerStudent, registerXeroxCenter, isLoading, error, user } = useAuth();\n  const { isDarkMode } = useTheme();\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n\n  const handleStudentSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    const success = await registerStudent(studentData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n\n  const handleXeroxSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    const success = await registerXeroxCenter(xeroxData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n\n  const handleStudentChange = (field: keyof StudentRegistrationData, value: string | number | undefined) => {\n    setStudentData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleXeroxChange = (field: keyof XeroxCenterRegistrationData, value: string) => {\n    setXeroxData(prev => ({ ...prev, [field]: value }));\n  };\n\n\n\n  return (\n    <Layout>\n      <Container className=\"d-flex align-items-center justify-content-center py-5\" style={{ minHeight: '100vh' }}>\n        <Row className=\"justify-content-center w-100\">\n          <Col md={8} lg={6}>\n            <motion.div\n              initial={{ opacity: 0, y: 50, scale: 0.9 }}\n              animate={{ opacity: 1, y: 0, scale: 1 }}\n              transition={{ duration: 0.6 }}\n            >\n              <Card className={`shadow-lg border-0 ${isDarkMode ? 'bg-dark text-light' : ''}`} style={{ borderRadius: '20px', overflow: 'hidden' }}>\n                <div className=\"position-relative\">\n                  <div\n                    className=\"w-100 h-100 position-absolute\"\n                    style={{\n                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                      height: '100px'\n                    }}\n                  />\n                  <Card.Body className=\"p-4 position-relative\">\n                    <div className=\"text-center mb-4\">\n                      <motion.div\n                        initial={{ scale: 0, rotate: -180 }}\n                        animate={{ scale: 1, rotate: 0 }}\n                        transition={{ delay: 0.3, duration: 0.5 }}\n                        className=\"d-inline-block p-3 rounded-circle bg-white shadow-sm mb-3\"\n                        style={{ marginTop: '10px' }}\n                      >\n                        <UserPlus size={32} className=\"text-primary\" />\n                      </motion.div>\n                      <h2 className=\"h4 mb-2\" style={{ color: isDarkMode ? '#fff' : '#333', marginTop: '40px' }}>Create Account</h2>\n                      <p className=\"text-muted small\">Join XeroxHub today</p>\n                    </div>\n\n                    <Nav variant=\"pills\" className=\"mb-4 justify-content-center\">\n                      <Nav.Item>\n                        <Nav.Link\n                          active={activeTab === 'student'}\n                          onClick={() => setActiveTab('student')}\n                          className={`px-4 py-2 mx-1 ${activeTab === 'student' ? 'btn-gradient' : ''}`}\n                          style={{\n                            borderRadius: '25px',\n                            fontWeight: '500',\n                            transition: 'all 0.3s ease',\n                            background: activeTab === 'student' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                            border: activeTab === 'student' ? 'none' : '2px solid #e9ecef',\n                            color: activeTab === 'student' ? 'white' : (isDarkMode ? '#fff' : '#333')\n                          }}\n                        >\n                          <GraduationCap size={16} className=\"me-2\" />\n                          Student\n                        </Nav.Link>\n                      </Nav.Item>\n                      <Nav.Item>\n                        <Nav.Link\n                          active={activeTab === 'xerox'}\n                          onClick={() => setActiveTab('xerox')}\n                          className={`px-4 py-2 mx-1 ${activeTab === 'xerox' ? 'btn-gradient' : ''}`}\n                          style={{\n                            borderRadius: '25px',\n                            fontWeight: '500',\n                            transition: 'all 0.3s ease',\n                            background: activeTab === 'xerox' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                            border: activeTab === 'xerox' ? 'none' : '2px solid #e9ecef',\n                            color: activeTab === 'xerox' ? 'white' : (isDarkMode ? '#fff' : '#333')\n                          }}\n                        >\n                          <Store size={16} className=\"me-2\" />\n                          Xerox Center\n                        </Nav.Link>\n                      </Nav.Item>\n                    </Nav>\n\n              {error && (\n                <Alert variant=\"danger\" className=\"mb-3\">\n                  <i className=\"fas fa-exclamation-triangle me-2\"></i>\n                  {error}\n                </Alert>\n              )}\n\n              {activeTab === 'student' ? (\n                <Form onSubmit={handleStudentSubmit}>\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Username</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Enter username\"\n                          value={studentData.username}\n                          onChange={(e) => handleStudentChange('username', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Student Number</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Enter student number\"\n                          value={studentData.studentNumber}\n                          onChange={(e) => handleStudentChange('studentNumber', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Email Address</Form.Label>\n                    <Form.Control\n                      type=\"email\"\n                      placeholder=\"Enter email\"\n                      value={studentData.email}\n                      onChange={(e) => handleStudentChange('email', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Password</Form.Label>\n                    <Form.Control\n                      type=\"password\"\n                      placeholder=\"Password\"\n                      value={studentData.password}\n                      onChange={(e) => handleStudentChange('password', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>First Name</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"First name\"\n                          value={studentData.firstName}\n                          onChange={(e) => handleStudentChange('firstName', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Last Name</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Last name\"\n                          value={studentData.lastName}\n                          onChange={(e) => handleStudentChange('lastName', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Department</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Department (optional)\"\n                          value={studentData.department}\n                          onChange={(e) => handleStudentChange('department', e.target.value)}\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Year</Form.Label>\n                        <Form.Control\n                          type=\"number\"\n                          placeholder=\"Year (optional)\"\n                          value={studentData.year || ''}\n                          onChange={(e) => handleStudentChange('year', e.target.value ? parseInt(e.target.value) : undefined)}\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Phone Number</Form.Label>\n                    <Form.Control\n                      type=\"tel\"\n                      placeholder=\"Phone number (optional)\"\n                      value={studentData.phoneNumber}\n                      onChange={(e) => handleStudentChange('phoneNumber', e.target.value)}\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Button \n                    variant=\"primary\" \n                    type=\"submit\" \n                    className=\"w-100 mb-3\"\n                    disabled={isLoading}\n                  >\n                    {isLoading ? (\n                      <>\n                        <Spinner\n                          as=\"span\"\n                          animation=\"border\"\n                          size=\"sm\"\n                          role=\"status\"\n                          aria-hidden=\"true\"\n                          className=\"me-2\"\n                        />\n                        Creating Account...\n                      </>\n                    ) : (\n                      <>\n                        <i className=\"fas fa-user-plus me-2\"></i>\n                        Register as Student\n                      </>\n                    )}\n                  </Button>\n                </Form>\n              ) : (\n                <Form onSubmit={handleXeroxSubmit}>\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Username</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Enter username\"\n                          value={xeroxData.username}\n                          onChange={(e) => handleXeroxChange('username', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Xerox Center Name</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Center name\"\n                          value={xeroxData.xeroxCenterName}\n                          onChange={(e) => handleXeroxChange('xeroxCenterName', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Email Address</Form.Label>\n                    <Form.Control\n                      type=\"email\"\n                      placeholder=\"Enter email\"\n                      value={xeroxData.email}\n                      onChange={(e) => handleXeroxChange('email', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Password</Form.Label>\n                    <Form.Control\n                      type=\"password\"\n                      placeholder=\"Password\"\n                      value={xeroxData.password}\n                      onChange={(e) => handleXeroxChange('password', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Location</Form.Label>\n                    <Form.Control\n                      type=\"text\"\n                      placeholder=\"Center location\"\n                      value={xeroxData.location}\n                      onChange={(e) => handleXeroxChange('location', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Contact Person</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Contact person (optional)\"\n                          value={xeroxData.contactPerson}\n                          onChange={(e) => handleXeroxChange('contactPerson', e.target.value)}\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Phone Number</Form.Label>\n                        <Form.Control\n                          type=\"tel\"\n                          placeholder=\"Phone number\"\n                          value={xeroxData.phoneNumber}\n                          onChange={(e) => handleXeroxChange('phoneNumber', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Description</Form.Label>\n                    <Form.Control\n                      as=\"textarea\"\n                      rows={3}\n                      placeholder=\"Center description (optional)\"\n                      value={xeroxData.description}\n                      onChange={(e) => handleXeroxChange('description', e.target.value)}\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Button \n                    variant=\"primary\" \n                    type=\"submit\" \n                    className=\"w-100 mb-3\"\n                    disabled={isLoading}\n                  >\n                    {isLoading ? (\n                      <>\n                        <Spinner\n                          as=\"span\"\n                          animation=\"border\"\n                          size=\"sm\"\n                          role=\"status\"\n                          aria-hidden=\"true\"\n                          className=\"me-2\"\n                        />\n                        Creating Account...\n                      </>\n                    ) : (\n                      <>\n                        <i className=\"fas fa-store me-2\"></i>\n                        Register as Xerox Center\n                      </>\n                    )}\n                  </Button>\n                </Form>\n              )}\n\n                    <hr style={{ margin: '2rem 0', borderColor: isDarkMode ? '#404040' : '#e9ecef' }} />\n\n                    <div className=\"text-center\">\n                      <Link\n                        to=\"/login\"\n                        className=\"text-decoration-none\"\n                        style={{\n                          color: '#667eea',\n                          fontWeight: '500',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#764ba2'}\n                        onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#667eea'}\n                      >\n                        Already have an account? Login!\n                      </Link>\n                    </div>\n                  </Card.Body>\n                </div>\n              </Card>\n            </motion.div>\n          </Col>\n        </Row>\n      </Container>\n    </Layout>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,QAAQ,iBAAiB;AAC9F,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,EAAEC,aAAa,EAAEC,KAAK,QAAQ,cAAc;AAC7D,SAASC,OAAO,QAA8D,yBAAyB;AACvG,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAsB,SAAS,CAAC;EAC1E,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAA0B;IACtE8B,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAEC;EACR,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAA8B;IACtE8B,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZU,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBR,WAAW,EAAE,EAAE;IACfS,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM;IAAEC,eAAe;IAAEC,mBAAmB;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGjC,OAAO,CAAC,CAAC;EAClF,MAAM;IAAEkC;EAAW,CAAC,GAAGjC,QAAQ,CAAC,CAAC;EACjC,MAAMkC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAE9BX,SAAS,CAAC,MAAM;IACd,IAAIiD,IAAI,EAAE;MACRE,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACF,IAAI,EAAEE,QAAQ,CAAC,CAAC;EAEpB,MAAMC,mBAAmB,GAAG,MAAOC,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,OAAO,GAAG,MAAMV,eAAe,CAAClB,WAAW,CAAC;IAClD,IAAI4B,OAAO,EAAE;MACXJ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAG,MAAOH,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,OAAO,GAAG,MAAMT,mBAAmB,CAACP,SAAS,CAAC;IACpD,IAAIgB,OAAO,EAAE;MACXJ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMM,mBAAmB,GAAGA,CAACC,KAAoC,EAAEC,KAAkC,KAAK;IACxG/B,cAAc,CAACgC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACvD,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAACH,KAAwC,EAAEC,KAAa,KAAK;IACrFnB,YAAY,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAID,oBACEvC,OAAA,CAACF,MAAM;IAAA4C,QAAA,eACL1C,OAAA,CAACnB,SAAS;MAAC8D,SAAS,EAAC,uDAAuD;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAAH,QAAA,eACzG1C,OAAA,CAAClB,GAAG;QAAC6D,SAAS,EAAC,8BAA8B;QAAAD,QAAA,eAC3C1C,OAAA,CAACjB,GAAG;UAAC+D,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAL,QAAA,eAChB1C,OAAA,CAACR,MAAM,CAACwD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3CC,OAAO,EAAE;cAAEH,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAE,CAAE;YACxCE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,eAE9B1C,OAAA,CAAChB,IAAI;cAAC2D,SAAS,EAAE,sBAAsBb,UAAU,GAAG,oBAAoB,GAAG,EAAE,EAAG;cAACc,KAAK,EAAE;gBAAEY,YAAY,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAS,CAAE;cAAAf,QAAA,eACnI1C,OAAA;gBAAK2C,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC1C,OAAA;kBACE2C,SAAS,EAAC,+BAA+B;kBACzCC,KAAK,EAAE;oBACLc,UAAU,EAAE,mDAAmD;oBAC/DC,MAAM,EAAE;kBACV;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF/D,OAAA,CAAChB,IAAI,CAACgF,IAAI;kBAACrB,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,gBAC1C1C,OAAA;oBAAK2C,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC/B1C,OAAA,CAACR,MAAM,CAACwD,GAAG;sBACTC,OAAO,EAAE;wBAAEG,KAAK,EAAE,CAAC;wBAAEa,MAAM,EAAE,CAAC;sBAAI,CAAE;sBACpCZ,OAAO,EAAE;wBAAED,KAAK,EAAE,CAAC;wBAAEa,MAAM,EAAE;sBAAE,CAAE;sBACjCX,UAAU,EAAE;wBAAEY,KAAK,EAAE,GAAG;wBAAEX,QAAQ,EAAE;sBAAI,CAAE;sBAC1CZ,SAAS,EAAC,2DAA2D;sBACrEC,KAAK,EAAE;wBAAEuB,SAAS,EAAE;sBAAO,CAAE;sBAAAzB,QAAA,eAE7B1C,OAAA,CAACP,QAAQ;wBAAC2E,IAAI,EAAE,EAAG;wBAACzB,SAAS,EAAC;sBAAc;wBAAAiB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC,eACb/D,OAAA;sBAAI2C,SAAS,EAAC,SAAS;sBAACC,KAAK,EAAE;wBAAEyB,KAAK,EAAEvC,UAAU,GAAG,MAAM,GAAG,MAAM;wBAAEqC,SAAS,EAAE;sBAAO,CAAE;sBAAAzB,QAAA,EAAC;oBAAc;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9G/D,OAAA;sBAAG2C,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAAmB;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eAEN/D,OAAA,CAACX,GAAG;oBAACiF,OAAO,EAAC,OAAO;oBAAC3B,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1D1C,OAAA,CAACX,GAAG,CAACkF,IAAI;sBAAA7B,QAAA,eACP1C,OAAA,CAACX,GAAG,CAACC,IAAI;wBACPkF,MAAM,EAAEnE,SAAS,KAAK,SAAU;wBAChCoE,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC,SAAS,CAAE;wBACvCqC,SAAS,EAAE,kBAAkBtC,SAAS,KAAK,SAAS,GAAG,cAAc,GAAG,EAAE,EAAG;wBAC7EuC,KAAK,EAAE;0BACLY,YAAY,EAAE,MAAM;0BACpBkB,UAAU,EAAE,KAAK;0BACjBpB,UAAU,EAAE,eAAe;0BAC3BI,UAAU,EAAErD,SAAS,KAAK,SAAS,GAAG,mDAAmD,GAAG,aAAa;0BACzGsE,MAAM,EAAEtE,SAAS,KAAK,SAAS,GAAG,MAAM,GAAG,mBAAmB;0BAC9DgE,KAAK,EAAEhE,SAAS,KAAK,SAAS,GAAG,OAAO,GAAIyB,UAAU,GAAG,MAAM,GAAG;wBACpE,CAAE;wBAAAY,QAAA,gBAEF1C,OAAA,CAACN,aAAa;0BAAC0E,IAAI,EAAE,EAAG;0BAACzB,SAAS,EAAC;wBAAM;0BAAAiB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,WAE9C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAU;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACX/D,OAAA,CAACX,GAAG,CAACkF,IAAI;sBAAA7B,QAAA,eACP1C,OAAA,CAACX,GAAG,CAACC,IAAI;wBACPkF,MAAM,EAAEnE,SAAS,KAAK,OAAQ;wBAC9BoE,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC,OAAO,CAAE;wBACrCqC,SAAS,EAAE,kBAAkBtC,SAAS,KAAK,OAAO,GAAG,cAAc,GAAG,EAAE,EAAG;wBAC3EuC,KAAK,EAAE;0BACLY,YAAY,EAAE,MAAM;0BACpBkB,UAAU,EAAE,KAAK;0BACjBpB,UAAU,EAAE,eAAe;0BAC3BI,UAAU,EAAErD,SAAS,KAAK,OAAO,GAAG,mDAAmD,GAAG,aAAa;0BACvGsE,MAAM,EAAEtE,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,mBAAmB;0BAC5DgE,KAAK,EAAEhE,SAAS,KAAK,OAAO,GAAG,OAAO,GAAIyB,UAAU,GAAG,MAAM,GAAG;wBAClE,CAAE;wBAAAY,QAAA,gBAEF1C,OAAA,CAACL,KAAK;0BAACyE,IAAI,EAAE,EAAG;0BAACzB,SAAS,EAAC;wBAAM;0BAAAiB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAEtC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAU;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,EAEXnC,KAAK,iBACJ5B,OAAA,CAACb,KAAK;oBAACmF,OAAO,EAAC,QAAQ;oBAAC3B,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACtC1C,OAAA;sBAAG2C,SAAS,EAAC;oBAAkC;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACnDnC,KAAK;kBAAA;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CACR,EAEA1D,SAAS,KAAK,SAAS,gBACtBL,OAAA,CAACf,IAAI;oBAAC2F,QAAQ,EAAE5C,mBAAoB;oBAAAU,QAAA,gBAClC1C,OAAA,CAAClB,GAAG;sBAAA4D,QAAA,gBACF1C,OAAA,CAACjB,GAAG;wBAAC+D,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACT1C,OAAA,CAACf,IAAI,CAAC4F,KAAK;0BAAClC,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;4BAAApC,QAAA,EAAC;0BAAQ;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACjC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,gBAAgB;4BAC5B1C,KAAK,EAAEhC,WAAW,CAACE,QAAS;4BAC5ByE,QAAQ,EAAGjD,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;4BACjE6C,QAAQ;4BACRC,QAAQ,EAAE1D;0BAAU;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACN/D,OAAA,CAACjB,GAAG;wBAAC+D,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACT1C,OAAA,CAACf,IAAI,CAAC4F,KAAK;0BAAClC,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;4BAAApC,QAAA,EAAC;0BAAc;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACvC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,sBAAsB;4BAClC1C,KAAK,EAAEhC,WAAW,CAACK,aAAc;4BACjCsE,QAAQ,EAAGjD,CAAC,IAAKI,mBAAmB,CAAC,eAAe,EAAEJ,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;4BACtE6C,QAAQ;4BACRC,QAAQ,EAAE1D;0BAAU;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN/D,OAAA,CAACf,IAAI,CAAC4F,KAAK;sBAAClC,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;wBAAApC,QAAA,EAAC;sBAAa;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACtC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;wBACXC,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,aAAa;wBACzB1C,KAAK,EAAEhC,WAAW,CAACG,KAAM;wBACzBwE,QAAQ,EAAGjD,CAAC,IAAKI,mBAAmB,CAAC,OAAO,EAAEJ,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;wBAC9D6C,QAAQ;wBACRC,QAAQ,EAAE1D;sBAAU;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEb/D,OAAA,CAACf,IAAI,CAAC4F,KAAK;sBAAClC,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;wBAAApC,QAAA,EAAC;sBAAQ;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;wBACXC,IAAI,EAAC,UAAU;wBACfC,WAAW,EAAC,UAAU;wBACtB1C,KAAK,EAAEhC,WAAW,CAACI,QAAS;wBAC5BuE,QAAQ,EAAGjD,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;wBACjE6C,QAAQ;wBACRC,QAAQ,EAAE1D;sBAAU;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEb/D,OAAA,CAAClB,GAAG;sBAAA4D,QAAA,gBACF1C,OAAA,CAACjB,GAAG;wBAAC+D,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACT1C,OAAA,CAACf,IAAI,CAAC4F,KAAK;0BAAClC,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;4BAAApC,QAAA,EAAC;0BAAU;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACnC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,YAAY;4BACxB1C,KAAK,EAAEhC,WAAW,CAACM,SAAU;4BAC7BqE,QAAQ,EAAGjD,CAAC,IAAKI,mBAAmB,CAAC,WAAW,EAAEJ,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;4BAClE6C,QAAQ;4BACRC,QAAQ,EAAE1D;0BAAU;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACN/D,OAAA,CAACjB,GAAG;wBAAC+D,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACT1C,OAAA,CAACf,IAAI,CAAC4F,KAAK;0BAAClC,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;4BAAApC,QAAA,EAAC;0BAAS;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eAClC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,WAAW;4BACvB1C,KAAK,EAAEhC,WAAW,CAACO,QAAS;4BAC5BoE,QAAQ,EAAGjD,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;4BACjE6C,QAAQ;4BACRC,QAAQ,EAAE1D;0BAAU;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN/D,OAAA,CAAClB,GAAG;sBAAA4D,QAAA,gBACF1C,OAAA,CAACjB,GAAG;wBAAC+D,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACT1C,OAAA,CAACf,IAAI,CAAC4F,KAAK;0BAAClC,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;4BAAApC,QAAA,EAAC;0BAAU;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACnC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,uBAAuB;4BACnC1C,KAAK,EAAEhC,WAAW,CAACS,UAAW;4BAC9BkE,QAAQ,EAAGjD,CAAC,IAAKI,mBAAmB,CAAC,YAAY,EAAEJ,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;4BACnE8C,QAAQ,EAAE1D;0BAAU;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACN/D,OAAA,CAACjB,GAAG;wBAAC+D,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACT1C,OAAA,CAACf,IAAI,CAAC4F,KAAK;0BAAClC,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;4BAAApC,QAAA,EAAC;0BAAI;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eAC7B/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;4BACXC,IAAI,EAAC,QAAQ;4BACbC,WAAW,EAAC,iBAAiB;4BAC7B1C,KAAK,EAAEhC,WAAW,CAACU,IAAI,IAAI,EAAG;4BAC9BiE,QAAQ,EAAGjD,CAAC,IAAKI,mBAAmB,CAAC,MAAM,EAAEJ,CAAC,CAACkD,MAAM,CAAC5C,KAAK,GAAG+C,QAAQ,CAACrD,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAC,GAAGrB,SAAS,CAAE;4BACpGmE,QAAQ,EAAE1D;0BAAU;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN/D,OAAA,CAACf,IAAI,CAAC4F,KAAK;sBAAClC,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;wBAAApC,QAAA,EAAC;sBAAY;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACrC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;wBACXC,IAAI,EAAC,KAAK;wBACVC,WAAW,EAAC,yBAAyB;wBACrC1C,KAAK,EAAEhC,WAAW,CAACQ,WAAY;wBAC/BmE,QAAQ,EAAGjD,CAAC,IAAKI,mBAAmB,CAAC,aAAa,EAAEJ,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;wBACpE8C,QAAQ,EAAE1D;sBAAU;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEb/D,OAAA,CAACd,MAAM;sBACLoF,OAAO,EAAC,SAAS;sBACjBU,IAAI,EAAC,QAAQ;sBACbrC,SAAS,EAAC,YAAY;sBACtB0C,QAAQ,EAAE1D,SAAU;sBAAAe,QAAA,EAEnBf,SAAS,gBACR3B,OAAA,CAAAE,SAAA;wBAAAwC,QAAA,gBACE1C,OAAA,CAACZ,OAAO;0BACNmG,EAAE,EAAC,MAAM;0BACTC,SAAS,EAAC,QAAQ;0BAClBpB,IAAI,EAAC,IAAI;0BACTqB,IAAI,EAAC,QAAQ;0BACb,eAAY,MAAM;0BAClB9C,SAAS,EAAC;wBAAM;0BAAAiB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,uBAEJ;sBAAA,eAAE,CAAC,gBAEH/D,OAAA,CAAAE,SAAA;wBAAAwC,QAAA,gBACE1C,OAAA;0BAAG2C,SAAS,EAAC;wBAAuB;0BAAAiB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,uBAE3C;sBAAA,eAAE;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAEP/D,OAAA,CAACf,IAAI;oBAAC2F,QAAQ,EAAExC,iBAAkB;oBAAAM,QAAA,gBAChC1C,OAAA,CAAClB,GAAG;sBAAA4D,QAAA,gBACF1C,OAAA,CAACjB,GAAG;wBAAC+D,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACT1C,OAAA,CAACf,IAAI,CAAC4F,KAAK;0BAAClC,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;4BAAApC,QAAA,EAAC;0BAAQ;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACjC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,gBAAgB;4BAC5B1C,KAAK,EAAEpB,SAAS,CAACV,QAAS;4BAC1ByE,QAAQ,EAAGjD,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;4BAC/D6C,QAAQ;4BACRC,QAAQ,EAAE1D;0BAAU;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACN/D,OAAA,CAACjB,GAAG;wBAAC+D,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACT1C,OAAA,CAACf,IAAI,CAAC4F,KAAK;0BAAClC,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;4BAAApC,QAAA,EAAC;0BAAiB;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eAC1C/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,aAAa;4BACzB1C,KAAK,EAAEpB,SAAS,CAACE,eAAgB;4BACjC6D,QAAQ,EAAGjD,CAAC,IAAKQ,iBAAiB,CAAC,iBAAiB,EAAER,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;4BACtE6C,QAAQ;4BACRC,QAAQ,EAAE1D;0BAAU;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN/D,OAAA,CAACf,IAAI,CAAC4F,KAAK;sBAAClC,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;wBAAApC,QAAA,EAAC;sBAAa;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACtC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;wBACXC,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,aAAa;wBACzB1C,KAAK,EAAEpB,SAAS,CAACT,KAAM;wBACvBwE,QAAQ,EAAGjD,CAAC,IAAKQ,iBAAiB,CAAC,OAAO,EAAER,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;wBAC5D6C,QAAQ;wBACRC,QAAQ,EAAE1D;sBAAU;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEb/D,OAAA,CAACf,IAAI,CAAC4F,KAAK;sBAAClC,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;wBAAApC,QAAA,EAAC;sBAAQ;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;wBACXC,IAAI,EAAC,UAAU;wBACfC,WAAW,EAAC,UAAU;wBACtB1C,KAAK,EAAEpB,SAAS,CAACR,QAAS;wBAC1BuE,QAAQ,EAAGjD,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;wBAC/D6C,QAAQ;wBACRC,QAAQ,EAAE1D;sBAAU;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEb/D,OAAA,CAACf,IAAI,CAAC4F,KAAK;sBAAClC,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;wBAAApC,QAAA,EAAC;sBAAQ;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;wBACXC,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,iBAAiB;wBAC7B1C,KAAK,EAAEpB,SAAS,CAACG,QAAS;wBAC1B4D,QAAQ,EAAGjD,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;wBAC/D6C,QAAQ;wBACRC,QAAQ,EAAE1D;sBAAU;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEb/D,OAAA,CAAClB,GAAG;sBAAA4D,QAAA,gBACF1C,OAAA,CAACjB,GAAG;wBAAC+D,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACT1C,OAAA,CAACf,IAAI,CAAC4F,KAAK;0BAAClC,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;4BAAApC,QAAA,EAAC;0BAAc;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACvC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,2BAA2B;4BACvC1C,KAAK,EAAEpB,SAAS,CAACI,aAAc;4BAC/B2D,QAAQ,EAAGjD,CAAC,IAAKQ,iBAAiB,CAAC,eAAe,EAAER,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;4BACpE8C,QAAQ,EAAE1D;0BAAU;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACN/D,OAAA,CAACjB,GAAG;wBAAC+D,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACT1C,OAAA,CAACf,IAAI,CAAC4F,KAAK;0BAAClC,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;4BAAApC,QAAA,EAAC;0BAAY;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACrC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;4BACXC,IAAI,EAAC,KAAK;4BACVC,WAAW,EAAC,cAAc;4BAC1B1C,KAAK,EAAEpB,SAAS,CAACJ,WAAY;4BAC7BmE,QAAQ,EAAGjD,CAAC,IAAKQ,iBAAiB,CAAC,aAAa,EAAER,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;4BAClE6C,QAAQ;4BACRC,QAAQ,EAAE1D;0BAAU;4BAAAiC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN/D,OAAA,CAACf,IAAI,CAAC4F,KAAK;sBAAClC,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1B1C,OAAA,CAACf,IAAI,CAAC6F,KAAK;wBAAApC,QAAA,EAAC;sBAAW;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACpC/D,OAAA,CAACf,IAAI,CAAC8F,OAAO;wBACXQ,EAAE,EAAC,UAAU;wBACbG,IAAI,EAAE,CAAE;wBACRT,WAAW,EAAC,+BAA+B;wBAC3C1C,KAAK,EAAEpB,SAAS,CAACK,WAAY;wBAC7B0D,QAAQ,EAAGjD,CAAC,IAAKQ,iBAAiB,CAAC,aAAa,EAAER,CAAC,CAACkD,MAAM,CAAC5C,KAAK,CAAE;wBAClE8C,QAAQ,EAAE1D;sBAAU;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEb/D,OAAA,CAACd,MAAM;sBACLoF,OAAO,EAAC,SAAS;sBACjBU,IAAI,EAAC,QAAQ;sBACbrC,SAAS,EAAC,YAAY;sBACtB0C,QAAQ,EAAE1D,SAAU;sBAAAe,QAAA,EAEnBf,SAAS,gBACR3B,OAAA,CAAAE,SAAA;wBAAAwC,QAAA,gBACE1C,OAAA,CAACZ,OAAO;0BACNmG,EAAE,EAAC,MAAM;0BACTC,SAAS,EAAC,QAAQ;0BAClBpB,IAAI,EAAC,IAAI;0BACTqB,IAAI,EAAC,QAAQ;0BACb,eAAY,MAAM;0BAClB9C,SAAS,EAAC;wBAAM;0BAAAiB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,uBAEJ;sBAAA,eAAE,CAAC,gBAEH/D,OAAA,CAAAE,SAAA;wBAAAwC,QAAA,gBACE1C,OAAA;0BAAG2C,SAAS,EAAC;wBAAmB;0BAAAiB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,4BAEvC;sBAAA,eAAE;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACP,eAEK/D,OAAA;oBAAI4C,KAAK,EAAE;sBAAE+C,MAAM,EAAE,QAAQ;sBAAEC,WAAW,EAAE9D,UAAU,GAAG,SAAS,GAAG;oBAAU;kBAAE;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEpF/D,OAAA;oBAAK2C,SAAS,EAAC,aAAa;oBAAAD,QAAA,eAC1B1C,OAAA,CAACV,IAAI;sBACHuG,EAAE,EAAC,QAAQ;sBACXlD,SAAS,EAAC,sBAAsB;sBAChCC,KAAK,EAAE;wBACLyB,KAAK,EAAE,SAAS;wBAChBK,UAAU,EAAE,KAAK;wBACjBpB,UAAU,EAAE;sBACd,CAAE;sBACFwC,YAAY,EAAG7D,CAAC,IAAMA,CAAC,CAACkD,MAAM,CAAiBvC,KAAK,CAACyB,KAAK,GAAG,SAAU;sBACvE0B,YAAY,EAAG9D,CAAC,IAAMA,CAAC,CAACkD,MAAM,CAAiBvC,KAAK,CAACyB,KAAK,GAAG,SAAU;sBAAA3B,QAAA,EACxE;oBAED;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEb,CAAC;AAAC3D,EAAA,CAjcID,QAAkB;EAAA,QAwBmDP,OAAO,EACzDC,QAAQ,EACdN,WAAW;AAAA;AAAAyG,EAAA,GA1BxB7F,QAAkB;AAmcxB,eAAeA,QAAQ;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}