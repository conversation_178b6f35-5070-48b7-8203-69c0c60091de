{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\AceternityLogin.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Mail, Lock, LogIn, Printer, Eye, EyeOff } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport AceternityLayout from './AceternityLayout';\nimport { cn } from '../../lib/utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AceternityLogin = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const {\n    login,\n    isLoading,\n    error,\n    user\n  } = useAuth();\n  const {\n    isDarkMode\n  } = useTheme();\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const success = await login(email, password);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AceternityLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          y: 0,\n          scale: 1\n        },\n        transition: {\n          duration: 0.5\n        },\n        className: \"max-w-md w-full space-y-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: cn(\"relative rounded-3xl p-8 backdrop-blur-xl border shadow-2xl\", isDarkMode ? \"bg-black/20 border-white/10 shadow-black/20\" : \"bg-white/20 border-white/30 shadow-black/10\"),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: -20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.2,\n                duration: 0.5\n              },\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                whileHover: {\n                  rotate: 5,\n                  scale: 1.05\n                },\n                className: \"mx-auto w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-6 shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(Printer, {\n                  className: \"w-8 h-8 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                children: \"Welcome Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Sign in to your XeroxHub account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              className: \"mt-6 p-4 rounded-2xl bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 83,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: error\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.form, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.3,\n                duration: 0.5\n              },\n              onSubmit: handleSubmit,\n              className: \"mt-8 space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                  children: \"Email Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                    children: /*#__PURE__*/_jsxDEV(Mail, {\n                      className: \"h-5 w-5 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 105,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    required: true,\n                    value: email,\n                    onChange: e => setEmail(e.target.value),\n                    disabled: isLoading,\n                    className: cn(\"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                    placeholder: \"Enter your email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                  children: \"Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                    children: /*#__PURE__*/_jsxDEV(Lock, {\n                      className: \"h-5 w-5 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: showPassword ? \"text\" : \"password\",\n                    required: true,\n                    value: password,\n                    onChange: e => setPassword(e.target.value),\n                    disabled: isLoading,\n                    className: cn(\"block w-full pl-12 pr-12 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                    placeholder: \"Enter your password\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: () => setShowPassword(!showPassword),\n                    className: \"absolute inset-y-0 right-0 pr-4 flex items-center\",\n                    children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                      className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                type: \"submit\",\n                disabled: isLoading,\n                className: cn(\"group relative w-full flex justify-center py-3 px-4 rounded-2xl text-sm font-medium text-white\", \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\", \"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\", \"disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\", \"shadow-lg hover:shadow-xl\"),\n                children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Signing In...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(LogIn, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Sign In\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/register\",\n                  className: \"text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\",\n                  children: \"Create an account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/forgot-password\",\n                  className: \"text-gray-600 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 transition-colors\",\n                  children: \"Forgot password?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(AceternityLogin, \"kdYPbnG4FMl7jnoBzyzFeyPybQs=\", false, function () {\n  return [useAuth, useTheme, useNavigate];\n});\n_c = AceternityLogin;\nexport default AceternityLogin;\nvar _c;\n$RefreshReg$(_c, \"AceternityLogin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "motion", "Mail", "Lock", "LogIn", "Printer", "Eye", "Eye<PERSON>ff", "useAuth", "useTheme", "AceternityLayout", "cn", "jsxDEV", "_jsxDEV", "AceternityLogin", "_s", "email", "setEmail", "password", "setPassword", "showPassword", "setShowPassword", "login", "isLoading", "error", "user", "isDarkMode", "navigate", "handleSubmit", "e", "preventDefault", "success", "children", "className", "div", "initial", "opacity", "y", "scale", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "whileHover", "rotate", "x", "fill", "viewBox", "fillRule", "d", "clipRule", "form", "onSubmit", "type", "required", "value", "onChange", "target", "disabled", "placeholder", "onClick", "button", "whileTap", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/AceternityLogin.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Mail, Lock, LogIn, Printer, Eye, EyeOff } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport AceternityLayout from './AceternityLayout';\nimport { cn } from '../../lib/utils';\n\nconst AceternityLogin: React.FC = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const { login, isLoading, error, user } = useAuth();\n  const { isDarkMode } = useTheme();\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    const success = await login(email, password);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n\n  return (\n    <AceternityLayout>\n      <div className=\"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20, scale: 0.95 }}\n          animate={{ opacity: 1, y: 0, scale: 1 }}\n          transition={{ duration: 0.5 }}\n          className=\"max-w-md w-full space-y-8\"\n        >\n          {/* Card Container */}\n          <div className={cn(\n            \"relative rounded-3xl p-8 backdrop-blur-xl border shadow-2xl\",\n            isDarkMode \n              ? \"bg-black/20 border-white/10 shadow-black/20\" \n              : \"bg-white/20 border-white/30 shadow-black/10\"\n          )}>\n            {/* Animated Background Gradient */}\n            <div className=\"absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-pulse\"></div>\n            \n            {/* Content */}\n            <div className=\"relative z-10\">\n              {/* Logo and Header */}\n              <motion.div\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.2, duration: 0.5 }}\n                className=\"text-center\"\n              >\n                <motion.div\n                  whileHover={{ rotate: 5, scale: 1.05 }}\n                  className=\"mx-auto w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-6 shadow-lg\"\n                >\n                  <Printer className=\"w-8 h-8 text-white\" />\n                </motion.div>\n                <h2 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                  Welcome Back\n                </h2>\n                <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n                  Sign in to your XeroxHub account\n                </p>\n              </motion.div>\n\n              {/* Error Alert */}\n              {error && (\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  className=\"mt-6 p-4 rounded-2xl bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400\"\n                >\n                  <div className=\"flex items-center space-x-2\">\n                    <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span className=\"text-sm\">{error}</span>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* Form */}\n              <motion.form\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3, duration: 0.5 }}\n                onSubmit={handleSubmit}\n                className=\"mt-8 space-y-6\"\n              >\n                {/* Email Field */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Email Address\n                  </label>\n                  <div className=\"relative\">\n                    <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                      <Mail className=\"h-5 w-5 text-gray-400\" />\n                    </div>\n                    <input\n                      type=\"email\"\n                      required\n                      value={email}\n                      onChange={(e) => setEmail(e.target.value)}\n                      disabled={isLoading}\n                      className={cn(\n                        \"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                        \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                        \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                        \"ring-gray-300 dark:ring-gray-600\",\n                        \"text-gray-900 dark:text-white\"\n                      )}\n                      placeholder=\"Enter your email\"\n                    />\n                  </div>\n                </div>\n\n                {/* Password Field */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Password\n                  </label>\n                  <div className=\"relative\">\n                    <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                      <Lock className=\"h-5 w-5 text-gray-400\" />\n                    </div>\n                    <input\n                      type={showPassword ? \"text\" : \"password\"}\n                      required\n                      value={password}\n                      onChange={(e) => setPassword(e.target.value)}\n                      disabled={isLoading}\n                      className={cn(\n                        \"block w-full pl-12 pr-12 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                        \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                        \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                        \"ring-gray-300 dark:ring-gray-600\",\n                        \"text-gray-900 dark:text-white\"\n                      )}\n                      placeholder=\"Enter your password\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowPassword(!showPassword)}\n                      className=\"absolute inset-y-0 right-0 pr-4 flex items-center\"\n                    >\n                      {showPassword ? (\n                        <EyeOff className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\n                      ) : (\n                        <Eye className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\n                      )}\n                    </button>\n                  </div>\n                </div>\n\n                {/* Submit Button */}\n                <motion.button\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  type=\"submit\"\n                  disabled={isLoading}\n                  className={cn(\n                    \"group relative w-full flex justify-center py-3 px-4 rounded-2xl text-sm font-medium text-white\",\n                    \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\",\n                    \"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                    \"disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\",\n                    \"shadow-lg hover:shadow-xl\"\n                  )}\n                >\n                  {isLoading ? (\n                    <div className=\"flex items-center space-x-2\">\n                      <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n                      <span>Signing In...</span>\n                    </div>\n                  ) : (\n                    <div className=\"flex items-center space-x-2\">\n                      <LogIn className=\"w-5 h-5\" />\n                      <span>Sign In</span>\n                    </div>\n                  )}\n                </motion.button>\n\n                {/* Links */}\n                <div className=\"flex items-center justify-between text-sm\">\n                  <Link\n                    to=\"/register\"\n                    className=\"text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\"\n                  >\n                    Create an account\n                  </Link>\n                  <Link\n                    to=\"/forgot-password\"\n                    className=\"text-gray-600 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 transition-colors\"\n                  >\n                    Forgot password?\n                  </Link>\n                </div>\n              </motion.form>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </AceternityLayout>\n  );\n};\n\nexport default AceternityLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,QAAQ,cAAc;AACtE,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM;IAAEyB,KAAK;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGjB,OAAO,CAAC,CAAC;EACnD,MAAM;IAAEkB;EAAW,CAAC,GAAGjB,QAAQ,CAAC,CAAC;EACjC,MAAMkB,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,IAAI2B,IAAI,EAAE;MACRE,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACF,IAAI,EAAEE,QAAQ,CAAC,CAAC;EAEpB,MAAMC,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,OAAO,GAAG,MAAMT,KAAK,CAACN,KAAK,EAAEE,QAAQ,CAAC;IAC5C,IAAIa,OAAO,EAAE;MACXJ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,oBACEd,OAAA,CAACH,gBAAgB;IAAAsB,QAAA,eACfnB,OAAA;MAAKoB,SAAS,EAAC,0EAA0E;MAAAD,QAAA,eACvFnB,OAAA,CAACZ,MAAM,CAACiC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5CC,OAAO,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QACxCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eAGrCnB,OAAA;UAAKoB,SAAS,EAAEtB,EAAE,CAChB,6DAA6D,EAC7De,UAAU,GACN,6CAA6C,GAC7C,6CACN,CAAE;UAAAM,QAAA,gBAEAnB,OAAA;YAAKoB,SAAS,EAAC;UAA+G;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGrIhC,OAAA;YAAKoB,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAE5BnB,OAAA,CAACZ,MAAM,CAACiC,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCE,OAAO,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BG,UAAU,EAAE;gBAAEM,KAAK,EAAE,GAAG;gBAAEL,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAEvBnB,OAAA,CAACZ,MAAM,CAACiC,GAAG;gBACTa,UAAU,EAAE;kBAAEC,MAAM,EAAE,CAAC;kBAAEV,KAAK,EAAE;gBAAK,CAAE;gBACvCL,SAAS,EAAC,6HAA6H;gBAAAD,QAAA,eAEvInB,OAAA,CAACR,OAAO;kBAAC4B,SAAS,EAAC;gBAAoB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACbhC,OAAA;gBAAIoB,SAAS,EAAC,+FAA+F;gBAAAD,QAAA,EAAC;cAE9G;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhC,OAAA;gBAAGoB,SAAS,EAAC,+CAA+C;gBAAAD,QAAA,EAAC;cAE7D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EAGZrB,KAAK,iBACJX,OAAA,CAACZ,MAAM,CAACiC,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCV,OAAO,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE;cAAE,CAAE;cAC9BhB,SAAS,EAAC,4FAA4F;cAAAD,QAAA,eAEtGnB,OAAA;gBAAKoB,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAC1CnB,OAAA;kBAAKoB,SAAS,EAAC,SAAS;kBAACiB,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAnB,QAAA,eAC9DnB,OAAA;oBAAMuC,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,mHAAmH;oBAACC,QAAQ,EAAC;kBAAS;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjK,CAAC,eACNhC,OAAA;kBAAMoB,SAAS,EAAC,SAAS;kBAAAD,QAAA,EAAER;gBAAK;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGDhC,OAAA,CAACZ,MAAM,CAACsD,IAAI;cACVpB,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BG,UAAU,EAAE;gBAAEM,KAAK,EAAE,GAAG;gBAAEL,QAAQ,EAAE;cAAI,CAAE;cAC1Ce,QAAQ,EAAE5B,YAAa;cACvBK,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAG1BnB,OAAA;gBAAAmB,QAAA,gBACEnB,OAAA;kBAAOoB,SAAS,EAAC,iEAAiE;kBAAAD,QAAA,EAAC;gBAEnF;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRhC,OAAA;kBAAKoB,SAAS,EAAC,UAAU;kBAAAD,QAAA,gBACvBnB,OAAA;oBAAKoB,SAAS,EAAC,sEAAsE;oBAAAD,QAAA,eACnFnB,OAAA,CAACX,IAAI;sBAAC+B,SAAS,EAAC;oBAAuB;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACNhC,OAAA;oBACE4C,IAAI,EAAC,OAAO;oBACZC,QAAQ;oBACRC,KAAK,EAAE3C,KAAM;oBACb4C,QAAQ,EAAG/B,CAAC,IAAKZ,QAAQ,CAACY,CAAC,CAACgC,MAAM,CAACF,KAAK,CAAE;oBAC1CG,QAAQ,EAAEvC,SAAU;oBACpBU,SAAS,EAAEtB,EAAE,CACX,iGAAiG,EACjG,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;oBACFoD,WAAW,EAAC;kBAAkB;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNhC,OAAA;gBAAAmB,QAAA,gBACEnB,OAAA;kBAAOoB,SAAS,EAAC,iEAAiE;kBAAAD,QAAA,EAAC;gBAEnF;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRhC,OAAA;kBAAKoB,SAAS,EAAC,UAAU;kBAAAD,QAAA,gBACvBnB,OAAA;oBAAKoB,SAAS,EAAC,sEAAsE;oBAAAD,QAAA,eACnFnB,OAAA,CAACV,IAAI;sBAAC8B,SAAS,EAAC;oBAAuB;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACNhC,OAAA;oBACE4C,IAAI,EAAErC,YAAY,GAAG,MAAM,GAAG,UAAW;oBACzCsC,QAAQ;oBACRC,KAAK,EAAEzC,QAAS;oBAChB0C,QAAQ,EAAG/B,CAAC,IAAKV,WAAW,CAACU,CAAC,CAACgC,MAAM,CAACF,KAAK,CAAE;oBAC7CG,QAAQ,EAAEvC,SAAU;oBACpBU,SAAS,EAAEtB,EAAE,CACX,kGAAkG,EAClG,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;oBACFoD,WAAW,EAAC;kBAAqB;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,eACFhC,OAAA;oBACE4C,IAAI,EAAC,QAAQ;oBACbO,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAAC,CAACD,YAAY,CAAE;oBAC9Ca,SAAS,EAAC,mDAAmD;oBAAAD,QAAA,EAE5DZ,YAAY,gBACXP,OAAA,CAACN,MAAM;sBAAC0B,SAAS,EAAC;oBAA2C;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEhEhC,OAAA,CAACP,GAAG;sBAAC2B,SAAS,EAAC;oBAA2C;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAC7D;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNhC,OAAA,CAACZ,MAAM,CAACgE,MAAM;gBACZlB,UAAU,EAAE;kBAAET,KAAK,EAAE;gBAAK,CAAE;gBAC5B4B,QAAQ,EAAE;kBAAE5B,KAAK,EAAE;gBAAK,CAAE;gBAC1BmB,IAAI,EAAC,QAAQ;gBACbK,QAAQ,EAAEvC,SAAU;gBACpBU,SAAS,EAAEtB,EAAE,CACX,gGAAgG,EAChG,sFAAsF,EACtF,yEAAyE,EACzE,6EAA6E,EAC7E,2BACF,CAAE;gBAAAqB,QAAA,EAEDT,SAAS,gBACRV,OAAA;kBAAKoB,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CnB,OAAA;oBAAKoB,SAAS,EAAC;kBAA2E;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjGhC,OAAA;oBAAAmB,QAAA,EAAM;kBAAa;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,gBAENhC,OAAA;kBAAKoB,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CnB,OAAA,CAACT,KAAK;oBAAC6B,SAAS,EAAC;kBAAS;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7BhC,OAAA;oBAAAmB,QAAA,EAAM;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACY,CAAC,eAGhBhC,OAAA;gBAAKoB,SAAS,EAAC,2CAA2C;gBAAAD,QAAA,gBACxDnB,OAAA,CAACd,IAAI;kBACHoE,EAAE,EAAC,WAAW;kBACdlC,SAAS,EAAC,iGAAiG;kBAAAD,QAAA,EAC5G;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPhC,OAAA,CAACd,IAAI;kBACHoE,EAAE,EAAC,kBAAkB;kBACrBlC,SAAS,EAAC,iGAAiG;kBAAAD,QAAA,EAC5G;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAEvB,CAAC;AAAC9B,EAAA,CA1MID,eAAyB;EAAA,QAIaN,OAAO,EAC1BC,QAAQ,EACdT,WAAW;AAAA;AAAAoE,EAAA,GANxBtD,eAAyB;AA4M/B,eAAeA,eAAe;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}