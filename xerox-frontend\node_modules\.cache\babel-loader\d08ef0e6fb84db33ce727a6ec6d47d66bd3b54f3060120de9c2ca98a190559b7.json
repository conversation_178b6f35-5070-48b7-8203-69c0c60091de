{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\ProfessionalXeroxDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Badge, Form, InputGroup } from 'react-bootstrap';\nimport { motion } from 'framer-motion';\nimport { FileText, Clock, Cog, DollarSign, Download, Eye, Play, CheckCircle, Truck, X, Search, Grid3X3, List, Activity, RefreshCw, TrendingUp } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { printJobApi, fileUploadApi } from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfessionalXeroxDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [jobs, setJobs] = useState([]);\n  const [filteredJobs, setFilteredJobs] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('All');\n  const [viewMode, setViewMode] = useState('cards');\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [showJobDetailsModal, setShowJobDetailsModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [quoteAmount, setQuoteAmount] = useState('');\n  const [estimatedTime, setEstimatedTime] = useState('');\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const [stats, setStats] = useState({\n    total: 0,\n    pending: 0,\n    inProgress: 0,\n    completed: 0,\n    revenue: 0\n  });\n  useEffect(() => {\n    fetchJobs();\n    const interval = setInterval(fetchJobs, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  useEffect(() => {\n    filterJobs();\n  }, [jobs, searchTerm, statusFilter]);\n  const fetchJobs = async () => {\n    setIsRefreshing(true);\n    try {\n      const response = await printJobApi.getXeroxCenterJobs();\n      setJobs(response.data);\n      const totalJobs = response.data.length;\n      const pendingJobs = response.data.filter(job => ['Requested', 'UnderReview'].includes(job.status)).length;\n      const inProgressJobs = response.data.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length;\n      const completedJobs = response.data.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n      const totalRevenue = response.data.reduce((sum, job) => sum + (job.cost || 0), 0);\n      setStats({\n        total: totalJobs,\n        pending: pendingJobs,\n        inProgress: inProgressJobs,\n        completed: completedJobs,\n        revenue: totalRevenue\n      });\n    } catch (error) {\n      console.error('Error fetching jobs:', error);\n      setJobs([]);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n  const filterJobs = () => {\n    let filtered = jobs.filter(job => {\n      const matchesSearch = searchTerm === '' || job.jobNumber.toLowerCase().includes(searchTerm.toLowerCase()) || job.fileName.toLowerCase().includes(searchTerm.toLowerCase()) || job.studentName.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesStatus = statusFilter === 'All' || job.status === statusFilter;\n      return matchesSearch && matchesStatus;\n    });\n    setFilteredJobs(filtered);\n  };\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': 'secondary',\n      'UnderReview': 'info',\n      'Quoted': 'warning',\n      'WaitingConfirmation': 'warning',\n      'Confirmed': 'info',\n      'InProgress': 'primary',\n      'Completed': 'success',\n      'Delivered': 'success',\n      'Rejected': 'danger',\n      'Cancelled': 'secondary'\n    };\n    const variant = statusConfig[status] || 'secondary';\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: variant,\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this);\n  };\n  const getPriorityBadge = priority => {\n    const priorityConfig = {\n      'Low': 'success',\n      'Normal': 'secondary',\n      'High': 'warning',\n      'Urgent': 'danger'\n    };\n    const variant = priorityConfig[priority] || 'secondary';\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: variant,\n      className: \"me-2\",\n      children: priority\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this);\n  };\n  const handleStatusUpdate = async (jobId, newStatus) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n      fetchJobs();\n    } catch (error) {\n      console.error('Error updating job status:', error);\n    }\n  };\n  const handleDownloadFile = async (jobId, fileName) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n    }\n  };\n  const handleSubmitQuote = async () => {\n    if (!selectedJob || !quoteAmount) return;\n    try {\n      await printJobApi.submitQuote(selectedJob.id, {\n        amount: parseFloat(quoteAmount),\n        estimatedCompletionTime: estimatedTime\n      });\n      setShowQuoteModal(false);\n      setQuoteAmount('');\n      setEstimatedTime('');\n      setSelectedJob(null);\n      fetchJobs();\n    } catch (error) {\n      console.error('Error submitting quote:', error);\n    }\n  };\n  const getTimeAgo = dateString => {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n    return `${Math.floor(diffInMinutes / 1440)}d ago`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    style: {\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      minHeight: '100vh',\n      paddingTop: '2rem'\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      className: \"py-4\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"display-4 fw-bold text-white mb-3\",\n          children: \"Xerox Center Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"lead text-white-50 mb-4\",\n          children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"fw-semibold text-white\",\n            children: user === null || user === void 0 ? void 0 : user.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 27\n          }, this), \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center justify-content-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"light\",\n              onClick: fetchJobs,\n              disabled: isRefreshing,\n              className: \"px-4 py-2\",\n              style: {\n                borderRadius: '50px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: `me-2 ${isRefreshing ? 'spin' : ''}`,\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), \"Refresh\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n            bg: \"light\",\n            text: \"dark\",\n            className: \"px-3 py-2\",\n            children: [\"Last updated: \", new Date().toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-5 g-4\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            whileHover: {\n              y: -5,\n              scale: 1.02\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"h-100 border-0 shadow-lg\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.95)',\n                backdropFilter: 'blur(10px)',\n                borderRadius: '20px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"text-center p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-inline-flex align-items-center justify-content-center\",\n                    style: {\n                      width: '60px',\n                      height: '60px',\n                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                      borderRadius: '15px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FileText, {\n                      className: \"text-white\",\n                      size: 24\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"text-muted mb-1\",\n                  children: \"Total Jobs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"fw-bold text-primary\",\n                  children: stats.total\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-success\",\n                  children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                    size: 12,\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 21\n                  }, this), \"+12% vs last month\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.1\n            },\n            whileHover: {\n              y: -5,\n              scale: 1.02\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"h-100 border-0 shadow-lg\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.95)',\n                backdropFilter: 'blur(10px)',\n                borderRadius: '20px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"text-center p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-inline-flex align-items-center justify-content-center\",\n                    style: {\n                      width: '60px',\n                      height: '60px',\n                      background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                      borderRadius: '15px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Clock, {\n                      className: \"text-white\",\n                      size: 24\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"text-muted mb-1\",\n                  children: \"Pending Jobs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"fw-bold text-warning\",\n                  children: stats.pending\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-danger\",\n                  children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                    size: 12,\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 21\n                  }, this), \"-5% vs last month\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.2\n            },\n            whileHover: {\n              y: -5,\n              scale: 1.02\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"h-100 border-0 shadow-lg\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.95)',\n                backdropFilter: 'blur(10px)',\n                borderRadius: '20px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"text-center p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-inline-flex align-items-center justify-content-center\",\n                    style: {\n                      width: '60px',\n                      height: '60px',\n                      background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                      borderRadius: '15px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Cog, {\n                      className: \"text-white\",\n                      size: 24\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"text-muted mb-1\",\n                  children: \"In Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"fw-bold text-info\",\n                  children: stats.inProgress\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-success\",\n                  children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                    size: 12,\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 21\n                  }, this), \"+8% vs last month\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.3\n            },\n            whileHover: {\n              y: -5,\n              scale: 1.02\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"h-100 border-0 shadow-lg\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.95)',\n                backdropFilter: 'blur(10px)',\n                borderRadius: '20px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"text-center p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-inline-flex align-items-center justify-content-center\",\n                    style: {\n                      width: '60px',\n                      height: '60px',\n                      background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n                      borderRadius: '15px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(DollarSign, {\n                      className: \"text-white\",\n                      size: 24\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"text-muted mb-1\",\n                  children: \"Revenue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"fw-bold text-success\",\n                  children: [\"$\", stats.revenue.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-success\",\n                  children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                    size: 12,\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 21\n                  }, this), \"+15% vs last month\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"border-0 shadow-lg\",\n        style: {\n          background: 'rgba(255, 255, 255, 0.95)',\n          backdropFilter: 'blur(10px)',\n          borderRadius: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          className: \"border-0 bg-transparent\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center justify-content-between mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"fw-bold mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(Activity, {\n                className: \"me-2\",\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), \"Job Queue\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(Badge, {\n                bg: \"primary\",\n                className: \"px-3 py-2\",\n                children: [filteredJobs.length, \" jobs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex rounded-pill bg-light p-1\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: viewMode === 'cards' ? 'primary' : 'light',\n                  size: \"sm\",\n                  onClick: () => setViewMode('cards'),\n                  className: \"rounded-pill px-3\",\n                  children: /*#__PURE__*/_jsxDEV(Grid3X3, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: viewMode === 'table' ? 'primary' : 'light',\n                  size: \"sm\",\n                  onClick: () => setViewMode('table'),\n                  className: \"rounded-pill px-3\",\n                  children: /*#__PURE__*/_jsxDEV(List, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"g-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(InputGroup, {\n                children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                  children: /*#__PURE__*/_jsxDEV(Search, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  placeholder: \"Search jobs...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  style: {\n                    borderRadius: '0 10px 10px 0'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: statusFilter,\n                onChange: e => setStatusFilter(e.target.value),\n                style: {\n                  borderRadius: '10px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"All\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Requested\",\n                  children: \"Requested\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"UnderReview\",\n                  children: \"Under Review\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Quoted\",\n                  children: \"Quoted\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Confirmed\",\n                  children: \"Confirmed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"InProgress\",\n                  children: \"In Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Completed\",\n                  children: \"Completed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Delivered\",\n                  children: \"Delivered\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: filteredJobs.length > 0 ? /*#__PURE__*/_jsxDEV(Row, {\n            className: \"g-4\",\n            children: filteredJobs.map((job, index) => /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              lg: 4,\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: index * 0.05\n                },\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: `h-100 border-0 shadow-sm ${job.priority === 'Urgent' ? 'border-danger' : ''}`,\n                  style: {\n                    borderRadius: '15px',\n                    background: job.priority === 'Urgent' ? 'rgba(220, 53, 69, 0.05)' : 'white'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                    className: \"border-0 bg-transparent\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-start justify-content-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"fw-bold mb-1\",\n                          children: job.jobNumber\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 484,\n                          columnNumber: 31\n                        }, this), getPriorityBadge(job.priority)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 483,\n                        columnNumber: 29\n                      }, this), getStatusBadge(job.status)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"pt-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"fw-semibold mb-1\",\n                        children: job.studentName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 493,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-muted small mb-0\",\n                        children: job.studentEmail\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 494,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center text-muted small mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(FileText, {\n                          size: 14,\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 499,\n                          columnNumber: 31\n                        }, this), job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 498,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"small text-muted\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Type:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 503,\n                            columnNumber: 36\n                          }, this), \" \", job.printType]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 503,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Copies:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 504,\n                            columnNumber: 36\n                          }, this), \" \", job.copies]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 504,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Color:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 505,\n                            columnNumber: 36\n                          }, this), \" \", job.colorType]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 505,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 502,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 27\n                    }, this), job.cost && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"h5 fw-bold text-success\",\n                        children: [\"$\", job.cost.toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 511,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small text-muted mb-3\",\n                      children: getTimeAgo(job.created)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 517,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex flex-wrap gap-1\",\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-primary\",\n                        size: \"sm\",\n                        onClick: () => handleDownloadFile(job.id, job.fileName),\n                        style: {\n                          borderRadius: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Download, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 528,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 522,\n                        columnNumber: 29\n                      }, this), job.status === 'Requested' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-success\",\n                          size: \"sm\",\n                          onClick: () => {\n                            setSelectedJob(job);\n                            setShowQuoteModal(true);\n                          },\n                          style: {\n                            borderRadius: '8px'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(DollarSign, {\n                            size: 12\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 542,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 533,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-danger\",\n                          size: \"sm\",\n                          onClick: () => handleStatusUpdate(job.id, 'Rejected'),\n                          style: {\n                            borderRadius: '8px'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(X, {\n                            size: 12\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 550,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 544,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true), job.status === 'Confirmed' && /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-primary\",\n                        size: \"sm\",\n                        onClick: () => handleStatusUpdate(job.id, 'InProgress'),\n                        style: {\n                          borderRadius: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Play, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 562,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 556,\n                        columnNumber: 31\n                      }, this), job.status === 'InProgress' && /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-success\",\n                        size: \"sm\",\n                        onClick: () => handleStatusUpdate(job.id, 'Completed'),\n                        style: {\n                          borderRadius: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 573,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 567,\n                        columnNumber: 31\n                      }, this), job.status === 'Completed' && /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-info\",\n                        size: \"sm\",\n                        onClick: () => handleStatusUpdate(job.id, 'Delivered'),\n                        style: {\n                          borderRadius: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Truck, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 584,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 578,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-secondary\",\n                        size: \"sm\",\n                        onClick: () => {\n                          setSelectedJob(job);\n                          setShowJobDetailsModal(true);\n                        },\n                        style: {\n                          borderRadius: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Eye, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 597,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 588,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 521,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 21\n              }, this)\n            }, job.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            className: \"text-center py-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-inline-flex align-items-center justify-content-center\",\n                style: {\n                  width: '80px',\n                  height: '80px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '20px'\n                },\n                children: /*#__PURE__*/_jsxDEV(FileText, {\n                  className: \"text-white\",\n                  size: 40\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-semibold mb-2\",\n              children: \"No jobs found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"No jobs match your current filter criteria.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfessionalXeroxDashboard, \"JR/Ek/491EBZLHxCC/AwRi0ESGg=\", false, function () {\n  return [useAuth];\n});\n_c = ProfessionalXeroxDashboard;\nexport default ProfessionalXeroxDashboard;\nvar _c;\n$RefreshReg$(_c, \"ProfessionalXeroxDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Badge", "Form", "InputGroup", "motion", "FileText", "Clock", "Cog", "DollarSign", "Download", "Eye", "Play", "CheckCircle", "Truck", "X", "Search", "Grid3X3", "List", "Activity", "RefreshCw", "TrendingUp", "useAuth", "printJobApi", "fileUploadApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfessionalXeroxDashboard", "_s", "user", "jobs", "setJobs", "filteredJobs", "setFilteredJobs", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "viewMode", "setViewMode", "showQuoteModal", "setShowQuoteModal", "showJobDetailsModal", "setShowJobDetailsModal", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "quoteAmount", "setQuoteAmount", "estimatedTime", "setEstimatedTime", "isRefreshing", "setIsRefreshing", "stats", "setStats", "total", "pending", "inProgress", "completed", "revenue", "fetchJobs", "interval", "setInterval", "clearInterval", "filterJobs", "response", "getXeroxCenterJobs", "data", "totalJobs", "length", "pendingJobs", "filter", "job", "includes", "status", "inProgressJobs", "completedJobs", "totalRevenue", "reduce", "sum", "cost", "error", "console", "filtered", "matchesSearch", "jobNumber", "toLowerCase", "fileName", "studentName", "matchesStatus", "getStatusBadge", "statusConfig", "variant", "bg", "children", "_jsxFileName", "lineNumber", "columnNumber", "getPriorityBadge", "priority", "priorityConfig", "className", "handleStatusUpdate", "jobId", "newStatus", "updateJobStatus", "handleDownloadFile", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleSubmitQuote", "submitQuote", "id", "amount", "parseFloat", "estimatedCompletionTime", "getTimeAgo", "dateString", "now", "Date", "date", "diffInMinutes", "Math", "floor", "getTime", "style", "background", "minHeight", "paddingTop", "fluid", "div", "initial", "opacity", "y", "animate", "transition", "duration", "username", "whileHover", "scale", "whileTap", "onClick", "disabled", "borderRadius", "size", "text", "toLocaleTimeString", "md", "<PERSON><PERSON>ilter", "Body", "width", "height", "delay", "toFixed", "Header", "Text", "Control", "placeholder", "value", "onChange", "e", "target", "Select", "map", "index", "lg", "studentEmail", "slice", "printType", "copies", "colorType", "created", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/ProfessionalXeroxDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Badge, Form, Modal, InputGroup } from 'react-bootstrap';\nimport { motion } from 'framer-motion';\nimport { \n  FileText, \n  Clock, \n  Cog, \n  DollarSign, \n  Download, \n  MessageCircle, \n  Eye, \n  Play, \n  CheckCircle, \n  Truck, \n  X, \n  Search,\n  Grid3X3,\n  List,\n  Activity,\n  RefreshCw,\n  TrendingUp\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../../services/api';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  fileSize?: number;\n  status: string;\n  priority: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  studentName: string;\n  studentEmail: string;\n  printType: string;\n  copies: number;\n  colorType: string;\n  paperSize: string;\n  remarks?: string;\n  created: string;\n}\n\nconst ProfessionalXeroxDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [jobs, setJobs] = useState<PrintJob[]>([]);\n  const [filteredJobs, setFilteredJobs] = useState<PrintJob[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('All');\n  const [viewMode, setViewMode] = useState<'table' | 'cards'>('cards');\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [showJobDetailsModal, setShowJobDetailsModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState<PrintJob | null>(null);\n  const [quoteAmount, setQuoteAmount] = useState('');\n  const [estimatedTime, setEstimatedTime] = useState('');\n  const [isRefreshing, setIsRefreshing] = useState(false);\n\n  const [stats, setStats] = useState({\n    total: 0,\n    pending: 0,\n    inProgress: 0,\n    completed: 0,\n    revenue: 0\n  });\n\n  useEffect(() => {\n    fetchJobs();\n    const interval = setInterval(fetchJobs, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  useEffect(() => {\n    filterJobs();\n  }, [jobs, searchTerm, statusFilter]);\n\n  const fetchJobs = async () => {\n    setIsRefreshing(true);\n    try {\n      const response = await printJobApi.getXeroxCenterJobs();\n      setJobs(response.data);\n      \n      const totalJobs = response.data.length;\n      const pendingJobs = response.data.filter((job: PrintJob) => ['Requested', 'UnderReview'].includes(job.status)).length;\n      const inProgressJobs = response.data.filter((job: PrintJob) => ['Confirmed', 'InProgress'].includes(job.status)).length;\n      const completedJobs = response.data.filter((job: PrintJob) => ['Completed', 'Delivered'].includes(job.status)).length;\n      const totalRevenue = response.data.reduce((sum: number, job: PrintJob) => sum + (job.cost || 0), 0);\n\n      setStats({\n        total: totalJobs,\n        pending: pendingJobs,\n        inProgress: inProgressJobs,\n        completed: completedJobs,\n        revenue: totalRevenue\n      });\n    } catch (error) {\n      console.error('Error fetching jobs:', error);\n      setJobs([]);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  const filterJobs = () => {\n    let filtered = jobs.filter(job => {\n      const matchesSearch = searchTerm === '' || \n        job.jobNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        job.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        job.studentName.toLowerCase().includes(searchTerm.toLowerCase());\n      \n      const matchesStatus = statusFilter === 'All' || job.status === statusFilter;\n      \n      return matchesSearch && matchesStatus;\n    });\n\n    setFilteredJobs(filtered);\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': 'secondary',\n      'UnderReview': 'info',\n      'Quoted': 'warning',\n      'WaitingConfirmation': 'warning',\n      'Confirmed': 'info',\n      'InProgress': 'primary',\n      'Completed': 'success',\n      'Delivered': 'success',\n      'Rejected': 'danger',\n      'Cancelled': 'secondary'\n    };\n\n    const variant = statusConfig[status as keyof typeof statusConfig] || 'secondary';\n    \n    return (\n      <Badge bg={variant}>\n        {status}\n      </Badge>\n    );\n  };\n\n  const getPriorityBadge = (priority: string) => {\n    const priorityConfig = {\n      'Low': 'success',\n      'Normal': 'secondary',\n      'High': 'warning',\n      'Urgent': 'danger'\n    };\n\n    const variant = priorityConfig[priority as keyof typeof priorityConfig] || 'secondary';\n    \n    return (\n      <Badge bg={variant} className=\"me-2\">\n        {priority}\n      </Badge>\n    );\n  };\n\n  const handleStatusUpdate = async (jobId: number, newStatus: string) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n      fetchJobs();\n    } catch (error) {\n      console.error('Error updating job status:', error);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n    }\n  };\n\n  const handleSubmitQuote = async () => {\n    if (!selectedJob || !quoteAmount) return;\n\n    try {\n      await printJobApi.submitQuote(selectedJob.id, {\n        amount: parseFloat(quoteAmount),\n        estimatedCompletionTime: estimatedTime\n      });\n\n      setShowQuoteModal(false);\n      setQuoteAmount('');\n      setEstimatedTime('');\n      setSelectedJob(null);\n      fetchJobs();\n    } catch (error) {\n      console.error('Error submitting quote:', error);\n    }\n  };\n\n  const getTimeAgo = (dateString: string) => {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    \n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n    return `${Math.floor(diffInMinutes / 1440)}d ago`;\n  };\n\n  return (\n    <div className=\"min-h-screen\" style={{ \n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      minHeight: '100vh',\n      paddingTop: '2rem'\n    }}>\n      <Container fluid className=\"py-4\">\n        {/* Header Section */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-5\"\n        >\n          <h1 className=\"display-4 fw-bold text-white mb-3\">\n            Xerox Center Dashboard\n          </h1>\n          <p className=\"lead text-white-50 mb-4\">\n            Welcome back, <span className=\"fw-semibold text-white\">{user?.username}</span>! \n          </p>\n          \n          <div className=\"d-flex align-items-center justify-content-center gap-3\">\n            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n              <Button\n                variant=\"light\"\n                onClick={fetchJobs}\n                disabled={isRefreshing}\n                className=\"px-4 py-2\"\n                style={{ borderRadius: '50px' }}\n              >\n                <RefreshCw className={`me-2 ${isRefreshing ? 'spin' : ''}`} size={16} />\n                Refresh\n              </Button>\n            </motion.div>\n            \n            <Badge bg=\"light\" text=\"dark\" className=\"px-3 py-2\">\n              Last updated: {new Date().toLocaleTimeString()}\n            </Badge>\n          </div>\n        </motion.div>\n\n        {/* Statistics Cards */}\n        <Row className=\"mb-5 g-4\">\n          <Col md={3}>\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5 }}\n              whileHover={{ y: -5, scale: 1.02 }}\n            >\n              <Card className=\"h-100 border-0 shadow-lg\" style={{ \n                background: 'rgba(255, 255, 255, 0.95)',\n                backdropFilter: 'blur(10px)',\n                borderRadius: '20px'\n              }}>\n                <Card.Body className=\"text-center p-4\">\n                  <div className=\"mb-3\">\n                    <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                      width: '60px',\n                      height: '60px',\n                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                      borderRadius: '15px'\n                    }}>\n                      <FileText className=\"text-white\" size={24} />\n                    </div>\n                  </div>\n                  <h6 className=\"text-muted mb-1\">Total Jobs</h6>\n                  <h2 className=\"fw-bold text-primary\">{stats.total}</h2>\n                  <small className=\"text-success\">\n                    <TrendingUp size={12} className=\"me-1\" />\n                    +12% vs last month\n                  </small>\n                </Card.Body>\n              </Card>\n            </motion.div>\n          </Col>\n          \n          <Col md={3}>\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n              whileHover={{ y: -5, scale: 1.02 }}\n            >\n              <Card className=\"h-100 border-0 shadow-lg\" style={{ \n                background: 'rgba(255, 255, 255, 0.95)',\n                backdropFilter: 'blur(10px)',\n                borderRadius: '20px'\n              }}>\n                <Card.Body className=\"text-center p-4\">\n                  <div className=\"mb-3\">\n                    <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                      width: '60px',\n                      height: '60px',\n                      background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                      borderRadius: '15px'\n                    }}>\n                      <Clock className=\"text-white\" size={24} />\n                    </div>\n                  </div>\n                  <h6 className=\"text-muted mb-1\">Pending Jobs</h6>\n                  <h2 className=\"fw-bold text-warning\">{stats.pending}</h2>\n                  <small className=\"text-danger\">\n                    <TrendingUp size={12} className=\"me-1\" />\n                    -5% vs last month\n                  </small>\n                </Card.Body>\n              </Card>\n            </motion.div>\n          </Col>\n          \n          <Col md={3}>\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              whileHover={{ y: -5, scale: 1.02 }}\n            >\n              <Card className=\"h-100 border-0 shadow-lg\" style={{ \n                background: 'rgba(255, 255, 255, 0.95)',\n                backdropFilter: 'blur(10px)',\n                borderRadius: '20px'\n              }}>\n                <Card.Body className=\"text-center p-4\">\n                  <div className=\"mb-3\">\n                    <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                      width: '60px',\n                      height: '60px',\n                      background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                      borderRadius: '15px'\n                    }}>\n                      <Cog className=\"text-white\" size={24} />\n                    </div>\n                  </div>\n                  <h6 className=\"text-muted mb-1\">In Progress</h6>\n                  <h2 className=\"fw-bold text-info\">{stats.inProgress}</h2>\n                  <small className=\"text-success\">\n                    <TrendingUp size={12} className=\"me-1\" />\n                    +8% vs last month\n                  </small>\n                </Card.Body>\n              </Card>\n            </motion.div>\n          </Col>\n          \n          <Col md={3}>\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 0.3 }}\n              whileHover={{ y: -5, scale: 1.02 }}\n            >\n              <Card className=\"h-100 border-0 shadow-lg\" style={{ \n                background: 'rgba(255, 255, 255, 0.95)',\n                backdropFilter: 'blur(10px)',\n                borderRadius: '20px'\n              }}>\n                <Card.Body className=\"text-center p-4\">\n                  <div className=\"mb-3\">\n                    <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                      width: '60px',\n                      height: '60px',\n                      background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n                      borderRadius: '15px'\n                    }}>\n                      <DollarSign className=\"text-white\" size={24} />\n                    </div>\n                  </div>\n                  <h6 className=\"text-muted mb-1\">Revenue</h6>\n                  <h2 className=\"fw-bold text-success\">${stats.revenue.toFixed(2)}</h2>\n                  <small className=\"text-success\">\n                    <TrendingUp size={12} className=\"me-1\" />\n                    +15% vs last month\n                  </small>\n                </Card.Body>\n              </Card>\n            </motion.div>\n          </Col>\n        </Row>\n\n        {/* Job Queue Section */}\n        <Card className=\"border-0 shadow-lg\" style={{ \n          background: 'rgba(255, 255, 255, 0.95)',\n          backdropFilter: 'blur(10px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <div className=\"d-flex align-items-center justify-content-between mb-3\">\n              <h4 className=\"fw-bold mb-0\">\n                <Activity className=\"me-2\" size={20} />\n                Job Queue\n              </h4>\n              <div className=\"d-flex align-items-center gap-3\">\n                <Badge bg=\"primary\" className=\"px-3 py-2\">\n                  {filteredJobs.length} jobs\n                </Badge>\n                \n                <div className=\"d-flex rounded-pill bg-light p-1\">\n                  <Button\n                    variant={viewMode === 'cards' ? 'primary' : 'light'}\n                    size=\"sm\"\n                    onClick={() => setViewMode('cards')}\n                    className=\"rounded-pill px-3\"\n                  >\n                    <Grid3X3 size={16} />\n                  </Button>\n                  <Button\n                    variant={viewMode === 'table' ? 'primary' : 'light'}\n                    size=\"sm\"\n                    onClick={() => setViewMode('table')}\n                    className=\"rounded-pill px-3\"\n                  >\n                    <List size={16} />\n                  </Button>\n                </div>\n              </div>\n            </div>\n\n            {/* Search and Filter Controls */}\n            <Row className=\"g-3\">\n              <Col md={6}>\n                <InputGroup>\n                  <InputGroup.Text>\n                    <Search size={16} />\n                  </InputGroup.Text>\n                  <Form.Control\n                    placeholder=\"Search jobs...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    style={{ borderRadius: '0 10px 10px 0' }}\n                  />\n                </InputGroup>\n              </Col>\n              \n              <Col md={3}>\n                <Form.Select\n                  value={statusFilter}\n                  onChange={(e) => setStatusFilter(e.target.value)}\n                  style={{ borderRadius: '10px' }}\n                >\n                  <option value=\"All\">All Status</option>\n                  <option value=\"Requested\">Requested</option>\n                  <option value=\"UnderReview\">Under Review</option>\n                  <option value=\"Quoted\">Quoted</option>\n                  <option value=\"Confirmed\">Confirmed</option>\n                  <option value=\"InProgress\">In Progress</option>\n                  <option value=\"Completed\">Completed</option>\n                  <option value=\"Delivered\">Delivered</option>\n                </Form.Select>\n              </Col>\n            </Row>\n          </Card.Header>\n          \n          <Card.Body>\n            {filteredJobs.length > 0 ? (\n              <Row className=\"g-4\">\n                {filteredJobs.map((job, index) => (\n                  <Col key={job.id} md={6} lg={4}>\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: index * 0.05 }}\n                    >\n                      <Card className={`h-100 border-0 shadow-sm ${job.priority === 'Urgent' ? 'border-danger' : ''}`} style={{ \n                        borderRadius: '15px',\n                        background: job.priority === 'Urgent' ? 'rgba(220, 53, 69, 0.05)' : 'white'\n                      }}>\n                        <Card.Header className=\"border-0 bg-transparent\">\n                          <div className=\"d-flex align-items-start justify-content-between\">\n                            <div>\n                              <h6 className=\"fw-bold mb-1\">{job.jobNumber}</h6>\n                              {getPriorityBadge(job.priority)}\n                            </div>\n                            {getStatusBadge(job.status)}\n                          </div>\n                        </Card.Header>\n                        \n                        <Card.Body className=\"pt-0\">\n                          <div className=\"mb-3\">\n                            <p className=\"fw-semibold mb-1\">{job.studentName}</p>\n                            <p className=\"text-muted small mb-0\">{job.studentEmail}</p>\n                          </div>\n                          \n                          <div className=\"mb-3\">\n                            <div className=\"d-flex align-items-center text-muted small mb-2\">\n                              <FileText size={14} className=\"me-1\" />\n                              {job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName}\n                            </div>\n                            <div className=\"small text-muted\">\n                              <div><strong>Type:</strong> {job.printType}</div>\n                              <div><strong>Copies:</strong> {job.copies}</div>\n                              <div><strong>Color:</strong> {job.colorType}</div>\n                            </div>\n                          </div>\n                          \n                          {job.cost && (\n                            <div className=\"mb-3\">\n                              <span className=\"h5 fw-bold text-success\">\n                                ${job.cost.toFixed(2)}\n                              </span>\n                            </div>\n                          )}\n                          \n                          <div className=\"small text-muted mb-3\">\n                            {getTimeAgo(job.created)}\n                          </div>\n                          \n                          <div className=\"d-flex flex-wrap gap-1\">\n                            <Button\n                              variant=\"outline-primary\"\n                              size=\"sm\"\n                              onClick={() => handleDownloadFile(job.id, job.fileName)}\n                              style={{ borderRadius: '8px' }}\n                            >\n                              <Download size={12} />\n                            </Button>\n                            \n                            {job.status === 'Requested' && (\n                              <>\n                                <Button\n                                  variant=\"outline-success\"\n                                  size=\"sm\"\n                                  onClick={() => {\n                                    setSelectedJob(job);\n                                    setShowQuoteModal(true);\n                                  }}\n                                  style={{ borderRadius: '8px' }}\n                                >\n                                  <DollarSign size={12} />\n                                </Button>\n                                <Button\n                                  variant=\"outline-danger\"\n                                  size=\"sm\"\n                                  onClick={() => handleStatusUpdate(job.id, 'Rejected')}\n                                  style={{ borderRadius: '8px' }}\n                                >\n                                  <X size={12} />\n                                </Button>\n                              </>\n                            )}\n\n                            {job.status === 'Confirmed' && (\n                              <Button\n                                variant=\"outline-primary\"\n                                size=\"sm\"\n                                onClick={() => handleStatusUpdate(job.id, 'InProgress')}\n                                style={{ borderRadius: '8px' }}\n                              >\n                                <Play size={12} />\n                              </Button>\n                            )}\n\n                            {job.status === 'InProgress' && (\n                              <Button\n                                variant=\"outline-success\"\n                                size=\"sm\"\n                                onClick={() => handleStatusUpdate(job.id, 'Completed')}\n                                style={{ borderRadius: '8px' }}\n                              >\n                                <CheckCircle size={12} />\n                              </Button>\n                            )}\n\n                            {job.status === 'Completed' && (\n                              <Button\n                                variant=\"outline-info\"\n                                size=\"sm\"\n                                onClick={() => handleStatusUpdate(job.id, 'Delivered')}\n                                style={{ borderRadius: '8px' }}\n                              >\n                                <Truck size={12} />\n                              </Button>\n                            )}\n\n                            <Button\n                              variant=\"outline-secondary\"\n                              size=\"sm\"\n                              onClick={() => {\n                                setSelectedJob(job);\n                                setShowJobDetailsModal(true);\n                              }}\n                              style={{ borderRadius: '8px' }}\n                            >\n                              <Eye size={12} />\n                            </Button>\n                          </div>\n                        </Card.Body>\n                      </Card>\n                    </motion.div>\n                  </Col>\n                ))}\n              </Row>\n            ) : (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                className=\"text-center py-5\"\n              >\n                <div className=\"mb-4\">\n                  <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                    width: '80px',\n                    height: '80px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '20px'\n                  }}>\n                    <FileText className=\"text-white\" size={40} />\n                  </div>\n                </div>\n                <h5 className=\"fw-semibold mb-2\">No jobs found</h5>\n                <p className=\"text-muted\">No jobs match your current filter criteria.</p>\n              </motion.div>\n            )}\n          </Card.Body>\n        </Card>\n      </Container>\n    </div>\n  );\n};\n\nexport default ProfessionalXeroxDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAASC,UAAU,QAAQ,iBAAiB;AACnG,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,QAAQ,EAERC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,CAAC,EACDC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,UAAU,QACL,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,EAAkBC,aAAa,QAAoB,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqB5F,MAAMC,0BAAoC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjD,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGtC,QAAQ,CAAa,EAAE,CAAC;EAChD,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAa,EAAE,CAAC;EAChE,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAoB,OAAO,CAAC;EACpE,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAkB,IAAI,CAAC;EACrE,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAC;IACjC6D,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE,CAAC;IACVC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EAEFhE,SAAS,CAAC,MAAM;IACdiE,SAAS,CAAC,CAAC;IACX,MAAMC,QAAQ,GAAGC,WAAW,CAACF,SAAS,EAAE,KAAK,CAAC;IAC9C,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAENlE,SAAS,CAAC,MAAM;IACdqE,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACjC,IAAI,EAAEI,UAAU,EAAEE,YAAY,CAAC,CAAC;EAEpC,MAAMuB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BR,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAM3C,WAAW,CAAC4C,kBAAkB,CAAC,CAAC;MACvDlC,OAAO,CAACiC,QAAQ,CAACE,IAAI,CAAC;MAEtB,MAAMC,SAAS,GAAGH,QAAQ,CAACE,IAAI,CAACE,MAAM;MACtC,MAAMC,WAAW,GAAGL,QAAQ,CAACE,IAAI,CAACI,MAAM,CAAEC,GAAa,IAAK,CAAC,WAAW,EAAE,aAAa,CAAC,CAACC,QAAQ,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,CAACL,MAAM;MACrH,MAAMM,cAAc,GAAGV,QAAQ,CAACE,IAAI,CAACI,MAAM,CAAEC,GAAa,IAAK,CAAC,WAAW,EAAE,YAAY,CAAC,CAACC,QAAQ,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,CAACL,MAAM;MACvH,MAAMO,aAAa,GAAGX,QAAQ,CAACE,IAAI,CAACI,MAAM,CAAEC,GAAa,IAAK,CAAC,WAAW,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,CAACL,MAAM;MACrH,MAAMQ,YAAY,GAAGZ,QAAQ,CAACE,IAAI,CAACW,MAAM,CAAC,CAACC,GAAW,EAAEP,GAAa,KAAKO,GAAG,IAAIP,GAAG,CAACQ,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAEnG1B,QAAQ,CAAC;QACPC,KAAK,EAAEa,SAAS;QAChBZ,OAAO,EAAEc,WAAW;QACpBb,UAAU,EAAEkB,cAAc;QAC1BjB,SAAS,EAAEkB,aAAa;QACxBjB,OAAO,EAAEkB;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CjD,OAAO,CAAC,EAAE,CAAC;IACb,CAAC,SAAS;MACRoB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAImB,QAAQ,GAAGpD,IAAI,CAACwC,MAAM,CAACC,GAAG,IAAI;MAChC,MAAMY,aAAa,GAAGjD,UAAU,KAAK,EAAE,IACrCqC,GAAG,CAACa,SAAS,CAACC,WAAW,CAAC,CAAC,CAACb,QAAQ,CAACtC,UAAU,CAACmD,WAAW,CAAC,CAAC,CAAC,IAC9Dd,GAAG,CAACe,QAAQ,CAACD,WAAW,CAAC,CAAC,CAACb,QAAQ,CAACtC,UAAU,CAACmD,WAAW,CAAC,CAAC,CAAC,IAC7Dd,GAAG,CAACgB,WAAW,CAACF,WAAW,CAAC,CAAC,CAACb,QAAQ,CAACtC,UAAU,CAACmD,WAAW,CAAC,CAAC,CAAC;MAElE,MAAMG,aAAa,GAAGpD,YAAY,KAAK,KAAK,IAAImC,GAAG,CAACE,MAAM,KAAKrC,YAAY;MAE3E,OAAO+C,aAAa,IAAIK,aAAa;IACvC,CAAC,CAAC;IAEFvD,eAAe,CAACiD,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMO,cAAc,GAAIhB,MAAc,IAAK;IACzC,MAAMiB,YAAY,GAAG;MACnB,WAAW,EAAE,WAAW;MACxB,aAAa,EAAE,MAAM;MACrB,QAAQ,EAAE,SAAS;MACnB,qBAAqB,EAAE,SAAS;MAChC,WAAW,EAAE,MAAM;MACnB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,QAAQ;MACpB,WAAW,EAAE;IACf,CAAC;IAED,MAAMC,OAAO,GAAGD,YAAY,CAACjB,MAAM,CAA8B,IAAI,WAAW;IAEhF,oBACEjD,OAAA,CAACxB,KAAK;MAAC4F,EAAE,EAAED,OAAQ;MAAAE,QAAA,EAChBpB;IAAM;MAAAa,QAAA,EAAAQ,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,MAAMC,cAAc,GAAG;MACrB,KAAK,EAAE,SAAS;MAChB,QAAQ,EAAE,WAAW;MACrB,MAAM,EAAE,SAAS;MACjB,QAAQ,EAAE;IACZ,CAAC;IAED,MAAMR,OAAO,GAAGQ,cAAc,CAACD,QAAQ,CAAgC,IAAI,WAAW;IAEtF,oBACE1E,OAAA,CAACxB,KAAK;MAAC4F,EAAE,EAAED,OAAQ;MAACS,SAAS,EAAC,MAAM;MAAAP,QAAA,EACjCK;IAAQ;MAAAZ,QAAA,EAAAQ,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEZ,CAAC;EAED,MAAMK,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAEC,SAAiB,KAAK;IACrE,IAAI;MACF,MAAMlF,WAAW,CAACmF,eAAe,CAACF,KAAK,EAAEC,SAAS,CAAC;MACnD5C,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAMyB,kBAAkB,GAAG,MAAAA,CAAOH,KAAa,EAAEhB,QAAgB,KAAK;IACpE,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAM1C,aAAa,CAACoF,YAAY,CAACJ,KAAK,CAAC;MACxD,MAAMK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC5C,QAAQ,CAACE,IAAI,CAAC,CAAC;MACtC,MAAM2C,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAG/B,QAAQ;MACxB4B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAM2C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC/E,WAAW,IAAI,CAACE,WAAW,EAAE;IAElC,IAAI;MACF,MAAMzB,WAAW,CAACuG,WAAW,CAAChF,WAAW,CAACiF,EAAE,EAAE;QAC5CC,MAAM,EAAEC,UAAU,CAACjF,WAAW,CAAC;QAC/BkF,uBAAuB,EAAEhF;MAC3B,CAAC,CAAC;MAEFP,iBAAiB,CAAC,KAAK,CAAC;MACxBM,cAAc,CAAC,EAAE,CAAC;MAClBE,gBAAgB,CAAC,EAAE,CAAC;MACpBJ,cAAc,CAAC,IAAI,CAAC;MACpBc,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMiD,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC,GAAGJ,IAAI,CAACI,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,OAAO;IACtD,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC,OAAO;IACzE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC,OAAO;EACnD,CAAC;EAED,oBACE9G,OAAA;IAAK4E,SAAS,EAAC,cAAc;IAACsC,KAAK,EAAE;MACnCC,UAAU,EAAE,mDAAmD;MAC/DC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE;IACd,CAAE;IAAAhD,QAAA,eACArE,OAAA,CAAC7B,SAAS;MAACmJ,KAAK;MAAC1C,SAAS,EAAC,MAAM;MAAAP,QAAA,gBAE/BrE,OAAA,CAACrB,MAAM,CAAC4I,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BjD,SAAS,EAAC,kBAAkB;QAAAP,QAAA,gBAE5BrE,OAAA;UAAI4E,SAAS,EAAC,mCAAmC;UAAAP,QAAA,EAAC;QAElD;UAAAP,QAAA,EAAAQ,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxE,OAAA;UAAG4E,SAAS,EAAC,yBAAyB;UAAAP,QAAA,GAAC,gBACvB,eAAArE,OAAA;YAAM4E,SAAS,EAAC,wBAAwB;YAAAP,QAAA,EAAEhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyH;UAAQ;YAAAhE,QAAA,EAAAQ,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,KAChF;QAAA;UAAAV,QAAA,EAAAQ,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJxE,OAAA;UAAK4E,SAAS,EAAC,wDAAwD;UAAAP,QAAA,gBACrErE,OAAA,CAACrB,MAAM,CAAC4I,GAAG;YAACQ,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAACC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAA3D,QAAA,eACjErE,OAAA,CAACzB,MAAM;cACL4F,OAAO,EAAC,OAAO;cACf+D,OAAO,EAAE/F,SAAU;cACnBgG,QAAQ,EAAEzG,YAAa;cACvBkD,SAAS,EAAC,WAAW;cACrBsC,KAAK,EAAE;gBAAEkB,YAAY,EAAE;cAAO,CAAE;cAAA/D,QAAA,gBAEhCrE,OAAA,CAACN,SAAS;gBAACkF,SAAS,EAAE,QAAQlD,YAAY,GAAG,MAAM,GAAG,EAAE,EAAG;gBAAC2G,IAAI,EAAE;cAAG;gBAAAvE,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAE1E;YAAA;cAAAV,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAV,QAAA,EAAAQ,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEbxE,OAAA,CAACxB,KAAK;YAAC4F,EAAE,EAAC,OAAO;YAACkE,IAAI,EAAC,MAAM;YAAC1D,SAAS,EAAC,WAAW;YAAAP,QAAA,GAAC,gBACpC,EAAC,IAAIuC,IAAI,CAAC,CAAC,CAAC2B,kBAAkB,CAAC,CAAC;UAAA;YAAAzE,QAAA,EAAAQ,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAV,QAAA,EAAAQ,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAV,QAAA,EAAAQ,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbxE,OAAA,CAAC5B,GAAG;QAACwG,SAAS,EAAC,UAAU;QAAAP,QAAA,gBACvBrE,OAAA,CAAC3B,GAAG;UAACmK,EAAE,EAAE,CAAE;UAAAnE,QAAA,eACTrE,OAAA,CAACrB,MAAM,CAAC4I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAI,CAAE;YACpCL,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAE,CAAE;YAClCJ,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BE,UAAU,EAAE;cAAEL,CAAC,EAAE,CAAC,CAAC;cAAEM,KAAK,EAAE;YAAK,CAAE;YAAA3D,QAAA,eAEnCrE,OAAA,CAAC1B,IAAI;cAACsG,SAAS,EAAC,0BAA0B;cAACsC,KAAK,EAAE;gBAChDC,UAAU,EAAE,2BAA2B;gBACvCsB,cAAc,EAAE,YAAY;gBAC5BL,YAAY,EAAE;cAChB,CAAE;cAAA/D,QAAA,eACArE,OAAA,CAAC1B,IAAI,CAACoK,IAAI;gBAAC9D,SAAS,EAAC,iBAAiB;gBAAAP,QAAA,gBACpCrE,OAAA;kBAAK4E,SAAS,EAAC,MAAM;kBAAAP,QAAA,eACnBrE,OAAA;oBAAK4E,SAAS,EAAC,yDAAyD;oBAACsC,KAAK,EAAE;sBAC9EyB,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdzB,UAAU,EAAE,mDAAmD;sBAC/DiB,YAAY,EAAE;oBAChB,CAAE;oBAAA/D,QAAA,eACArE,OAAA,CAACpB,QAAQ;sBAACgG,SAAS,EAAC,YAAY;sBAACyD,IAAI,EAAE;oBAAG;sBAAAvE,QAAA,EAAAQ,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAV,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C;gBAAC;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxE,OAAA;kBAAI4E,SAAS,EAAC,iBAAiB;kBAAAP,QAAA,EAAC;gBAAU;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/CxE,OAAA;kBAAI4E,SAAS,EAAC,sBAAsB;kBAAAP,QAAA,EAAEzC,KAAK,CAACE;gBAAK;kBAAAgC,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvDxE,OAAA;kBAAO4E,SAAS,EAAC,cAAc;kBAAAP,QAAA,gBAC7BrE,OAAA,CAACL,UAAU;oBAAC0I,IAAI,EAAE,EAAG;oBAACzD,SAAS,EAAC;kBAAM;oBAAAd,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAE3C;gBAAA;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAV,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAV,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAV,QAAA,EAAAQ,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAV,QAAA,EAAAQ,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENxE,OAAA,CAAC3B,GAAG;UAACmK,EAAE,EAAE,CAAE;UAAAnE,QAAA,eACTrE,OAAA,CAACrB,MAAM,CAAC4I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAI,CAAE;YACpCL,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAE,CAAE;YAClCJ,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEgB,KAAK,EAAE;YAAI,CAAE;YAC1Cd,UAAU,EAAE;cAAEL,CAAC,EAAE,CAAC,CAAC;cAAEM,KAAK,EAAE;YAAK,CAAE;YAAA3D,QAAA,eAEnCrE,OAAA,CAAC1B,IAAI;cAACsG,SAAS,EAAC,0BAA0B;cAACsC,KAAK,EAAE;gBAChDC,UAAU,EAAE,2BAA2B;gBACvCsB,cAAc,EAAE,YAAY;gBAC5BL,YAAY,EAAE;cAChB,CAAE;cAAA/D,QAAA,eACArE,OAAA,CAAC1B,IAAI,CAACoK,IAAI;gBAAC9D,SAAS,EAAC,iBAAiB;gBAAAP,QAAA,gBACpCrE,OAAA;kBAAK4E,SAAS,EAAC,MAAM;kBAAAP,QAAA,eACnBrE,OAAA;oBAAK4E,SAAS,EAAC,yDAAyD;oBAACsC,KAAK,EAAE;sBAC9EyB,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdzB,UAAU,EAAE,mDAAmD;sBAC/DiB,YAAY,EAAE;oBAChB,CAAE;oBAAA/D,QAAA,eACArE,OAAA,CAACnB,KAAK;sBAAC+F,SAAS,EAAC,YAAY;sBAACyD,IAAI,EAAE;oBAAG;sBAAAvE,QAAA,EAAAQ,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAV,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxE,OAAA;kBAAI4E,SAAS,EAAC,iBAAiB;kBAAAP,QAAA,EAAC;gBAAY;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDxE,OAAA;kBAAI4E,SAAS,EAAC,sBAAsB;kBAAAP,QAAA,EAAEzC,KAAK,CAACG;gBAAO;kBAAA+B,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzDxE,OAAA;kBAAO4E,SAAS,EAAC,aAAa;kBAAAP,QAAA,gBAC5BrE,OAAA,CAACL,UAAU;oBAAC0I,IAAI,EAAE,EAAG;oBAACzD,SAAS,EAAC;kBAAM;oBAAAd,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qBAE3C;gBAAA;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAV,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAV,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAV,QAAA,EAAAQ,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAV,QAAA,EAAAQ,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENxE,OAAA,CAAC3B,GAAG;UAACmK,EAAE,EAAE,CAAE;UAAAnE,QAAA,eACTrE,OAAA,CAACrB,MAAM,CAAC4I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAI,CAAE;YACpCL,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAE,CAAE;YAClCJ,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEgB,KAAK,EAAE;YAAI,CAAE;YAC1Cd,UAAU,EAAE;cAAEL,CAAC,EAAE,CAAC,CAAC;cAAEM,KAAK,EAAE;YAAK,CAAE;YAAA3D,QAAA,eAEnCrE,OAAA,CAAC1B,IAAI;cAACsG,SAAS,EAAC,0BAA0B;cAACsC,KAAK,EAAE;gBAChDC,UAAU,EAAE,2BAA2B;gBACvCsB,cAAc,EAAE,YAAY;gBAC5BL,YAAY,EAAE;cAChB,CAAE;cAAA/D,QAAA,eACArE,OAAA,CAAC1B,IAAI,CAACoK,IAAI;gBAAC9D,SAAS,EAAC,iBAAiB;gBAAAP,QAAA,gBACpCrE,OAAA;kBAAK4E,SAAS,EAAC,MAAM;kBAAAP,QAAA,eACnBrE,OAAA;oBAAK4E,SAAS,EAAC,yDAAyD;oBAACsC,KAAK,EAAE;sBAC9EyB,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdzB,UAAU,EAAE,mDAAmD;sBAC/DiB,YAAY,EAAE;oBAChB,CAAE;oBAAA/D,QAAA,eACArE,OAAA,CAAClB,GAAG;sBAAC8F,SAAS,EAAC,YAAY;sBAACyD,IAAI,EAAE;oBAAG;sBAAAvE,QAAA,EAAAQ,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAV,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC;gBAAC;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxE,OAAA;kBAAI4E,SAAS,EAAC,iBAAiB;kBAAAP,QAAA,EAAC;gBAAW;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChDxE,OAAA;kBAAI4E,SAAS,EAAC,mBAAmB;kBAAAP,QAAA,EAAEzC,KAAK,CAACI;gBAAU;kBAAA8B,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzDxE,OAAA;kBAAO4E,SAAS,EAAC,cAAc;kBAAAP,QAAA,gBAC7BrE,OAAA,CAACL,UAAU;oBAAC0I,IAAI,EAAE,EAAG;oBAACzD,SAAS,EAAC;kBAAM;oBAAAd,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qBAE3C;gBAAA;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAV,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAV,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAV,QAAA,EAAAQ,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAV,QAAA,EAAAQ,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENxE,OAAA,CAAC3B,GAAG;UAACmK,EAAE,EAAE,CAAE;UAAAnE,QAAA,eACTrE,OAAA,CAACrB,MAAM,CAAC4I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAI,CAAE;YACpCL,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAE,CAAE;YAClCJ,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEgB,KAAK,EAAE;YAAI,CAAE;YAC1Cd,UAAU,EAAE;cAAEL,CAAC,EAAE,CAAC,CAAC;cAAEM,KAAK,EAAE;YAAK,CAAE;YAAA3D,QAAA,eAEnCrE,OAAA,CAAC1B,IAAI;cAACsG,SAAS,EAAC,0BAA0B;cAACsC,KAAK,EAAE;gBAChDC,UAAU,EAAE,2BAA2B;gBACvCsB,cAAc,EAAE,YAAY;gBAC5BL,YAAY,EAAE;cAChB,CAAE;cAAA/D,QAAA,eACArE,OAAA,CAAC1B,IAAI,CAACoK,IAAI;gBAAC9D,SAAS,EAAC,iBAAiB;gBAAAP,QAAA,gBACpCrE,OAAA;kBAAK4E,SAAS,EAAC,MAAM;kBAAAP,QAAA,eACnBrE,OAAA;oBAAK4E,SAAS,EAAC,yDAAyD;oBAACsC,KAAK,EAAE;sBAC9EyB,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdzB,UAAU,EAAE,mDAAmD;sBAC/DiB,YAAY,EAAE;oBAChB,CAAE;oBAAA/D,QAAA,eACArE,OAAA,CAACjB,UAAU;sBAAC6F,SAAS,EAAC,YAAY;sBAACyD,IAAI,EAAE;oBAAG;sBAAAvE,QAAA,EAAAQ,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAV,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C;gBAAC;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxE,OAAA;kBAAI4E,SAAS,EAAC,iBAAiB;kBAAAP,QAAA,EAAC;gBAAO;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5CxE,OAAA;kBAAI4E,SAAS,EAAC,sBAAsB;kBAAAP,QAAA,GAAC,GAAC,EAACzC,KAAK,CAACM,OAAO,CAAC4G,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAhF,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrExE,OAAA;kBAAO4E,SAAS,EAAC,cAAc;kBAAAP,QAAA,gBAC7BrE,OAAA,CAACL,UAAU;oBAAC0I,IAAI,EAAE,EAAG;oBAACzD,SAAS,EAAC;kBAAM;oBAAAd,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAE3C;gBAAA;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAV,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAV,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAV,QAAA,EAAAQ,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAV,QAAA,EAAAQ,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAV,QAAA,EAAAQ,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxE,OAAA,CAAC1B,IAAI;QAACsG,SAAS,EAAC,oBAAoB;QAACsC,KAAK,EAAE;UAC1CC,UAAU,EAAE,2BAA2B;UACvCsB,cAAc,EAAE,YAAY;UAC5BL,YAAY,EAAE;QAChB,CAAE;QAAA/D,QAAA,gBACArE,OAAA,CAAC1B,IAAI,CAACyK,MAAM;UAACnE,SAAS,EAAC,yBAAyB;UAAAP,QAAA,gBAC9CrE,OAAA;YAAK4E,SAAS,EAAC,wDAAwD;YAAAP,QAAA,gBACrErE,OAAA;cAAI4E,SAAS,EAAC,cAAc;cAAAP,QAAA,gBAC1BrE,OAAA,CAACP,QAAQ;gBAACmF,SAAS,EAAC,MAAM;gBAACyD,IAAI,EAAE;cAAG;gBAAAvE,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAEzC;YAAA;cAAAV,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxE,OAAA;cAAK4E,SAAS,EAAC,iCAAiC;cAAAP,QAAA,gBAC9CrE,OAAA,CAACxB,KAAK;gBAAC4F,EAAE,EAAC,SAAS;gBAACQ,SAAS,EAAC,WAAW;gBAAAP,QAAA,GACtC7D,YAAY,CAACoC,MAAM,EAAC,OACvB;cAAA;gBAAAkB,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAERxE,OAAA;gBAAK4E,SAAS,EAAC,kCAAkC;gBAAAP,QAAA,gBAC/CrE,OAAA,CAACzB,MAAM;kBACL4F,OAAO,EAAErD,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,OAAQ;kBACpDuH,IAAI,EAAC,IAAI;kBACTH,OAAO,EAAEA,CAAA,KAAMnH,WAAW,CAAC,OAAO,CAAE;kBACpC6D,SAAS,EAAC,mBAAmB;kBAAAP,QAAA,eAE7BrE,OAAA,CAACT,OAAO;oBAAC8I,IAAI,EAAE;kBAAG;oBAAAvE,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACTxE,OAAA,CAACzB,MAAM;kBACL4F,OAAO,EAAErD,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,OAAQ;kBACpDuH,IAAI,EAAC,IAAI;kBACTH,OAAO,EAAEA,CAAA,KAAMnH,WAAW,CAAC,OAAO,CAAE;kBACpC6D,SAAS,EAAC,mBAAmB;kBAAAP,QAAA,eAE7BrE,OAAA,CAACR,IAAI;oBAAC6I,IAAI,EAAE;kBAAG;oBAAAvE,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAV,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAV,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAV,QAAA,EAAAQ,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxE,OAAA,CAAC5B,GAAG;YAACwG,SAAS,EAAC,KAAK;YAAAP,QAAA,gBAClBrE,OAAA,CAAC3B,GAAG;cAACmK,EAAE,EAAE,CAAE;cAAAnE,QAAA,eACTrE,OAAA,CAACtB,UAAU;gBAAA2F,QAAA,gBACTrE,OAAA,CAACtB,UAAU,CAACsK,IAAI;kBAAA3E,QAAA,eACdrE,OAAA,CAACV,MAAM;oBAAC+I,IAAI,EAAE;kBAAG;oBAAAvE,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAClBxE,OAAA,CAACvB,IAAI,CAACwK,OAAO;kBACXC,WAAW,EAAC,gBAAgB;kBAC5BC,KAAK,EAAEzI,UAAW;kBAClB0I,QAAQ,EAAGC,CAAC,IAAK1I,aAAa,CAAC0I,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CjC,KAAK,EAAE;oBAAEkB,YAAY,EAAE;kBAAgB;gBAAE;kBAAAtE,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAV,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAV,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENxE,OAAA,CAAC3B,GAAG;cAACmK,EAAE,EAAE,CAAE;cAAAnE,QAAA,eACTrE,OAAA,CAACvB,IAAI,CAAC8K,MAAM;gBACVJ,KAAK,EAAEvI,YAAa;gBACpBwI,QAAQ,EAAGC,CAAC,IAAKxI,eAAe,CAACwI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACjDjC,KAAK,EAAE;kBAAEkB,YAAY,EAAE;gBAAO,CAAE;gBAAA/D,QAAA,gBAEhCrE,OAAA;kBAAQmJ,KAAK,EAAC,KAAK;kBAAA9E,QAAA,EAAC;gBAAU;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCxE,OAAA;kBAAQmJ,KAAK,EAAC,WAAW;kBAAA9E,QAAA,EAAC;gBAAS;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CxE,OAAA;kBAAQmJ,KAAK,EAAC,aAAa;kBAAA9E,QAAA,EAAC;gBAAY;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjDxE,OAAA;kBAAQmJ,KAAK,EAAC,QAAQ;kBAAA9E,QAAA,EAAC;gBAAM;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCxE,OAAA;kBAAQmJ,KAAK,EAAC,WAAW;kBAAA9E,QAAA,EAAC;gBAAS;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CxE,OAAA;kBAAQmJ,KAAK,EAAC,YAAY;kBAAA9E,QAAA,EAAC;gBAAW;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/CxE,OAAA;kBAAQmJ,KAAK,EAAC,WAAW;kBAAA9E,QAAA,EAAC;gBAAS;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CxE,OAAA;kBAAQmJ,KAAK,EAAC,WAAW;kBAAA9E,QAAA,EAAC;gBAAS;kBAAAP,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAV,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAV,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAV,QAAA,EAAAQ,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAV,QAAA,EAAAQ,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAEdxE,OAAA,CAAC1B,IAAI,CAACoK,IAAI;UAAArE,QAAA,EACP7D,YAAY,CAACoC,MAAM,GAAG,CAAC,gBACtB5C,OAAA,CAAC5B,GAAG;YAACwG,SAAS,EAAC,KAAK;YAAAP,QAAA,EACjB7D,YAAY,CAACgJ,GAAG,CAAC,CAACzG,GAAG,EAAE0G,KAAK,kBAC3BzJ,OAAA,CAAC3B,GAAG;cAAcmK,EAAE,EAAE,CAAE;cAACkB,EAAE,EAAE,CAAE;cAAArF,QAAA,eAC7BrE,OAAA,CAACrB,MAAM,CAAC4I,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,UAAU,EAAE;kBAAEiB,KAAK,EAAEY,KAAK,GAAG;gBAAK,CAAE;gBAAApF,QAAA,eAEpCrE,OAAA,CAAC1B,IAAI;kBAACsG,SAAS,EAAE,4BAA4B7B,GAAG,CAAC2B,QAAQ,KAAK,QAAQ,GAAG,eAAe,GAAG,EAAE,EAAG;kBAACwC,KAAK,EAAE;oBACtGkB,YAAY,EAAE,MAAM;oBACpBjB,UAAU,EAAEpE,GAAG,CAAC2B,QAAQ,KAAK,QAAQ,GAAG,yBAAyB,GAAG;kBACtE,CAAE;kBAAAL,QAAA,gBACArE,OAAA,CAAC1B,IAAI,CAACyK,MAAM;oBAACnE,SAAS,EAAC,yBAAyB;oBAAAP,QAAA,eAC9CrE,OAAA;sBAAK4E,SAAS,EAAC,kDAAkD;sBAAAP,QAAA,gBAC/DrE,OAAA;wBAAAqE,QAAA,gBACErE,OAAA;0BAAI4E,SAAS,EAAC,cAAc;0BAAAP,QAAA,EAAEtB,GAAG,CAACa;wBAAS;0BAAAE,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,EAChDC,gBAAgB,CAAC1B,GAAG,CAAC2B,QAAQ,CAAC;sBAAA;wBAAAZ,QAAA,EAAAQ,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,EACLP,cAAc,CAAClB,GAAG,CAACE,MAAM,CAAC;oBAAA;sBAAAa,QAAA,EAAAQ,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB;kBAAC;oBAAAV,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,eAEdxE,OAAA,CAAC1B,IAAI,CAACoK,IAAI;oBAAC9D,SAAS,EAAC,MAAM;oBAAAP,QAAA,gBACzBrE,OAAA;sBAAK4E,SAAS,EAAC,MAAM;sBAAAP,QAAA,gBACnBrE,OAAA;wBAAG4E,SAAS,EAAC,kBAAkB;wBAAAP,QAAA,EAAEtB,GAAG,CAACgB;sBAAW;wBAAAD,QAAA,EAAAQ,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrDxE,OAAA;wBAAG4E,SAAS,EAAC,uBAAuB;wBAAAP,QAAA,EAAEtB,GAAG,CAAC4G;sBAAY;wBAAA7F,QAAA,EAAAQ,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAV,QAAA,EAAAQ,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eAENxE,OAAA;sBAAK4E,SAAS,EAAC,MAAM;sBAAAP,QAAA,gBACnBrE,OAAA;wBAAK4E,SAAS,EAAC,iDAAiD;wBAAAP,QAAA,gBAC9DrE,OAAA,CAACpB,QAAQ;0BAACyJ,IAAI,EAAE,EAAG;0BAACzD,SAAS,EAAC;wBAAM;0BAAAd,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACtCzB,GAAG,CAACe,QAAQ,CAAClB,MAAM,GAAG,EAAE,GAAGG,GAAG,CAACe,QAAQ,CAAC8F,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG7G,GAAG,CAACe,QAAQ;sBAAA;wBAAAA,QAAA,EAAAQ,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE,CAAC,eACNxE,OAAA;wBAAK4E,SAAS,EAAC,kBAAkB;wBAAAP,QAAA,gBAC/BrE,OAAA;0BAAAqE,QAAA,gBAAKrE,OAAA;4BAAAqE,QAAA,EAAQ;0BAAK;4BAAAP,QAAA,EAAAQ,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACzB,GAAG,CAAC8G,SAAS;wBAAA;0BAAA/F,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACjDxE,OAAA;0BAAAqE,QAAA,gBAAKrE,OAAA;4BAAAqE,QAAA,EAAQ;0BAAO;4BAAAP,QAAA,EAAAQ,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACzB,GAAG,CAAC+G,MAAM;wBAAA;0BAAAhG,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAChDxE,OAAA;0BAAAqE,QAAA,gBAAKrE,OAAA;4BAAAqE,QAAA,EAAQ;0BAAM;4BAAAP,QAAA,EAAAQ,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACzB,GAAG,CAACgH,SAAS;wBAAA;0BAAAjG,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAV,QAAA,EAAAQ,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC;oBAAA;sBAAAV,QAAA,EAAAQ,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EAELzB,GAAG,CAACQ,IAAI,iBACPvD,OAAA;sBAAK4E,SAAS,EAAC,MAAM;sBAAAP,QAAA,eACnBrE,OAAA;wBAAM4E,SAAS,EAAC,yBAAyB;wBAAAP,QAAA,GAAC,GACvC,EAACtB,GAAG,CAACQ,IAAI,CAACuF,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAhF,QAAA,EAAAQ,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB;oBAAC;sBAAAV,QAAA,EAAAQ,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CACN,eAEDxE,OAAA;sBAAK4E,SAAS,EAAC,uBAAuB;sBAAAP,QAAA,EACnCoC,UAAU,CAAC1D,GAAG,CAACiH,OAAO;oBAAC;sBAAAlG,QAAA,EAAAQ,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eAENxE,OAAA;sBAAK4E,SAAS,EAAC,wBAAwB;sBAAAP,QAAA,gBACrCrE,OAAA,CAACzB,MAAM;wBACL4F,OAAO,EAAC,iBAAiB;wBACzBkE,IAAI,EAAC,IAAI;wBACTH,OAAO,EAAEA,CAAA,KAAMjD,kBAAkB,CAAClC,GAAG,CAACsD,EAAE,EAAEtD,GAAG,CAACe,QAAQ,CAAE;wBACxDoD,KAAK,EAAE;0BAAEkB,YAAY,EAAE;wBAAM,CAAE;wBAAA/D,QAAA,eAE/BrE,OAAA,CAAChB,QAAQ;0BAACqJ,IAAI,EAAE;wBAAG;0BAAAvE,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAV,QAAA,EAAAQ,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChB,CAAC,EAERzB,GAAG,CAACE,MAAM,KAAK,WAAW,iBACzBjD,OAAA,CAAAE,SAAA;wBAAAmE,QAAA,gBACErE,OAAA,CAACzB,MAAM;0BACL4F,OAAO,EAAC,iBAAiB;0BACzBkE,IAAI,EAAC,IAAI;0BACTH,OAAO,EAAEA,CAAA,KAAM;4BACb7G,cAAc,CAAC0B,GAAG,CAAC;4BACnB9B,iBAAiB,CAAC,IAAI,CAAC;0BACzB,CAAE;0BACFiG,KAAK,EAAE;4BAAEkB,YAAY,EAAE;0BAAM,CAAE;0BAAA/D,QAAA,eAE/BrE,OAAA,CAACjB,UAAU;4BAACsJ,IAAI,EAAE;0BAAG;4BAAAvE,QAAA,EAAAQ,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAV,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClB,CAAC,eACTxE,OAAA,CAACzB,MAAM;0BACL4F,OAAO,EAAC,gBAAgB;0BACxBkE,IAAI,EAAC,IAAI;0BACTH,OAAO,EAAEA,CAAA,KAAMrD,kBAAkB,CAAC9B,GAAG,CAACsD,EAAE,EAAE,UAAU,CAAE;0BACtDa,KAAK,EAAE;4BAAEkB,YAAY,EAAE;0BAAM,CAAE;0BAAA/D,QAAA,eAE/BrE,OAAA,CAACX,CAAC;4BAACgJ,IAAI,EAAE;0BAAG;4BAAAvE,QAAA,EAAAQ,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAV,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT,CAAC;sBAAA,eACT,CACH,EAEAzB,GAAG,CAACE,MAAM,KAAK,WAAW,iBACzBjD,OAAA,CAACzB,MAAM;wBACL4F,OAAO,EAAC,iBAAiB;wBACzBkE,IAAI,EAAC,IAAI;wBACTH,OAAO,EAAEA,CAAA,KAAMrD,kBAAkB,CAAC9B,GAAG,CAACsD,EAAE,EAAE,YAAY,CAAE;wBACxDa,KAAK,EAAE;0BAAEkB,YAAY,EAAE;wBAAM,CAAE;wBAAA/D,QAAA,eAE/BrE,OAAA,CAACd,IAAI;0BAACmJ,IAAI,EAAE;wBAAG;0BAAAvE,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAV,QAAA,EAAAQ,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CACT,EAEAzB,GAAG,CAACE,MAAM,KAAK,YAAY,iBAC1BjD,OAAA,CAACzB,MAAM;wBACL4F,OAAO,EAAC,iBAAiB;wBACzBkE,IAAI,EAAC,IAAI;wBACTH,OAAO,EAAEA,CAAA,KAAMrD,kBAAkB,CAAC9B,GAAG,CAACsD,EAAE,EAAE,WAAW,CAAE;wBACvDa,KAAK,EAAE;0BAAEkB,YAAY,EAAE;wBAAM,CAAE;wBAAA/D,QAAA,eAE/BrE,OAAA,CAACb,WAAW;0BAACkJ,IAAI,EAAE;wBAAG;0BAAAvE,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAV,QAAA,EAAAQ,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CACT,EAEAzB,GAAG,CAACE,MAAM,KAAK,WAAW,iBACzBjD,OAAA,CAACzB,MAAM;wBACL4F,OAAO,EAAC,cAAc;wBACtBkE,IAAI,EAAC,IAAI;wBACTH,OAAO,EAAEA,CAAA,KAAMrD,kBAAkB,CAAC9B,GAAG,CAACsD,EAAE,EAAE,WAAW,CAAE;wBACvDa,KAAK,EAAE;0BAAEkB,YAAY,EAAE;wBAAM,CAAE;wBAAA/D,QAAA,eAE/BrE,OAAA,CAACZ,KAAK;0BAACiJ,IAAI,EAAE;wBAAG;0BAAAvE,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAV,QAAA,EAAAQ,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CACT,eAEDxE,OAAA,CAACzB,MAAM;wBACL4F,OAAO,EAAC,mBAAmB;wBAC3BkE,IAAI,EAAC,IAAI;wBACTH,OAAO,EAAEA,CAAA,KAAM;0BACb7G,cAAc,CAAC0B,GAAG,CAAC;0BACnB5B,sBAAsB,CAAC,IAAI,CAAC;wBAC9B,CAAE;wBACF+F,KAAK,EAAE;0BAAEkB,YAAY,EAAE;wBAAM,CAAE;wBAAA/D,QAAA,eAE/BrE,OAAA,CAACf,GAAG;0BAACoJ,IAAI,EAAE;wBAAG;0BAAAvE,QAAA,EAAAQ,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAV,QAAA,EAAAQ,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX,CAAC;oBAAA;sBAAAV,QAAA,EAAAQ,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAV,QAAA,EAAAQ,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAV,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAV,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GAnILzB,GAAG,CAACsD,EAAE;cAAAvC,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoIX,CACN;UAAC;YAAAV,QAAA,EAAAQ,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENxE,OAAA,CAACrB,MAAM,CAAC4I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAI,CAAE;YACpCL,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAE,CAAE;YAClCpD,SAAS,EAAC,kBAAkB;YAAAP,QAAA,gBAE5BrE,OAAA;cAAK4E,SAAS,EAAC,MAAM;cAAAP,QAAA,eACnBrE,OAAA;gBAAK4E,SAAS,EAAC,yDAAyD;gBAACsC,KAAK,EAAE;kBAC9EyB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdzB,UAAU,EAAE,mDAAmD;kBAC/DiB,YAAY,EAAE;gBAChB,CAAE;gBAAA/D,QAAA,eACArE,OAAA,CAACpB,QAAQ;kBAACgG,SAAS,EAAC,YAAY;kBAACyD,IAAI,EAAE;gBAAG;kBAAAvE,QAAA,EAAAQ,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAV,QAAA,EAAAQ,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAV,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxE,OAAA;cAAI4E,SAAS,EAAC,kBAAkB;cAAAP,QAAA,EAAC;YAAa;cAAAP,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDxE,OAAA;cAAG4E,SAAS,EAAC,YAAY;cAAAP,QAAA,EAAC;YAA2C;cAAAP,QAAA,EAAAQ,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAV,QAAA,EAAAQ,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QACb;UAAAV,QAAA,EAAAQ,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAV,QAAA,EAAAQ,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAV,QAAA,EAAAQ,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAV,QAAA,EAAAQ,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACpE,EAAA,CA1kBID,0BAAoC;EAAA,QACvBP,OAAO;AAAA;AAAAqK,EAAA,GADpB9J,0BAAoC;AA4kB1C,eAAeA,0BAA0B;AAAC,IAAA8J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}