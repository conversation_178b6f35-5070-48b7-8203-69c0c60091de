{"ast": null, "code": "import { isMotionValue, defaultOffset, isGenerator, createGeneratorEasing, fillOffset } from 'motion-dom';\nimport { progress, secondsToMilliseconds, invariant, getEasingForSegment } from 'motion-utils';\nimport { resolveSubjects } from '../animate/resolve-subjects.mjs';\nimport { calculateRepeatDuration } from './utils/calc-repeat-duration.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { normalizeTimes } from './utils/normalize-times.mjs';\nimport { compareByTime } from './utils/sort.mjs';\nconst defaultSegmentEasing = \"easeInOut\";\nconst MAX_REPEAT = 20;\nfunction createAnimationsFromSequence(sequence, {\n  defaultTransition = {},\n  ...sequenceTransition\n} = {}, scope, generators) {\n  const defaultDuration = defaultTransition.duration || 0.3;\n  const animationDefinitions = new Map();\n  const sequences = new Map();\n  const elementCache = {};\n  const timeLabels = new Map();\n  let prevTime = 0;\n  let currentTime = 0;\n  let totalDuration = 0;\n  /**\n   * Build the timeline by mapping over the sequence array and converting\n   * the definitions into keyframes and offsets with absolute time values.\n   * These will later get converted into relative offsets in a second pass.\n   */\n  for (let i = 0; i < sequence.length; i++) {\n    const segment = sequence[i];\n    /**\n     * If this is a timeline label, mark it and skip the rest of this iteration.\n     */\n    if (typeof segment === \"string\") {\n      timeLabels.set(segment, currentTime);\n      continue;\n    } else if (!Array.isArray(segment)) {\n      timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n      continue;\n    }\n    let [subject, keyframes, transition = {}] = segment;\n    /**\n     * If a relative or absolute time value has been specified we need to resolve\n     * it in relation to the currentTime.\n     */\n    if (transition.at !== undefined) {\n      currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n    }\n    /**\n     * Keep track of the maximum duration in this definition. This will be\n     * applied to currentTime once the definition has been parsed.\n     */\n    let maxDuration = 0;\n    const resolveValueSequence = (valueKeyframes, valueTransition, valueSequence, elementIndex = 0, numSubjects = 0) => {\n      const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n      const {\n        delay = 0,\n        times = defaultOffset(valueKeyframesAsList),\n        type = \"keyframes\",\n        repeat,\n        repeatType,\n        repeatDelay = 0,\n        ...remainingTransition\n      } = valueTransition;\n      let {\n        ease = defaultTransition.ease || \"easeOut\",\n        duration\n      } = valueTransition;\n      /**\n       * Resolve stagger() if defined.\n       */\n      const calculatedDelay = typeof delay === \"function\" ? delay(elementIndex, numSubjects) : delay;\n      /**\n       * If this animation should and can use a spring, generate a spring easing function.\n       */\n      const numKeyframes = valueKeyframesAsList.length;\n      const createGenerator = isGenerator(type) ? type : generators?.[type || \"keyframes\"];\n      if (numKeyframes <= 2 && createGenerator) {\n        /**\n         * As we're creating an easing function from a spring,\n         * ideally we want to generate it using the real distance\n         * between the two keyframes. However this isn't always\n         * possible - in these situations we use 0-100.\n         */\n        let absoluteDelta = 100;\n        if (numKeyframes === 2 && isNumberKeyframesArray(valueKeyframesAsList)) {\n          const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n          absoluteDelta = Math.abs(delta);\n        }\n        const springTransition = {\n          ...remainingTransition\n        };\n        if (duration !== undefined) {\n          springTransition.duration = secondsToMilliseconds(duration);\n        }\n        const springEasing = createGeneratorEasing(springTransition, absoluteDelta, createGenerator);\n        ease = springEasing.ease;\n        duration = springEasing.duration;\n      }\n      duration ?? (duration = defaultDuration);\n      const startTime = currentTime + calculatedDelay;\n      /**\n       * If there's only one time offset of 0, fill in a second with length 1\n       */\n      if (times.length === 1 && times[0] === 0) {\n        times[1] = 1;\n      }\n      /**\n       * Fill out if offset if fewer offsets than keyframes\n       */\n      const remainder = times.length - valueKeyframesAsList.length;\n      remainder > 0 && fillOffset(times, remainder);\n      /**\n       * If only one value has been set, ie [1], push a null to the start of\n       * the keyframe array. This will let us mark a keyframe at this point\n       * that will later be hydrated with the previous value.\n       */\n      valueKeyframesAsList.length === 1 && valueKeyframesAsList.unshift(null);\n      /**\n       * Handle repeat options\n       */\n      if (repeat) {\n        invariant(repeat < MAX_REPEAT, \"Repeat count too high, must be less than 20\");\n        duration = calculateRepeatDuration(duration, repeat);\n        const originalKeyframes = [...valueKeyframesAsList];\n        const originalTimes = [...times];\n        ease = Array.isArray(ease) ? [...ease] : [ease];\n        const originalEase = [...ease];\n        for (let repeatIndex = 0; repeatIndex < repeat; repeatIndex++) {\n          valueKeyframesAsList.push(...originalKeyframes);\n          for (let keyframeIndex = 0; keyframeIndex < originalKeyframes.length; keyframeIndex++) {\n            times.push(originalTimes[keyframeIndex] + (repeatIndex + 1));\n            ease.push(keyframeIndex === 0 ? \"linear\" : getEasingForSegment(originalEase, keyframeIndex - 1));\n          }\n        }\n        normalizeTimes(times, repeat);\n      }\n      const targetTime = startTime + duration;\n      /**\n       * Add keyframes, mapping offsets to absolute time.\n       */\n      addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n      maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n      totalDuration = Math.max(targetTime, totalDuration);\n    };\n    if (isMotionValue(subject)) {\n      const subjectSequence = getSubjectSequence(subject, sequences);\n      resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n    } else {\n      const subjects = resolveSubjects(subject, keyframes, scope, elementCache);\n      const numSubjects = subjects.length;\n      /**\n       * For every element in this segment, process the defined values.\n       */\n      for (let subjectIndex = 0; subjectIndex < numSubjects; subjectIndex++) {\n        /**\n         * Cast necessary, but we know these are of this type\n         */\n        keyframes = keyframes;\n        transition = transition;\n        const thisSubject = subjects[subjectIndex];\n        const subjectSequence = getSubjectSequence(thisSubject, sequences);\n        for (const key in keyframes) {\n          resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), subjectIndex, numSubjects);\n        }\n      }\n    }\n    prevTime = currentTime;\n    currentTime += maxDuration;\n  }\n  /**\n   * For every element and value combination create a new animation.\n   */\n  sequences.forEach((valueSequences, element) => {\n    for (const key in valueSequences) {\n      const valueSequence = valueSequences[key];\n      /**\n       * Arrange all the keyframes in ascending time order.\n       */\n      valueSequence.sort(compareByTime);\n      const keyframes = [];\n      const valueOffset = [];\n      const valueEasing = [];\n      /**\n       * For each keyframe, translate absolute times into\n       * relative offsets based on the total duration of the timeline.\n       */\n      for (let i = 0; i < valueSequence.length; i++) {\n        const {\n          at,\n          value,\n          easing\n        } = valueSequence[i];\n        keyframes.push(value);\n        valueOffset.push(progress(0, totalDuration, at));\n        valueEasing.push(easing || \"easeOut\");\n      }\n      /**\n       * If the first keyframe doesn't land on offset: 0\n       * provide one by duplicating the initial keyframe. This ensures\n       * it snaps to the first keyframe when the animation starts.\n       */\n      if (valueOffset[0] !== 0) {\n        valueOffset.unshift(0);\n        keyframes.unshift(keyframes[0]);\n        valueEasing.unshift(defaultSegmentEasing);\n      }\n      /**\n       * If the last keyframe doesn't land on offset: 1\n       * provide one with a null wildcard value. This will ensure it\n       * stays static until the end of the animation.\n       */\n      if (valueOffset[valueOffset.length - 1] !== 1) {\n        valueOffset.push(1);\n        keyframes.push(null);\n      }\n      if (!animationDefinitions.has(element)) {\n        animationDefinitions.set(element, {\n          keyframes: {},\n          transition: {}\n        });\n      }\n      const definition = animationDefinitions.get(element);\n      definition.keyframes[key] = keyframes;\n      definition.transition[key] = {\n        ...defaultTransition,\n        duration: totalDuration,\n        ease: valueEasing,\n        times: valueOffset,\n        ...sequenceTransition\n      };\n    }\n  });\n  return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n  !sequences.has(subject) && sequences.set(subject, {});\n  return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n  if (!sequences[name]) sequences[name] = [];\n  return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n  return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n  return transition && transition[key] ? {\n    ...transition,\n    ...transition[key]\n  } : {\n    ...transition\n  };\n}\nconst isNumber = keyframe => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = keyframes => keyframes.every(isNumber);\nexport { createAnimationsFromSequence, getValueTransition };", "map": {"version": 3, "names": ["isMotionValue", "defaultOffset", "isGenerator", "createGeneratorEasing", "fillOffset", "progress", "secondsToMilliseconds", "invariant", "getEasingForSegment", "resolveSubjects", "calculateRepeatDuration", "calcNextTime", "addKeyframes", "normalizeTimes", "compareByTime", "defaultSegmentEasing", "MAX_REPEAT", "createAnimationsFromSequence", "sequence", "defaultTransition", "sequenceTransition", "scope", "generators", "defaultDuration", "duration", "animationDefinitions", "Map", "sequences", "elementCache", "time<PERSON><PERSON><PERSON>", "prevTime", "currentTime", "totalDuration", "i", "length", "segment", "set", "Array", "isArray", "name", "at", "subject", "keyframes", "transition", "undefined", "maxDuration", "resolveValueSequence", "valueKeyframes", "valueTransition", "valueSequence", "elementIndex", "numSubjects", "valueKeyframesAsList", "keyframesAsList", "delay", "times", "type", "repeat", "repeatType", "repeatDelay", "remainingTransition", "ease", "calculatedDelay", "numKeyframes", "createGenerator", "absoluteDelta", "isNumberKeyframesArray", "delta", "Math", "abs", "springTransition", "springEasing", "startTime", "remainder", "unshift", "originalKeyframes", "originalTimes", "originalEase", "repeatIndex", "push", "keyframeIndex", "targetTime", "max", "subjectSequence", "getSubjectSequence", "getValueSequence", "subjects", "subjectIndex", "thisSubject", "key", "getValueTransition", "for<PERSON>ach", "valueSequences", "element", "sort", "valueOffset", "valueEasing", "value", "easing", "has", "definition", "get", "isNumber", "keyframe", "every"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/animation/sequence/create.mjs"], "sourcesContent": ["import { isMotionValue, defaultOffset, isGenerator, createGeneratorEasing, fillOffset } from 'motion-dom';\nimport { progress, secondsToMilliseconds, invariant, getEasingForSegment } from 'motion-utils';\nimport { resolveSubjects } from '../animate/resolve-subjects.mjs';\nimport { calculateRepeatDuration } from './utils/calc-repeat-duration.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { normalizeTimes } from './utils/normalize-times.mjs';\nimport { compareByTime } from './utils/sort.mjs';\n\nconst defaultSegmentEasing = \"easeInOut\";\nconst MAX_REPEAT = 20;\nfunction createAnimationsFromSequence(sequence, { defaultTransition = {}, ...sequenceTransition } = {}, scope, generators) {\n    const defaultDuration = defaultTransition.duration || 0.3;\n    const animationDefinitions = new Map();\n    const sequences = new Map();\n    const elementCache = {};\n    const timeLabels = new Map();\n    let prevTime = 0;\n    let currentTime = 0;\n    let totalDuration = 0;\n    /**\n     * Build the timeline by mapping over the sequence array and converting\n     * the definitions into keyframes and offsets with absolute time values.\n     * These will later get converted into relative offsets in a second pass.\n     */\n    for (let i = 0; i < sequence.length; i++) {\n        const segment = sequence[i];\n        /**\n         * If this is a timeline label, mark it and skip the rest of this iteration.\n         */\n        if (typeof segment === \"string\") {\n            timeLabels.set(segment, currentTime);\n            continue;\n        }\n        else if (!Array.isArray(segment)) {\n            timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n            continue;\n        }\n        let [subject, keyframes, transition = {}] = segment;\n        /**\n         * If a relative or absolute time value has been specified we need to resolve\n         * it in relation to the currentTime.\n         */\n        if (transition.at !== undefined) {\n            currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n        }\n        /**\n         * Keep track of the maximum duration in this definition. This will be\n         * applied to currentTime once the definition has been parsed.\n         */\n        let maxDuration = 0;\n        const resolveValueSequence = (valueKeyframes, valueTransition, valueSequence, elementIndex = 0, numSubjects = 0) => {\n            const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n            const { delay = 0, times = defaultOffset(valueKeyframesAsList), type = \"keyframes\", repeat, repeatType, repeatDelay = 0, ...remainingTransition } = valueTransition;\n            let { ease = defaultTransition.ease || \"easeOut\", duration } = valueTransition;\n            /**\n             * Resolve stagger() if defined.\n             */\n            const calculatedDelay = typeof delay === \"function\"\n                ? delay(elementIndex, numSubjects)\n                : delay;\n            /**\n             * If this animation should and can use a spring, generate a spring easing function.\n             */\n            const numKeyframes = valueKeyframesAsList.length;\n            const createGenerator = isGenerator(type)\n                ? type\n                : generators?.[type || \"keyframes\"];\n            if (numKeyframes <= 2 && createGenerator) {\n                /**\n                 * As we're creating an easing function from a spring,\n                 * ideally we want to generate it using the real distance\n                 * between the two keyframes. However this isn't always\n                 * possible - in these situations we use 0-100.\n                 */\n                let absoluteDelta = 100;\n                if (numKeyframes === 2 &&\n                    isNumberKeyframesArray(valueKeyframesAsList)) {\n                    const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n                    absoluteDelta = Math.abs(delta);\n                }\n                const springTransition = { ...remainingTransition };\n                if (duration !== undefined) {\n                    springTransition.duration = secondsToMilliseconds(duration);\n                }\n                const springEasing = createGeneratorEasing(springTransition, absoluteDelta, createGenerator);\n                ease = springEasing.ease;\n                duration = springEasing.duration;\n            }\n            duration ?? (duration = defaultDuration);\n            const startTime = currentTime + calculatedDelay;\n            /**\n             * If there's only one time offset of 0, fill in a second with length 1\n             */\n            if (times.length === 1 && times[0] === 0) {\n                times[1] = 1;\n            }\n            /**\n             * Fill out if offset if fewer offsets than keyframes\n             */\n            const remainder = times.length - valueKeyframesAsList.length;\n            remainder > 0 && fillOffset(times, remainder);\n            /**\n             * If only one value has been set, ie [1], push a null to the start of\n             * the keyframe array. This will let us mark a keyframe at this point\n             * that will later be hydrated with the previous value.\n             */\n            valueKeyframesAsList.length === 1 &&\n                valueKeyframesAsList.unshift(null);\n            /**\n             * Handle repeat options\n             */\n            if (repeat) {\n                invariant(repeat < MAX_REPEAT, \"Repeat count too high, must be less than 20\");\n                duration = calculateRepeatDuration(duration, repeat);\n                const originalKeyframes = [...valueKeyframesAsList];\n                const originalTimes = [...times];\n                ease = Array.isArray(ease) ? [...ease] : [ease];\n                const originalEase = [...ease];\n                for (let repeatIndex = 0; repeatIndex < repeat; repeatIndex++) {\n                    valueKeyframesAsList.push(...originalKeyframes);\n                    for (let keyframeIndex = 0; keyframeIndex < originalKeyframes.length; keyframeIndex++) {\n                        times.push(originalTimes[keyframeIndex] + (repeatIndex + 1));\n                        ease.push(keyframeIndex === 0\n                            ? \"linear\"\n                            : getEasingForSegment(originalEase, keyframeIndex - 1));\n                    }\n                }\n                normalizeTimes(times, repeat);\n            }\n            const targetTime = startTime + duration;\n            /**\n             * Add keyframes, mapping offsets to absolute time.\n             */\n            addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n            maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n            totalDuration = Math.max(targetTime, totalDuration);\n        };\n        if (isMotionValue(subject)) {\n            const subjectSequence = getSubjectSequence(subject, sequences);\n            resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n        }\n        else {\n            const subjects = resolveSubjects(subject, keyframes, scope, elementCache);\n            const numSubjects = subjects.length;\n            /**\n             * For every element in this segment, process the defined values.\n             */\n            for (let subjectIndex = 0; subjectIndex < numSubjects; subjectIndex++) {\n                /**\n                 * Cast necessary, but we know these are of this type\n                 */\n                keyframes = keyframes;\n                transition = transition;\n                const thisSubject = subjects[subjectIndex];\n                const subjectSequence = getSubjectSequence(thisSubject, sequences);\n                for (const key in keyframes) {\n                    resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), subjectIndex, numSubjects);\n                }\n            }\n        }\n        prevTime = currentTime;\n        currentTime += maxDuration;\n    }\n    /**\n     * For every element and value combination create a new animation.\n     */\n    sequences.forEach((valueSequences, element) => {\n        for (const key in valueSequences) {\n            const valueSequence = valueSequences[key];\n            /**\n             * Arrange all the keyframes in ascending time order.\n             */\n            valueSequence.sort(compareByTime);\n            const keyframes = [];\n            const valueOffset = [];\n            const valueEasing = [];\n            /**\n             * For each keyframe, translate absolute times into\n             * relative offsets based on the total duration of the timeline.\n             */\n            for (let i = 0; i < valueSequence.length; i++) {\n                const { at, value, easing } = valueSequence[i];\n                keyframes.push(value);\n                valueOffset.push(progress(0, totalDuration, at));\n                valueEasing.push(easing || \"easeOut\");\n            }\n            /**\n             * If the first keyframe doesn't land on offset: 0\n             * provide one by duplicating the initial keyframe. This ensures\n             * it snaps to the first keyframe when the animation starts.\n             */\n            if (valueOffset[0] !== 0) {\n                valueOffset.unshift(0);\n                keyframes.unshift(keyframes[0]);\n                valueEasing.unshift(defaultSegmentEasing);\n            }\n            /**\n             * If the last keyframe doesn't land on offset: 1\n             * provide one with a null wildcard value. This will ensure it\n             * stays static until the end of the animation.\n             */\n            if (valueOffset[valueOffset.length - 1] !== 1) {\n                valueOffset.push(1);\n                keyframes.push(null);\n            }\n            if (!animationDefinitions.has(element)) {\n                animationDefinitions.set(element, {\n                    keyframes: {},\n                    transition: {},\n                });\n            }\n            const definition = animationDefinitions.get(element);\n            definition.keyframes[key] = keyframes;\n            definition.transition[key] = {\n                ...defaultTransition,\n                duration: totalDuration,\n                ease: valueEasing,\n                times: valueOffset,\n                ...sequenceTransition,\n            };\n        }\n    });\n    return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n    !sequences.has(subject) && sequences.set(subject, {});\n    return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n    if (!sequences[name])\n        sequences[name] = [];\n    return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n    return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n    return transition && transition[key]\n        ? {\n            ...transition,\n            ...transition[key],\n        }\n        : { ...transition };\n}\nconst isNumber = (keyframe) => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = (keyframes) => keyframes.every(isNumber);\n\nexport { createAnimationsFromSequence, getValueTransition };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,aAAa,EAAEC,WAAW,EAAEC,qBAAqB,EAAEC,UAAU,QAAQ,YAAY;AACzG,SAASC,QAAQ,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,mBAAmB,QAAQ,cAAc;AAC9F,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,aAAa,QAAQ,kBAAkB;AAEhD,MAAMC,oBAAoB,GAAG,WAAW;AACxC,MAAMC,UAAU,GAAG,EAAE;AACrB,SAASC,4BAA4BA,CAACC,QAAQ,EAAE;EAAEC,iBAAiB,GAAG,CAAC,CAAC;EAAE,GAAGC;AAAmB,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACvH,MAAMC,eAAe,GAAGJ,iBAAiB,CAACK,QAAQ,IAAI,GAAG;EACzD,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtC,MAAMC,SAAS,GAAG,IAAID,GAAG,CAAC,CAAC;EAC3B,MAAME,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMC,UAAU,GAAG,IAAIH,GAAG,CAAC,CAAC;EAC5B,IAAII,QAAQ,GAAG,CAAC;EAChB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,aAAa,GAAG,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,QAAQ,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,MAAME,OAAO,GAAGjB,QAAQ,CAACe,CAAC,CAAC;IAC3B;AACR;AACA;IACQ,IAAI,OAAOE,OAAO,KAAK,QAAQ,EAAE;MAC7BN,UAAU,CAACO,GAAG,CAACD,OAAO,EAAEJ,WAAW,CAAC;MACpC;IACJ,CAAC,MACI,IAAI,CAACM,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;MAC9BN,UAAU,CAACO,GAAG,CAACD,OAAO,CAACI,IAAI,EAAE5B,YAAY,CAACoB,WAAW,EAAEI,OAAO,CAACK,EAAE,EAAEV,QAAQ,EAAED,UAAU,CAAC,CAAC;MACzF;IACJ;IACA,IAAI,CAACY,OAAO,EAAEC,SAAS,EAAEC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAGR,OAAO;IACnD;AACR;AACA;AACA;IACQ,IAAIQ,UAAU,CAACH,EAAE,KAAKI,SAAS,EAAE;MAC7Bb,WAAW,GAAGpB,YAAY,CAACoB,WAAW,EAAEY,UAAU,CAACH,EAAE,EAAEV,QAAQ,EAAED,UAAU,CAAC;IAChF;IACA;AACR;AACA;AACA;IACQ,IAAIgB,WAAW,GAAG,CAAC;IACnB,MAAMC,oBAAoB,GAAGA,CAACC,cAAc,EAAEC,eAAe,EAAEC,aAAa,EAAEC,YAAY,GAAG,CAAC,EAAEC,WAAW,GAAG,CAAC,KAAK;MAChH,MAAMC,oBAAoB,GAAGC,eAAe,CAACN,cAAc,CAAC;MAC5D,MAAM;QAAEO,KAAK,GAAG,CAAC;QAAEC,KAAK,GAAGtD,aAAa,CAACmD,oBAAoB,CAAC;QAAEI,IAAI,GAAG,WAAW;QAAEC,MAAM;QAAEC,UAAU;QAAEC,WAAW,GAAG,CAAC;QAAE,GAAGC;MAAoB,CAAC,GAAGZ,eAAe;MACnK,IAAI;QAAEa,IAAI,GAAG1C,iBAAiB,CAAC0C,IAAI,IAAI,SAAS;QAAErC;MAAS,CAAC,GAAGwB,eAAe;MAC9E;AACZ;AACA;MACY,MAAMc,eAAe,GAAG,OAAOR,KAAK,KAAK,UAAU,GAC7CA,KAAK,CAACJ,YAAY,EAAEC,WAAW,CAAC,GAChCG,KAAK;MACX;AACZ;AACA;MACY,MAAMS,YAAY,GAAGX,oBAAoB,CAAClB,MAAM;MAChD,MAAM8B,eAAe,GAAG9D,WAAW,CAACsD,IAAI,CAAC,GACnCA,IAAI,GACJlC,UAAU,GAAGkC,IAAI,IAAI,WAAW,CAAC;MACvC,IAAIO,YAAY,IAAI,CAAC,IAAIC,eAAe,EAAE;QACtC;AAChB;AACA;AACA;AACA;AACA;QACgB,IAAIC,aAAa,GAAG,GAAG;QACvB,IAAIF,YAAY,KAAK,CAAC,IAClBG,sBAAsB,CAACd,oBAAoB,CAAC,EAAE;UAC9C,MAAMe,KAAK,GAAGf,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC;UAC/Da,aAAa,GAAGG,IAAI,CAACC,GAAG,CAACF,KAAK,CAAC;QACnC;QACA,MAAMG,gBAAgB,GAAG;UAAE,GAAGV;QAAoB,CAAC;QACnD,IAAIpC,QAAQ,KAAKoB,SAAS,EAAE;UACxB0B,gBAAgB,CAAC9C,QAAQ,GAAGlB,qBAAqB,CAACkB,QAAQ,CAAC;QAC/D;QACA,MAAM+C,YAAY,GAAGpE,qBAAqB,CAACmE,gBAAgB,EAAEL,aAAa,EAAED,eAAe,CAAC;QAC5FH,IAAI,GAAGU,YAAY,CAACV,IAAI;QACxBrC,QAAQ,GAAG+C,YAAY,CAAC/C,QAAQ;MACpC;MACAA,QAAQ,KAAKA,QAAQ,GAAGD,eAAe,CAAC;MACxC,MAAMiD,SAAS,GAAGzC,WAAW,GAAG+B,eAAe;MAC/C;AACZ;AACA;MACY,IAAIP,KAAK,CAACrB,MAAM,KAAK,CAAC,IAAIqB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtCA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;MAChB;MACA;AACZ;AACA;MACY,MAAMkB,SAAS,GAAGlB,KAAK,CAACrB,MAAM,GAAGkB,oBAAoB,CAAClB,MAAM;MAC5DuC,SAAS,GAAG,CAAC,IAAIrE,UAAU,CAACmD,KAAK,EAAEkB,SAAS,CAAC;MAC7C;AACZ;AACA;AACA;AACA;MACYrB,oBAAoB,CAAClB,MAAM,KAAK,CAAC,IAC7BkB,oBAAoB,CAACsB,OAAO,CAAC,IAAI,CAAC;MACtC;AACZ;AACA;MACY,IAAIjB,MAAM,EAAE;QACRlD,SAAS,CAACkD,MAAM,GAAGzC,UAAU,EAAE,6CAA6C,CAAC;QAC7EQ,QAAQ,GAAGd,uBAAuB,CAACc,QAAQ,EAAEiC,MAAM,CAAC;QACpD,MAAMkB,iBAAiB,GAAG,CAAC,GAAGvB,oBAAoB,CAAC;QACnD,MAAMwB,aAAa,GAAG,CAAC,GAAGrB,KAAK,CAAC;QAChCM,IAAI,GAAGxB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC,GAAG,CAAC,GAAGA,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC;QAC/C,MAAMgB,YAAY,GAAG,CAAC,GAAGhB,IAAI,CAAC;QAC9B,KAAK,IAAIiB,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGrB,MAAM,EAAEqB,WAAW,EAAE,EAAE;UAC3D1B,oBAAoB,CAAC2B,IAAI,CAAC,GAAGJ,iBAAiB,CAAC;UAC/C,KAAK,IAAIK,aAAa,GAAG,CAAC,EAAEA,aAAa,GAAGL,iBAAiB,CAACzC,MAAM,EAAE8C,aAAa,EAAE,EAAE;YACnFzB,KAAK,CAACwB,IAAI,CAACH,aAAa,CAACI,aAAa,CAAC,IAAIF,WAAW,GAAG,CAAC,CAAC,CAAC;YAC5DjB,IAAI,CAACkB,IAAI,CAACC,aAAa,KAAK,CAAC,GACvB,QAAQ,GACRxE,mBAAmB,CAACqE,YAAY,EAAEG,aAAa,GAAG,CAAC,CAAC,CAAC;UAC/D;QACJ;QACAnE,cAAc,CAAC0C,KAAK,EAAEE,MAAM,CAAC;MACjC;MACA,MAAMwB,UAAU,GAAGT,SAAS,GAAGhD,QAAQ;MACvC;AACZ;AACA;MACYZ,YAAY,CAACqC,aAAa,EAAEG,oBAAoB,EAAES,IAAI,EAAEN,KAAK,EAAEiB,SAAS,EAAES,UAAU,CAAC;MACrFpC,WAAW,GAAGuB,IAAI,CAACc,GAAG,CAACpB,eAAe,GAAGtC,QAAQ,EAAEqB,WAAW,CAAC;MAC/Db,aAAa,GAAGoC,IAAI,CAACc,GAAG,CAACD,UAAU,EAAEjD,aAAa,CAAC;IACvD,CAAC;IACD,IAAIhC,aAAa,CAACyC,OAAO,CAAC,EAAE;MACxB,MAAM0C,eAAe,GAAGC,kBAAkB,CAAC3C,OAAO,EAAEd,SAAS,CAAC;MAC9DmB,oBAAoB,CAACJ,SAAS,EAAEC,UAAU,EAAE0C,gBAAgB,CAAC,SAAS,EAAEF,eAAe,CAAC,CAAC;IAC7F,CAAC,MACI;MACD,MAAMG,QAAQ,GAAG7E,eAAe,CAACgC,OAAO,EAAEC,SAAS,EAAErB,KAAK,EAAEO,YAAY,CAAC;MACzE,MAAMuB,WAAW,GAAGmC,QAAQ,CAACpD,MAAM;MACnC;AACZ;AACA;MACY,KAAK,IAAIqD,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAGpC,WAAW,EAAEoC,YAAY,EAAE,EAAE;QACnE;AAChB;AACA;QACgB7C,SAAS,GAAGA,SAAS;QACrBC,UAAU,GAAGA,UAAU;QACvB,MAAM6C,WAAW,GAAGF,QAAQ,CAACC,YAAY,CAAC;QAC1C,MAAMJ,eAAe,GAAGC,kBAAkB,CAACI,WAAW,EAAE7D,SAAS,CAAC;QAClE,KAAK,MAAM8D,GAAG,IAAI/C,SAAS,EAAE;UACzBI,oBAAoB,CAACJ,SAAS,CAAC+C,GAAG,CAAC,EAAEC,kBAAkB,CAAC/C,UAAU,EAAE8C,GAAG,CAAC,EAAEJ,gBAAgB,CAACI,GAAG,EAAEN,eAAe,CAAC,EAAEI,YAAY,EAAEpC,WAAW,CAAC;QAChJ;MACJ;IACJ;IACArB,QAAQ,GAAGC,WAAW;IACtBA,WAAW,IAAIc,WAAW;EAC9B;EACA;AACJ;AACA;EACIlB,SAAS,CAACgE,OAAO,CAAC,CAACC,cAAc,EAAEC,OAAO,KAAK;IAC3C,KAAK,MAAMJ,GAAG,IAAIG,cAAc,EAAE;MAC9B,MAAM3C,aAAa,GAAG2C,cAAc,CAACH,GAAG,CAAC;MACzC;AACZ;AACA;MACYxC,aAAa,CAAC6C,IAAI,CAAChF,aAAa,CAAC;MACjC,MAAM4B,SAAS,GAAG,EAAE;MACpB,MAAMqD,WAAW,GAAG,EAAE;MACtB,MAAMC,WAAW,GAAG,EAAE;MACtB;AACZ;AACA;AACA;MACY,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,aAAa,CAACf,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3C,MAAM;UAAEO,EAAE;UAAEyD,KAAK;UAAEC;QAAO,CAAC,GAAGjD,aAAa,CAAChB,CAAC,CAAC;QAC9CS,SAAS,CAACqC,IAAI,CAACkB,KAAK,CAAC;QACrBF,WAAW,CAAChB,IAAI,CAAC1E,QAAQ,CAAC,CAAC,EAAE2B,aAAa,EAAEQ,EAAE,CAAC,CAAC;QAChDwD,WAAW,CAACjB,IAAI,CAACmB,MAAM,IAAI,SAAS,CAAC;MACzC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtBA,WAAW,CAACrB,OAAO,CAAC,CAAC,CAAC;QACtBhC,SAAS,CAACgC,OAAO,CAAChC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/BsD,WAAW,CAACtB,OAAO,CAAC3D,oBAAoB,CAAC;MAC7C;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIgF,WAAW,CAACA,WAAW,CAAC7D,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAC3C6D,WAAW,CAAChB,IAAI,CAAC,CAAC,CAAC;QACnBrC,SAAS,CAACqC,IAAI,CAAC,IAAI,CAAC;MACxB;MACA,IAAI,CAACtD,oBAAoB,CAAC0E,GAAG,CAACN,OAAO,CAAC,EAAE;QACpCpE,oBAAoB,CAACW,GAAG,CAACyD,OAAO,EAAE;UAC9BnD,SAAS,EAAE,CAAC,CAAC;UACbC,UAAU,EAAE,CAAC;QACjB,CAAC,CAAC;MACN;MACA,MAAMyD,UAAU,GAAG3E,oBAAoB,CAAC4E,GAAG,CAACR,OAAO,CAAC;MACpDO,UAAU,CAAC1D,SAAS,CAAC+C,GAAG,CAAC,GAAG/C,SAAS;MACrC0D,UAAU,CAACzD,UAAU,CAAC8C,GAAG,CAAC,GAAG;QACzB,GAAGtE,iBAAiB;QACpBK,QAAQ,EAAEQ,aAAa;QACvB6B,IAAI,EAAEmC,WAAW;QACjBzC,KAAK,EAAEwC,WAAW;QAClB,GAAG3E;MACP,CAAC;IACL;EACJ,CAAC,CAAC;EACF,OAAOK,oBAAoB;AAC/B;AACA,SAAS2D,kBAAkBA,CAAC3C,OAAO,EAAEd,SAAS,EAAE;EAC5C,CAACA,SAAS,CAACwE,GAAG,CAAC1D,OAAO,CAAC,IAAId,SAAS,CAACS,GAAG,CAACK,OAAO,EAAE,CAAC,CAAC,CAAC;EACrD,OAAOd,SAAS,CAAC0E,GAAG,CAAC5D,OAAO,CAAC;AACjC;AACA,SAAS4C,gBAAgBA,CAAC9C,IAAI,EAAEZ,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,CAACY,IAAI,CAAC,EAChBZ,SAAS,CAACY,IAAI,CAAC,GAAG,EAAE;EACxB,OAAOZ,SAAS,CAACY,IAAI,CAAC;AAC1B;AACA,SAASc,eAAeA,CAACX,SAAS,EAAE;EAChC,OAAOL,KAAK,CAACC,OAAO,CAACI,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;AAC7D;AACA,SAASgD,kBAAkBA,CAAC/C,UAAU,EAAE8C,GAAG,EAAE;EACzC,OAAO9C,UAAU,IAAIA,UAAU,CAAC8C,GAAG,CAAC,GAC9B;IACE,GAAG9C,UAAU;IACb,GAAGA,UAAU,CAAC8C,GAAG;EACrB,CAAC,GACC;IAAE,GAAG9C;EAAW,CAAC;AAC3B;AACA,MAAM2D,QAAQ,GAAIC,QAAQ,IAAK,OAAOA,QAAQ,KAAK,QAAQ;AAC3D,MAAMrC,sBAAsB,GAAIxB,SAAS,IAAKA,SAAS,CAAC8D,KAAK,CAACF,QAAQ,CAAC;AAEvE,SAASrF,4BAA4B,EAAEyE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}