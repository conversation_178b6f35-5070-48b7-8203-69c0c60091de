{"ast": null, "code": "function isObject(value) {\n  return typeof value === \"object\" && value !== null;\n}\nexport { isObject };", "map": {"version": 3, "names": ["isObject", "value"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-utils/dist/es/is-object.mjs"], "sourcesContent": ["function isObject(value) {\n    return typeof value === \"object\" && value !== null;\n}\n\nexport { isObject };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;AACtD;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}