{"ast": null, "code": "import { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\nconst circIn = p => 1 - Math.sin(Math.acos(p));\nconst circOut = reverseEasing(circIn);\nconst circInOut = mirrorEasing(circIn);\nexport { circIn, circInOut, circOut };", "map": {"version": 3, "names": ["mirrorEasing", "reverseEasing", "circIn", "p", "Math", "sin", "acos", "circOut", "circInOut"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-utils/dist/es/easing/circ.mjs"], "sourcesContent": ["import { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst circIn = (p) => 1 - Math.sin(Math.acos(p));\nconst circOut = reverseEasing(circIn);\nconst circInOut = mirrorEasing(circIn);\n\nexport { circIn, circInOut, circOut };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AAEvD,MAAMC,MAAM,GAAIC,CAAC,IAAK,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,IAAI,CAACH,CAAC,CAAC,CAAC;AAChD,MAAMI,OAAO,GAAGN,aAAa,CAACC,MAAM,CAAC;AACrC,MAAMM,SAAS,GAAGR,YAAY,CAACE,MAAM,CAAC;AAEtC,SAASA,MAAM,EAAEM,SAAS,EAAED,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}