import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, But<PERSON>, Al<PERSON>, Spinner, Nav } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { UserPlus, GraduationCap, Store } from 'lucide-react';
import { useAuth, StudentRegistrationData, XeroxCenterRegistrationData } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import Layout from './Layout';

const Register: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'student' | 'xerox'>('student');
  const [studentData, setStudentData] = useState<StudentRegistrationData>({
    username: '',
    email: '',
    password: '',
    studentNumber: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    department: '',
    year: undefined
  });
  const [xeroxData, setXeroxData] = useState<XeroxCenterRegistrationData>({
    username: '',
    email: '',
    password: '',
    xeroxCenterName: '',
    location: '',
    contactPerson: '',
    phoneNumber: '',
    description: ''
  });

  const { registerStudent, registerXeroxCenter, isLoading, error, user } = useAuth();
  const { isDarkMode } = useTheme();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const handleStudentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await registerStudent(studentData);
    if (success) {
      navigate('/dashboard');
    }
  };

  const handleXeroxSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await registerXeroxCenter(xeroxData);
    if (success) {
      navigate('/dashboard');
    }
  };

  const handleStudentChange = (field: keyof StudentRegistrationData, value: string | number | undefined) => {
    setStudentData(prev => ({ ...prev, [field]: value }));
  };

  const handleXeroxChange = (field: keyof XeroxCenterRegistrationData, value: string) => {
    setXeroxData(prev => ({ ...prev, [field]: value }));
  };



  return (
    <Layout>
      <Container className="d-flex align-items-center justify-content-center py-5" style={{ minHeight: '100vh' }}>
        <Row className="justify-content-center w-100">
          <Col md={8} lg={6}>
            <motion.div
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6 }}
            >
              <Card className={`shadow-lg border-0 ${isDarkMode ? 'bg-dark text-light' : ''}`} style={{ borderRadius: '20px', overflow: 'hidden' }}>
                <div className="position-relative">
                  <div
                    className="w-100 h-100 position-absolute"
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      height: '100px'
                    }}
                  />
                  <Card.Body className="p-4 position-relative">
                    <div className="text-center mb-4">
                      <motion.div
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ delay: 0.3, duration: 0.5 }}
                        className="d-inline-block p-3 rounded-circle bg-white shadow-sm mb-3"
                        style={{ marginTop: '10px' }}
                      >
                        <UserPlus size={32} className="text-primary" />
                      </motion.div>
                      <h2 className="h4 mb-2" style={{ color: isDarkMode ? '#fff' : '#333', marginTop: '40px' }}>Create Account</h2>
                      <p className="text-muted small">Join XeroxHub today</p>
                    </div>

                    <Nav variant="pills" className="mb-4 justify-content-center">
                      <Nav.Item>
                        <Nav.Link
                          active={activeTab === 'student'}
                          onClick={() => setActiveTab('student')}
                          className={`px-4 py-2 mx-1 ${activeTab === 'student' ? 'btn-gradient' : ''}`}
                          style={{
                            borderRadius: '25px',
                            fontWeight: '500',
                            transition: 'all 0.3s ease',
                            background: activeTab === 'student' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',
                            border: activeTab === 'student' ? 'none' : '2px solid #e9ecef',
                            color: activeTab === 'student' ? 'white' : (isDarkMode ? '#fff' : '#333')
                          }}
                        >
                          <GraduationCap size={16} className="me-2" />
                          Student
                        </Nav.Link>
                      </Nav.Item>
                      <Nav.Item>
                        <Nav.Link
                          active={activeTab === 'xerox'}
                          onClick={() => setActiveTab('xerox')}
                          className={`px-4 py-2 mx-1 ${activeTab === 'xerox' ? 'btn-gradient' : ''}`}
                          style={{
                            borderRadius: '25px',
                            fontWeight: '500',
                            transition: 'all 0.3s ease',
                            background: activeTab === 'xerox' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',
                            border: activeTab === 'xerox' ? 'none' : '2px solid #e9ecef',
                            color: activeTab === 'xerox' ? 'white' : (isDarkMode ? '#fff' : '#333')
                          }}
                        >
                          <Store size={16} className="me-2" />
                          Xerox Center
                        </Nav.Link>
                      </Nav.Item>
                    </Nav>

              {error && (
                <Alert variant="danger" className="mb-3">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </Alert>
              )}

              {activeTab === 'student' ? (
                <Form onSubmit={handleStudentSubmit}>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Username</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Enter username"
                          value={studentData.username}
                          onChange={(e) => handleStudentChange('username', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Student Number</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Enter student number"
                          value={studentData.studentNumber}
                          onChange={(e) => handleStudentChange('studentNumber', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Email Address</Form.Label>
                    <Form.Control
                      type="email"
                      placeholder="Enter email"
                      value={studentData.email}
                      onChange={(e) => handleStudentChange('email', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Password</Form.Label>
                    <Form.Control
                      type="password"
                      placeholder="Password"
                      value={studentData.password}
                      onChange={(e) => handleStudentChange('password', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>First Name</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="First name"
                          value={studentData.firstName}
                          onChange={(e) => handleStudentChange('firstName', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Last Name</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Last name"
                          value={studentData.lastName}
                          onChange={(e) => handleStudentChange('lastName', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Department</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Department (optional)"
                          value={studentData.department}
                          onChange={(e) => handleStudentChange('department', e.target.value)}
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Year</Form.Label>
                        <Form.Control
                          type="number"
                          placeholder="Year (optional)"
                          value={studentData.year || ''}
                          onChange={(e) => handleStudentChange('year', e.target.value ? parseInt(e.target.value) : undefined)}
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Phone Number</Form.Label>
                    <Form.Control
                      type="tel"
                      placeholder="Phone number (optional)"
                      value={studentData.phoneNumber}
                      onChange={(e) => handleStudentChange('phoneNumber', e.target.value)}
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Button 
                    variant="primary" 
                    type="submit" 
                    className="w-100 mb-3"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Creating Account...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-user-plus me-2"></i>
                        Register as Student
                      </>
                    )}
                  </Button>
                </Form>
              ) : (
                <Form onSubmit={handleXeroxSubmit}>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Username</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Enter username"
                          value={xeroxData.username}
                          onChange={(e) => handleXeroxChange('username', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Xerox Center Name</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Center name"
                          value={xeroxData.xeroxCenterName}
                          onChange={(e) => handleXeroxChange('xeroxCenterName', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Email Address</Form.Label>
                    <Form.Control
                      type="email"
                      placeholder="Enter email"
                      value={xeroxData.email}
                      onChange={(e) => handleXeroxChange('email', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Password</Form.Label>
                    <Form.Control
                      type="password"
                      placeholder="Password"
                      value={xeroxData.password}
                      onChange={(e) => handleXeroxChange('password', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Location</Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="Center location"
                      value={xeroxData.location}
                      onChange={(e) => handleXeroxChange('location', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Contact Person</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Contact person (optional)"
                          value={xeroxData.contactPerson}
                          onChange={(e) => handleXeroxChange('contactPerson', e.target.value)}
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Phone Number</Form.Label>
                        <Form.Control
                          type="tel"
                          placeholder="Phone number"
                          value={xeroxData.phoneNumber}
                          onChange={(e) => handleXeroxChange('phoneNumber', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Description</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      placeholder="Center description (optional)"
                      value={xeroxData.description}
                      onChange={(e) => handleXeroxChange('description', e.target.value)}
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Button 
                    variant="primary" 
                    type="submit" 
                    className="w-100 mb-3"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Creating Account...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-store me-2"></i>
                        Register as Xerox Center
                      </>
                    )}
                  </Button>
                </Form>
              )}

                    <hr style={{ margin: '2rem 0', borderColor: isDarkMode ? '#404040' : '#e9ecef' }} />

                    <div className="text-center">
                      <Link
                        to="/login"
                        className="text-decoration-none"
                        style={{
                          color: '#667eea',
                          fontWeight: '500',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#764ba2'}
                        onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#667eea'}
                      >
                        Already have an account? Login!
                      </Link>
                    </div>
                  </Card.Body>
                </div>
              </Card>
            </motion.div>
          </Col>
        </Row>
      </Container>
    </Layout>
  );
};

export default Register;
