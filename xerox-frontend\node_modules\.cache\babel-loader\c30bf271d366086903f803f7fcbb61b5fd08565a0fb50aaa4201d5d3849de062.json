{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\AceternityLayout.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport FloatingNav from './AceternityNavbar';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { cn } from '../../lib/utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BackgroundBeams = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-blue-500/20 to-transparent animate-pulse\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-0 left-1/2 w-px h-full bg-gradient-to-b from-transparent via-purple-500/20 to-transparent animate-pulse delay-1000\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-0 left-3/4 w-px h-full bg-gradient-to-b from-transparent via-blue-500/20 to-transparent animate-pulse delay-2000\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      animate: {\n        y: [0, -20, 0],\n        opacity: [0.3, 0.8, 0.3]\n      },\n      transition: {\n        duration: 4,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      },\n      className: \"absolute top-1/4 left-1/6 w-32 h-32 bg-blue-500/10 rounded-full blur-xl\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      animate: {\n        y: [0, 20, 0],\n        opacity: [0.2, 0.6, 0.2]\n      },\n      transition: {\n        duration: 5,\n        repeat: Infinity,\n        ease: \"easeInOut\",\n        delay: 1\n      },\n      className: \"absolute top-1/2 right-1/6 w-40 h-40 bg-purple-500/10 rounded-full blur-xl\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      animate: {\n        y: [0, -15, 0],\n        opacity: [0.1, 0.4, 0.1]\n      },\n      transition: {\n        duration: 6,\n        repeat: Infinity,\n        ease: \"easeInOut\",\n        delay: 2\n      },\n      className: \"absolute bottom-1/4 left-1/3 w-24 h-24 bg-indigo-500/10 rounded-full blur-xl\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = BackgroundBeams;\nconst GridPattern = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute inset-0 opacity-20 dark:opacity-10\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0\",\n      style: {\n        backgroundImage: `\n          linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),\n          linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)\n        `,\n        backgroundSize: '50px 50px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n_c2 = GridPattern;\nconst AceternityLayout = ({\n  children,\n  className = ''\n}) => {\n  _s();\n  const {\n    isDarkMode\n  } = useTheme();\n  const {\n    user\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cn(\"min-h-screen transition-all duration-500 relative overflow-hidden\", isDarkMode ? \"bg-gradient-to-br from-gray-900 via-black to-gray-900\" : \"bg-gradient-to-br from-gray-50 via-white to-gray-100\"),\n    children: [/*#__PURE__*/_jsxDEV(GridPattern, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(BackgroundBeams, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(FloatingNav, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 16\n    }, this), /*#__PURE__*/_jsxDEV(motion.main, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.5\n      },\n      className: cn(\"relative z-10\", user ? \"pt-32\" : \"pt-8\", className),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        scale: 0,\n        opacity: 0\n      },\n      animate: {\n        scale: 1,\n        opacity: 1\n      },\n      transition: {\n        delay: 1,\n        duration: 0.3\n      },\n      className: \"fixed bottom-8 right-8 z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.button, {\n        whileHover: {\n          scale: 1.1\n        },\n        whileTap: {\n          scale: 0.9\n        },\n        onClick: () => {\n          if (user.userType === 'Student') {\n            window.location.href = '/upload';\n          } else {\n            window.location.href = '/job-queue';\n          }\n        },\n        className: \"w-14 h-14 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg shadow-blue-500/25 flex items-center justify-center text-white hover:shadow-xl hover:shadow-blue-500/40 transition-all duration-300\",\n        children: user.userType === 'Student' ? /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 4v16m8-8H4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        x: 100\n      },\n      animate: {\n        opacity: 1,\n        x: 0\n      },\n      transition: {\n        delay: 0.5,\n        duration: 0.3\n      },\n      className: \"fixed top-1/2 right-4 transform -translate-y-1/2 z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-12 h-12 rounded-2xl bg-white/10 dark:bg-black/20 backdrop-blur-md border border-white/20 flex items-center justify-center\",\n        children: isDarkMode ? /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5 text-yellow-400\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5 text-blue-600\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(AceternityLayout, \"jAnCuiuX/KWEMNgoC9SL9mKTjeU=\", false, function () {\n  return [useTheme, useAuth];\n});\n_c3 = AceternityLayout;\nexport default AceternityLayout;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"BackgroundBeams\");\n$RefreshReg$(_c2, \"GridPattern\");\n$RefreshReg$(_c3, \"AceternityLayout\");", "map": {"version": 3, "names": ["React", "motion", "FloatingNav", "useTheme", "useAuth", "cn", "jsxDEV", "_jsxDEV", "BackgroundBeams", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "animate", "y", "opacity", "transition", "duration", "repeat", "Infinity", "ease", "delay", "_c", "GridPattern", "style", "backgroundImage", "backgroundSize", "_c2", "AceternityLayout", "_s", "isDarkMode", "user", "main", "initial", "scale", "button", "whileHover", "whileTap", "onClick", "userType", "window", "location", "href", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "x", "fillRule", "clipRule", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/AceternityLayout.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react';\nimport { motion } from 'framer-motion';\nimport FloatingNav from './AceternityNavbar';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { cn } from '../../lib/utils';\n\ninterface AceternityLayoutProps {\n  children: ReactNode;\n  className?: string;\n}\n\nconst BackgroundBeams = () => {\n  return (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n      {/* Animated background beams */}\n      <div className=\"absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-blue-500/20 to-transparent animate-pulse\"></div>\n      <div className=\"absolute top-0 left-1/2 w-px h-full bg-gradient-to-b from-transparent via-purple-500/20 to-transparent animate-pulse delay-1000\"></div>\n      <div className=\"absolute top-0 left-3/4 w-px h-full bg-gradient-to-b from-transparent via-blue-500/20 to-transparent animate-pulse delay-2000\"></div>\n      \n      {/* Floating orbs */}\n      <motion.div\n        animate={{\n          y: [0, -20, 0],\n          opacity: [0.3, 0.8, 0.3],\n        }}\n        transition={{\n          duration: 4,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n        className=\"absolute top-1/4 left-1/6 w-32 h-32 bg-blue-500/10 rounded-full blur-xl\"\n      />\n      <motion.div\n        animate={{\n          y: [0, 20, 0],\n          opacity: [0.2, 0.6, 0.2],\n        }}\n        transition={{\n          duration: 5,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 1,\n        }}\n        className=\"absolute top-1/2 right-1/6 w-40 h-40 bg-purple-500/10 rounded-full blur-xl\"\n      />\n      <motion.div\n        animate={{\n          y: [0, -15, 0],\n          opacity: [0.1, 0.4, 0.1],\n        }}\n        transition={{\n          duration: 6,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 2,\n        }}\n        className=\"absolute bottom-1/4 left-1/3 w-24 h-24 bg-indigo-500/10 rounded-full blur-xl\"\n      />\n    </div>\n  );\n};\n\nconst GridPattern = () => {\n  return (\n    <div className=\"absolute inset-0 opacity-20 dark:opacity-10\">\n      <div className=\"absolute inset-0\" style={{\n        backgroundImage: `\n          linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),\n          linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)\n        `,\n        backgroundSize: '50px 50px'\n      }} />\n    </div>\n  );\n};\n\nconst AceternityLayout: React.FC<AceternityLayoutProps> = ({ children, className = '' }) => {\n  const { isDarkMode } = useTheme();\n  const { user } = useAuth();\n\n  return (\n    <div className={cn(\n      \"min-h-screen transition-all duration-500 relative overflow-hidden\",\n      isDarkMode \n        ? \"bg-gradient-to-br from-gray-900 via-black to-gray-900\" \n        : \"bg-gradient-to-br from-gray-50 via-white to-gray-100\"\n    )}>\n      {/* Background Effects */}\n      <GridPattern />\n      <BackgroundBeams />\n      \n      {/* Navbar */}\n      {user && <FloatingNav />}\n\n      {/* Main Content */}\n      <motion.main\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n        className={cn(\n          \"relative z-10\",\n          user ? \"pt-32\" : \"pt-8\",\n          className\n        )}\n      >\n        <div className=\"container mx-auto px-4\">\n          {children}\n        </div>\n      </motion.main>\n\n      {/* Floating Action Button */}\n      {user && (\n        <motion.div\n          initial={{ scale: 0, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          transition={{ delay: 1, duration: 0.3 }}\n          className=\"fixed bottom-8 right-8 z-50\"\n        >\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            onClick={() => {\n              if (user.userType === 'Student') {\n                window.location.href = '/upload';\n              } else {\n                window.location.href = '/job-queue';\n              }\n            }}\n            className=\"w-14 h-14 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg shadow-blue-500/25 flex items-center justify-center text-white hover:shadow-xl hover:shadow-blue-500/40 transition-all duration-300\"\n          >\n            {user.userType === 'Student' ? (\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n              </svg>\n            ) : (\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n              </svg>\n            )}\n          </motion.button>\n        </motion.div>\n      )}\n\n      {/* Theme Indicator */}\n      <motion.div\n        initial={{ opacity: 0, x: 100 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ delay: 0.5, duration: 0.3 }}\n        className=\"fixed top-1/2 right-4 transform -translate-y-1/2 z-40\"\n      >\n        <div className=\"w-12 h-12 rounded-2xl bg-white/10 dark:bg-black/20 backdrop-blur-md border border-white/20 flex items-center justify-center\">\n          {isDarkMode ? (\n            <svg className=\"w-5 h-5 text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z\" clipRule=\"evenodd\" />\n            </svg>\n          ) : (\n            <svg className=\"w-5 h-5 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z\" />\n            </svg>\n          )}\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default AceternityLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOrC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,oBACED,OAAA;IAAKE,SAAS,EAAC,sDAAsD;IAAAC,QAAA,gBAEnEH,OAAA;MAAKE,SAAS,EAAC;IAAoH;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC1IP,OAAA;MAAKE,SAAS,EAAC;IAAiI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvJP,OAAA;MAAKE,SAAS,EAAC;IAA+H;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAGrJP,OAAA,CAACN,MAAM,CAACc,GAAG;MACTC,OAAO,EAAE;QACPC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACdC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;MACzB,CAAE;MACFC,UAAU,EAAE;QACVC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAEC,QAAQ;QAChBC,IAAI,EAAE;MACR,CAAE;MACFd,SAAS,EAAC;IAAyE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC,eACFP,OAAA,CAACN,MAAM,CAACc,GAAG;MACTC,OAAO,EAAE;QACPC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACbC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;MACzB,CAAE;MACFC,UAAU,EAAE;QACVC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAEC,QAAQ;QAChBC,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE;MACT,CAAE;MACFf,SAAS,EAAC;IAA4E;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvF,CAAC,eACFP,OAAA,CAACN,MAAM,CAACc,GAAG;MACTC,OAAO,EAAE;QACPC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACdC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;MACzB,CAAE;MACFC,UAAU,EAAE;QACVC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAEC,QAAQ;QAChBC,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE;MACT,CAAE;MACFf,SAAS,EAAC;IAA8E;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACW,EAAA,GAjDIjB,eAAe;AAmDrB,MAAMkB,WAAW,GAAGA,CAAA,KAAM;EACxB,oBACEnB,OAAA;IAAKE,SAAS,EAAC,6CAA6C;IAAAC,QAAA,eAC1DH,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAACkB,KAAK,EAAE;QACvCC,eAAe,EAAE;AACzB;AACA;AACA,SAAS;QACDC,cAAc,EAAE;MAClB;IAAE;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV,CAAC;AAACgB,GAAA,GAZIJ,WAAW;AAcjB,MAAMK,gBAAiD,GAAGA,CAAC;EAAErB,QAAQ;EAAED,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAuB,EAAA;EAC1F,MAAM;IAAEC;EAAW,CAAC,GAAG9B,QAAQ,CAAC,CAAC;EACjC,MAAM;IAAE+B;EAAK,CAAC,GAAG9B,OAAO,CAAC,CAAC;EAE1B,oBACEG,OAAA;IAAKE,SAAS,EAAEJ,EAAE,CAChB,mEAAmE,EACnE4B,UAAU,GACN,uDAAuD,GACvD,sDACN,CAAE;IAAAvB,QAAA,gBAEAH,OAAA,CAACmB,WAAW;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfP,OAAA,CAACC,eAAe;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGlBoB,IAAI,iBAAI3B,OAAA,CAACL,WAAW;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGxBP,OAAA,CAACN,MAAM,CAACkC,IAAI;MACVC,OAAO,EAAE;QAAElB,OAAO,EAAE,CAAC;QAAED,CAAC,EAAE;MAAG,CAAE;MAC/BD,OAAO,EAAE;QAAEE,OAAO,EAAE,CAAC;QAAED,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BX,SAAS,EAAEJ,EAAE,CACX,eAAe,EACf6B,IAAI,GAAG,OAAO,GAAG,MAAM,EACvBzB,SACF,CAAE;MAAAC,QAAA,eAEFH,OAAA;QAAKE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EACpCA;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,EAGboB,IAAI,iBACH3B,OAAA,CAACN,MAAM,CAACc,GAAG;MACTqB,OAAO,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEnB,OAAO,EAAE;MAAE,CAAE;MAClCF,OAAO,EAAE;QAAEqB,KAAK,EAAE,CAAC;QAAEnB,OAAO,EAAE;MAAE,CAAE;MAClCC,UAAU,EAAE;QAAEK,KAAK,EAAE,CAAC;QAAEJ,QAAQ,EAAE;MAAI,CAAE;MACxCX,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAEvCH,OAAA,CAACN,MAAM,CAACqC,MAAM;QACZC,UAAU,EAAE;UAAEF,KAAK,EAAE;QAAI,CAAE;QAC3BG,QAAQ,EAAE;UAAEH,KAAK,EAAE;QAAI,CAAE;QACzBI,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIP,IAAI,CAACQ,QAAQ,KAAK,SAAS,EAAE;YAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,SAAS;UAClC,CAAC,MAAM;YACLF,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAY;UACrC;QACF,CAAE;QACFpC,SAAS,EAAC,kNAAkN;QAAAC,QAAA,EAE3NwB,IAAI,CAACQ,QAAQ,KAAK,SAAS,gBAC1BnC,OAAA;UAAKE,SAAS,EAAC,SAAS;UAACqC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAtC,QAAA,eAC5EH,OAAA;YAAM0C,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAgB;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,gBAENP,OAAA;UAAKE,SAAS,EAAC,SAAS;UAACqC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAtC,QAAA,eAC5EH,OAAA;YAAM0C,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAgI;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrM;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACb,eAGDP,OAAA,CAACN,MAAM,CAACc,GAAG;MACTqB,OAAO,EAAE;QAAElB,OAAO,EAAE,CAAC;QAAEmC,CAAC,EAAE;MAAI,CAAE;MAChCrC,OAAO,EAAE;QAAEE,OAAO,EAAE,CAAC;QAAEmC,CAAC,EAAE;MAAE,CAAE;MAC9BlC,UAAU,EAAE;QAAEK,KAAK,EAAE,GAAG;QAAEJ,QAAQ,EAAE;MAAI,CAAE;MAC1CX,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eAEjEH,OAAA;QAAKE,SAAS,EAAC,6HAA6H;QAAAC,QAAA,EACzIuB,UAAU,gBACT1B,OAAA;UAAKE,SAAS,EAAC,yBAAyB;UAACqC,IAAI,EAAC,cAAc;UAACE,OAAO,EAAC,WAAW;UAAAtC,QAAA,eAC9EH,OAAA;YAAM+C,QAAQ,EAAC,SAAS;YAACF,CAAC,EAAC,sfAAsf;YAACG,QAAQ,EAAC;UAAS;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpiB,CAAC,gBAENP,OAAA;UAAKE,SAAS,EAAC,uBAAuB;UAACqC,IAAI,EAAC,cAAc;UAACE,OAAO,EAAC,WAAW;UAAAtC,QAAA,eAC5EH,OAAA;YAAM6C,CAAC,EAAC;UAAmE;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACkB,EAAA,CAxFID,gBAAiD;EAAA,QAC9B5B,QAAQ,EACdC,OAAO;AAAA;AAAAoD,GAAA,GAFpBzB,gBAAiD;AA0FvD,eAAeA,gBAAgB;AAAC,IAAAN,EAAA,EAAAK,GAAA,EAAA0B,GAAA;AAAAC,YAAA,CAAAhC,EAAA;AAAAgC,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}