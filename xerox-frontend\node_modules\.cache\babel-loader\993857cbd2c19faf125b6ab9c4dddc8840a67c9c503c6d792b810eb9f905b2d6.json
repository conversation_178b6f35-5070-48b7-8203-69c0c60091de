{"ast": null, "code": "const cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nexport { cubicBezierAsString };", "map": {"version": 3, "names": ["cubicBezierAsString", "a", "b", "c", "d"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs"], "sourcesContent": ["const cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\nexport { cubicBezierAsString };\n"], "mappings": "AAAA,MAAMA,mBAAmB,GAAGA,CAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,KAAK,gBAAgBH,CAAC,KAAKC,CAAC,KAAKC,CAAC,KAAKC,CAAC,GAAG;AAEpF,SAASJ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}