{"ast": null, "code": "/**\n * @license lucide-react v0.515.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"6\",\n  key: \"1vlfrh\"\n}], [\"polyline\", {\n  points: \"12 10 12 12 13 13\",\n  key: \"19dquz\"\n}], [\"path\", {\n  d: \"m16.13 7.66-.81-4.05a2 2 0 0 0-2-1.61h-2.68a2 2 0 0 0-2 1.61l-.78 4.05\",\n  key: \"18k57s\"\n}], [\"path\", {\n  d: \"m7.88 16.36.8 4a2 2 0 0 0 2 1.61h2.72a2 2 0 0 0 2-1.61l.81-4.05\",\n  key: \"16ny36\"\n}]];\nconst Watch = createLucideIcon(\"watch\", __iconNode);\nexport { __iconNode, Watch as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "points", "d", "Watch", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\node_modules\\lucide-react\\src\\icons\\watch.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '6', key: '1vlfrh' }],\n  ['polyline', { points: '12 10 12 12 13 13', key: '19dquz' }],\n  [\n    'path',\n    { d: 'm16.13 7.66-.81-4.05a2 2 0 0 0-2-1.61h-2.68a2 2 0 0 0-2 1.61l-.78 4.05', key: '18k57s' },\n  ],\n  ['path', { d: 'm7.88 16.36.8 4a2 2 0 0 0 2 1.61h2.72a2 2 0 0 0 2-1.61l.81-4.05', key: '16ny36' }],\n];\n\n/**\n * @component @name Watch\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI2IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjEyIDEwIDEyIDEyIDEzIDEzIiAvPgogIDxwYXRoIGQ9Im0xNi4xMyA3LjY2LS44MS00LjA1YTIgMiAwIDAgMC0yLTEuNjFoLTIuNjhhMiAyIDAgMCAwLTIgMS42MWwtLjc4IDQuMDUiIC8+CiAgPHBhdGggZD0ibTcuODggMTYuMzYuOCA0YTIgMiAwIDAgMCAyIDEuNjFoMi43MmEyIDIgMCAwIDAgMi0xLjYxbC44MS00LjA1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/watch\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Watch = createLucideIcon('watch', __iconNode);\n\nexport default Watch;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,UAAY;EAAEC,MAAA,EAAQ,mBAAqB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3D,CACE,QACA;EAAEE,CAAA,EAAG,wEAA0E;EAAAF,GAAA,EAAK;AAAS,EAC/F,EACA,CAAC,MAAQ;EAAEE,CAAA,EAAG,iEAAmE;EAAAF,GAAA,EAAK;AAAU,GAClG;AAaM,MAAAG,KAAA,GAAQC,gBAAiB,UAASR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}