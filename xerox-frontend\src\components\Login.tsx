import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, Row, Col, Card, Form, <PERSON><PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Printer, Mail, Lock, LogIn } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import Layout from './Layout';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const { login, isLoading, error, user } = useAuth();
  const { isDarkMode } = useTheme();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const success = await login(email, password);
    if (success) {
      navigate('/dashboard');
    }
  };



  return (
    <Layout>
      <Container className="d-flex align-items-center justify-content-center" style={{ minHeight: '100vh' }}>
        <Row className="justify-content-center w-100">
          <Col md={6} lg={4}>
            <motion.div
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6 }}
            >
              <Card className={`shadow-lg border-0 ${isDarkMode ? 'bg-dark text-light' : ''}`} style={{ borderRadius: '20px', overflow: 'hidden' }}>
                <div className="position-relative">
                  <div
                    className="w-100 h-100 position-absolute"
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      height: '120px'
                    }}
                  />
                  <Card.Body className="p-4 position-relative">
                    <div className="text-center mb-4">
                      <motion.div
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ delay: 0.3, duration: 0.5 }}
                        className="d-inline-block p-3 rounded-circle bg-white shadow-sm mb-3"
                        style={{ marginTop: '20px' }}
                      >
                        <Printer size={40} className="text-primary" />
                      </motion.div>
                      <h2 className="h4 mb-2" style={{ color: isDarkMode ? '#fff' : '#333', marginTop: '60px' }}>Welcome Back!</h2>
                      <p className="text-muted small">Sign in to your XeroxHub account</p>
                    </div>

                    {error && (
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Alert variant="danger" className="mb-3 border-0" style={{ borderRadius: '12px' }}>
                          <i className="fas fa-exclamation-triangle me-2"></i>
                          {error}
                        </Alert>
                      </motion.div>
                    )}

                    <Form onSubmit={handleSubmit}>
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4, duration: 0.3 }}
                      >
                        <Form.Group className="mb-3">
                          <Form.Label className="d-flex align-items-center">
                            <Mail size={16} className="me-2" />
                            Email Address
                          </Form.Label>
                          <Form.Control
                            type="email"
                            placeholder="Enter your email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                            disabled={isLoading}
                            style={{
                              borderRadius: '12px',
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              transition: 'all 0.3s ease'
                            }}
                            onFocus={(e) => e.target.style.borderColor = '#667eea'}
                            onBlur={(e) => e.target.style.borderColor = '#e9ecef'}
                          />
                        </Form.Group>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5, duration: 0.3 }}
                      >
                        <Form.Group className="mb-4">
                          <Form.Label className="d-flex align-items-center">
                            <Lock size={16} className="me-2" />
                            Password
                          </Form.Label>
                          <Form.Control
                            type="password"
                            placeholder="Enter your password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                            disabled={isLoading}
                            style={{
                              borderRadius: '12px',
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              transition: 'all 0.3s ease'
                            }}
                            onFocus={(e) => e.target.style.borderColor = '#667eea'}
                            onBlur={(e) => e.target.style.borderColor = '#e9ecef'}
                          />
                        </Form.Group>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6, duration: 0.3 }}
                      >
                        <Button
                          type="submit"
                          className="w-100 mb-3 btn-gradient"
                          disabled={isLoading}
                          style={{
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            border: 'none',
                            borderRadius: '12px',
                            padding: '12px',
                            fontWeight: '600',
                            fontSize: '16px',
                            transition: 'all 0.3s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.3)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = 'none';
                          }}
                        >
                          {isLoading ? (
                            <>
                              <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                              />
                              Signing In...
                            </>
                          ) : (
                            <>
                              <LogIn size={18} className="me-2" />
                              Sign In
                            </>
                          )}
                        </Button>
                      </motion.div>
                    </Form>

                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.7, duration: 0.3 }}
                    >
                      <hr style={{ margin: '2rem 0', borderColor: isDarkMode ? '#404040' : '#e9ecef' }} />

                      <div className="text-center">
                        <Link
                          to="/register"
                          className="text-decoration-none"
                          style={{
                            color: '#667eea',
                            fontWeight: '500',
                            transition: 'all 0.3s ease'
                          }}
                          onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#764ba2'}
                          onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#667eea'}
                        >
                          Don't have an account? Create Account!
                        </Link>
                      </div>

                      <div className="text-center mt-2">
                        <Link
                          to="/forgot-password"
                          className="text-decoration-none small"
                          style={{
                            color: '#6c757d',
                            transition: 'all 0.3s ease'
                          }}
                          onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#667eea'}
                          onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#6c757d'}
                        >
                          Forgot Password?
                        </Link>
                      </div>
                    </motion.div>
                  </Card.Body>
                </div>
              </Card>
            </motion.div>
          </Col>
        </Row>
      </Container>
    </Layout>
  );
};

export default Login;
