{"ast": null, "code": "import { transformPropOrder } from '../../render/utils/keys-transform.mjs';\nconst translateAlias = {\n  x: \"translateX\",\n  y: \"translateY\",\n  z: \"translateZ\",\n  transformPerspective: \"perspective\"\n};\nfunction buildTransform(state) {\n  let transform = \"\";\n  let transformIsDefault = true;\n  /**\n   * Loop over all possible transforms in order, adding the ones that\n   * are present to the transform string.\n   */\n  for (let i = 0; i < transformPropOrder.length; i++) {\n    const key = transformPropOrder[i];\n    const value = state.latest[key];\n    if (value === undefined) continue;\n    let valueIsDefault = true;\n    if (typeof value === \"number\") {\n      valueIsDefault = value === (key.startsWith(\"scale\") ? 1 : 0);\n    } else {\n      valueIsDefault = parseFloat(value) === 0;\n    }\n    if (!valueIsDefault) {\n      transformIsDefault = false;\n      const transformName = translateAlias[key] || key;\n      const valueToRender = state.latest[key];\n      transform += `${transformName}(${valueToRender}) `;\n    }\n  }\n  return transformIsDefault ? \"none\" : transform.trim();\n}\nexport { buildTransform };", "map": {"version": 3, "names": ["transformPropOrder", "<PERSON><PERSON><PERSON><PERSON>", "x", "y", "z", "transformPerspective", "buildTransform", "state", "transform", "transformIsDefault", "i", "length", "key", "value", "latest", "undefined", "valueIsDefault", "startsWith", "parseFloat", "transformName", "valueToRender", "trim"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/effects/style/transform.mjs"], "sourcesContent": ["import { transformPropOrder } from '../../render/utils/keys-transform.mjs';\n\nconst translateAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n    transformPerspective: \"perspective\",\n};\nfunction buildTransform(state) {\n    let transform = \"\";\n    let transformIsDefault = true;\n    /**\n     * Loop over all possible transforms in order, adding the ones that\n     * are present to the transform string.\n     */\n    for (let i = 0; i < transformPropOrder.length; i++) {\n        const key = transformPropOrder[i];\n        const value = state.latest[key];\n        if (value === undefined)\n            continue;\n        let valueIsDefault = true;\n        if (typeof value === \"number\") {\n            valueIsDefault = value === (key.startsWith(\"scale\") ? 1 : 0);\n        }\n        else {\n            valueIsDefault = parseFloat(value) === 0;\n        }\n        if (!valueIsDefault) {\n            transformIsDefault = false;\n            const transformName = translateAlias[key] || key;\n            const valueToRender = state.latest[key];\n            transform += `${transformName}(${valueToRender}) `;\n        }\n    }\n    return transformIsDefault ? \"none\" : transform.trim();\n}\n\nexport { buildTransform };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,uCAAuC;AAE1E,MAAMC,cAAc,GAAG;EACnBC,CAAC,EAAE,YAAY;EACfC,CAAC,EAAE,YAAY;EACfC,CAAC,EAAE,YAAY;EACfC,oBAAoB,EAAE;AAC1B,CAAC;AACD,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC3B,IAAIC,SAAS,GAAG,EAAE;EAClB,IAAIC,kBAAkB,GAAG,IAAI;EAC7B;AACJ;AACA;AACA;EACI,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,kBAAkB,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;IAChD,MAAME,GAAG,GAAGZ,kBAAkB,CAACU,CAAC,CAAC;IACjC,MAAMG,KAAK,GAAGN,KAAK,CAACO,MAAM,CAACF,GAAG,CAAC;IAC/B,IAAIC,KAAK,KAAKE,SAAS,EACnB;IACJ,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;MAC3BG,cAAc,GAAGH,KAAK,MAAMD,GAAG,CAACK,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChE,CAAC,MACI;MACDD,cAAc,GAAGE,UAAU,CAACL,KAAK,CAAC,KAAK,CAAC;IAC5C;IACA,IAAI,CAACG,cAAc,EAAE;MACjBP,kBAAkB,GAAG,KAAK;MAC1B,MAAMU,aAAa,GAAGlB,cAAc,CAACW,GAAG,CAAC,IAAIA,GAAG;MAChD,MAAMQ,aAAa,GAAGb,KAAK,CAACO,MAAM,CAACF,GAAG,CAAC;MACvCJ,SAAS,IAAI,GAAGW,aAAa,IAAIC,aAAa,IAAI;IACtD;EACJ;EACA,OAAOX,kBAAkB,GAAG,MAAM,GAAGD,SAAS,CAACa,IAAI,CAAC,CAAC;AACzD;AAEA,SAASf,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}