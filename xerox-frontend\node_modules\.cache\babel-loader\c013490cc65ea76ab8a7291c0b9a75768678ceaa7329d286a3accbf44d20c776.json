{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\AceternityRegister.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { UserPlus, GraduationCap, Store, Mail, Lock, User, Phone, MapPin } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport AceternityLayout from './AceternityLayout';\nimport { cn } from '../../lib/utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AceternityRegister = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('student');\n  const [studentData, setStudentData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    studentNumber: '',\n    firstName: '',\n    lastName: '',\n    phoneNumber: '',\n    department: '',\n    year: undefined\n  });\n  const [xeroxData, setXeroxData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    xeroxCenterName: '',\n    location: '',\n    contactPerson: '',\n    phoneNumber: '',\n    description: ''\n  });\n  const {\n    registerStudent,\n    registerXeroxCenter,\n    isLoading,\n    error,\n    user\n  } = useAuth();\n  const {\n    isDarkMode\n  } = useTheme();\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n  const handleStudentSubmit = async e => {\n    e.preventDefault();\n    const success = await registerStudent(studentData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n  const handleXeroxSubmit = async e => {\n    e.preventDefault();\n    const success = await registerXeroxCenter(xeroxData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n  const handleStudentChange = (field, value) => {\n    setStudentData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleXeroxChange = (field, value) => {\n    setXeroxData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(AceternityLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          y: 0,\n          scale: 1\n        },\n        transition: {\n          duration: 0.5\n        },\n        className: \"max-w-2xl w-full space-y-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: cn(\"relative rounded-3xl p-8 backdrop-blur-xl border shadow-2xl\", isDarkMode ? \"bg-black/20 border-white/10 shadow-black/20\" : \"bg-white/20 border-white/30 shadow-black/10\"),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: -20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.2,\n                duration: 0.5\n              },\n              className: \"text-center mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                whileHover: {\n                  rotate: 5,\n                  scale: 1.05\n                },\n                className: \"mx-auto w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-6 shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(UserPlus, {\n                  className: \"w-8 h-8 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                children: \"Join XeroxHub\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-gray-600 dark:text-gray-400\",\n                children: \"Create your account and start printing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.3,\n                duration: 0.5\n              },\n              className: \"flex space-x-1 rounded-2xl bg-white/10 dark:bg-black/20 p-1 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('student'),\n                className: cn(\"flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-xl transition-all duration-200\", activeTab === 'student' ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg\" : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"),\n                children: [/*#__PURE__*/_jsxDEV(GraduationCap, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('xerox'),\n                className: cn(\"flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-xl transition-all duration-200\", activeTab === 'xerox' ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg\" : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"),\n                children: [/*#__PURE__*/_jsxDEV(Store, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Xerox Center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              className: \"mb-6 p-4 rounded-2xl bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: error\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              mode: \"wait\",\n              children: activeTab === 'student' ? /*#__PURE__*/_jsxDEV(motion.form, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                exit: {\n                  opacity: 0,\n                  x: 20\n                },\n                transition: {\n                  duration: 0.3\n                },\n                onSubmit: handleStudentSubmit,\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                      children: \"Username\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                        children: /*#__PURE__*/_jsxDEV(User, {\n                          className: \"h-5 w-5 text-gray-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 179,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 178,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        required: true,\n                        value: studentData.username,\n                        onChange: e => handleStudentChange('username', e.target.value),\n                        disabled: isLoading,\n                        className: cn(\"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                        placeholder: \"Enter username\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 181,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                      children: \"Student Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      required: true,\n                      value: studentData.studentNumber,\n                      onChange: e => handleStudentChange('studentNumber', e.target.value),\n                      disabled: isLoading,\n                      className: cn(\"block w-full px-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                      placeholder: \"Student number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                    children: \"Email Address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                      children: /*#__PURE__*/_jsxDEV(Mail, {\n                        className: \"h-5 w-5 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 227,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"email\",\n                      required: true,\n                      value: studentData.email,\n                      onChange: e => handleStudentChange('email', e.target.value),\n                      disabled: isLoading,\n                      className: cn(\"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                      placeholder: \"Enter your email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                    children: \"Password\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                      children: /*#__PURE__*/_jsxDEV(Lock, {\n                        className: \"h-5 w-5 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 253,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"password\",\n                      required: true,\n                      value: studentData.password,\n                      onChange: e => handleStudentChange('password', e.target.value),\n                      disabled: isLoading,\n                      className: cn(\"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                      placeholder: \"Create a password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                      children: \"First Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      required: true,\n                      value: studentData.firstName,\n                      onChange: e => handleStudentChange('firstName', e.target.value),\n                      disabled: isLoading,\n                      className: cn(\"block w-full px-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                      placeholder: \"First name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 278,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                      children: \"Last Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      required: true,\n                      value: studentData.lastName,\n                      onChange: e => handleStudentChange('lastName', e.target.value),\n                      disabled: isLoading,\n                      className: cn(\"block w-full px-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                      placeholder: \"Last name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  type: \"submit\",\n                  disabled: isLoading,\n                  className: cn(\"group relative w-full flex justify-center py-3 px-4 rounded-2xl text-sm font-medium text-white\", \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\", \"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\", \"disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\", \"shadow-lg hover:shadow-xl\"),\n                  children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Creating Account...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(GraduationCap, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Register as Student\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this)]\n              }, \"student\", true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(motion.form, {\n                initial: {\n                  opacity: 0,\n                  x: 20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                exit: {\n                  opacity: 0,\n                  x: -20\n                },\n                transition: {\n                  duration: 0.3\n                },\n                onSubmit: handleXeroxSubmit,\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                      children: \"Username\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                        children: /*#__PURE__*/_jsxDEV(User, {\n                          className: \"h-5 w-5 text-gray-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 362,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        required: true,\n                        value: xeroxData.username,\n                        onChange: e => handleXeroxChange('username', e.target.value),\n                        disabled: isLoading,\n                        className: cn(\"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                        placeholder: \"Enter username\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 364,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                      children: \"Center Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                        children: /*#__PURE__*/_jsxDEV(Store, {\n                          className: \"h-5 w-5 text-gray-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 388,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 387,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        required: true,\n                        value: xeroxData.xeroxCenterName,\n                        onChange: e => handleXeroxChange('xeroxCenterName', e.target.value),\n                        disabled: isLoading,\n                        className: cn(\"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                        placeholder: \"Center name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 390,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                    children: \"Email Address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                      children: /*#__PURE__*/_jsxDEV(Mail, {\n                        className: \"h-5 w-5 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 415,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 414,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"email\",\n                      required: true,\n                      value: xeroxData.email,\n                      onChange: e => handleXeroxChange('email', e.target.value),\n                      disabled: isLoading,\n                      className: cn(\"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                      placeholder: \"Enter your email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                    children: \"Password\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                      children: /*#__PURE__*/_jsxDEV(Lock, {\n                        className: \"h-5 w-5 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 441,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"password\",\n                      required: true,\n                      value: xeroxData.password,\n                      onChange: e => handleXeroxChange('password', e.target.value),\n                      disabled: isLoading,\n                      className: cn(\"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                      placeholder: \"Create a password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                    children: \"Location\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                      children: /*#__PURE__*/_jsxDEV(MapPin, {\n                        className: \"h-5 w-5 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 467,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      required: true,\n                      value: xeroxData.location,\n                      onChange: e => handleXeroxChange('location', e.target.value),\n                      disabled: isLoading,\n                      className: cn(\"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                      placeholder: \"Center location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 469,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                      children: \"Contact Person\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: xeroxData.contactPerson,\n                      onChange: e => handleXeroxChange('contactPerson', e.target.value),\n                      disabled: isLoading,\n                      className: cn(\"block w-full px-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                      placeholder: \"Contact person (optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                      children: \"Phone Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                        children: /*#__PURE__*/_jsxDEV(Phone, {\n                          className: \"h-5 w-5 text-gray-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 514,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 513,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"tel\",\n                        required: true,\n                        value: xeroxData.phoneNumber,\n                        onChange: e => handleXeroxChange('phoneNumber', e.target.value),\n                        disabled: isLoading,\n                        className: cn(\"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\", \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\", \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\", \"ring-gray-300 dark:ring-gray-600\", \"text-gray-900 dark:text-white\"),\n                        placeholder: \"Phone number\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 516,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  type: \"submit\",\n                  disabled: isLoading,\n                  className: cn(\"group relative w-full flex justify-center py-3 px-4 rounded-2xl text-sm font-medium text-white\", \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\", \"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\", \"disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\", \"shadow-lg hover:shadow-xl\"),\n                  children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 551,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Creating Account...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Store, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 556,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Register as Xerox Center\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 557,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 21\n                }, this)]\n              }, \"xerox\", true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.3\n              },\n              className: \"mt-6 text-center\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors text-sm\",\n                children: \"Already have an account? Sign in\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(AceternityRegister, \"innhoHiNPBQ7HtBFsbfyS9Rqisk=\", false, function () {\n  return [useAuth, useTheme, useNavigate];\n});\n_c = AceternityRegister;\nexport default AceternityRegister;\nvar _c;\n$RefreshReg$(_c, \"AceternityRegister\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "motion", "AnimatePresence", "UserPlus", "GraduationCap", "Store", "Mail", "Lock", "User", "Phone", "MapPin", "useAuth", "useTheme", "AceternityLayout", "cn", "jsxDEV", "_jsxDEV", "AceternityRegister", "_s", "activeTab", "setActiveTab", "studentData", "setStudentData", "username", "email", "password", "studentNumber", "firstName", "lastName", "phoneNumber", "department", "year", "undefined", "xeroxData", "setXeroxData", "xeroxCenterName", "location", "<PERSON><PERSON><PERSON>", "description", "registerStudent", "registerXeroxCenter", "isLoading", "error", "user", "isDarkMode", "navigate", "handleStudentSubmit", "e", "preventDefault", "success", "handleXeroxSubmit", "handleStudentChange", "field", "value", "prev", "handleXeroxChange", "children", "className", "div", "initial", "opacity", "y", "scale", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "whileHover", "rotate", "onClick", "x", "fill", "viewBox", "fillRule", "d", "clipRule", "mode", "form", "exit", "onSubmit", "type", "required", "onChange", "target", "disabled", "placeholder", "button", "whileTap", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/AceternityRegister.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { UserPlus, GraduationCap, Store, Mail, Lock, User, Phone, MapPin } from 'lucide-react';\nimport { useAuth, StudentRegistrationData, XeroxCenterRegistrationData } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport AceternityLayout from './AceternityLayout';\nimport { cn } from '../../lib/utils';\n\nconst AceternityRegister: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<'student' | 'xerox'>('student');\n  const [studentData, setStudentData] = useState<StudentRegistrationData>({\n    username: '',\n    email: '',\n    password: '',\n    studentNumber: '',\n    firstName: '',\n    lastName: '',\n    phoneNumber: '',\n    department: '',\n    year: undefined\n  });\n  const [xeroxData, setXeroxData] = useState<XeroxCenterRegistrationData>({\n    username: '',\n    email: '',\n    password: '',\n    xeroxCenterName: '',\n    location: '',\n    contactPerson: '',\n    phoneNumber: '',\n    description: ''\n  });\n\n  const { registerStudent, registerXeroxCenter, isLoading, error, user } = useAuth();\n  const { isDarkMode } = useTheme();\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n\n  const handleStudentSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    const success = await registerStudent(studentData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n\n  const handleXeroxSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    const success = await registerXeroxCenter(xeroxData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n\n  const handleStudentChange = (field: keyof StudentRegistrationData, value: string | number | undefined) => {\n    setStudentData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleXeroxChange = (field: keyof XeroxCenterRegistrationData, value: string) => {\n    setXeroxData(prev => ({ ...prev, [field]: value }));\n  };\n\n  return (\n    <AceternityLayout>\n      <div className=\"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20, scale: 0.95 }}\n          animate={{ opacity: 1, y: 0, scale: 1 }}\n          transition={{ duration: 0.5 }}\n          className=\"max-w-2xl w-full space-y-8\"\n        >\n          {/* Card Container */}\n          <div className={cn(\n            \"relative rounded-3xl p-8 backdrop-blur-xl border shadow-2xl\",\n            isDarkMode \n              ? \"bg-black/20 border-white/10 shadow-black/20\" \n              : \"bg-white/20 border-white/30 shadow-black/10\"\n          )}>\n            {/* Animated Background Gradient */}\n            <div className=\"absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-pulse\"></div>\n            \n            {/* Content */}\n            <div className=\"relative z-10\">\n              {/* Logo and Header */}\n              <motion.div\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.2, duration: 0.5 }}\n                className=\"text-center mb-8\"\n              >\n                <motion.div\n                  whileHover={{ rotate: 5, scale: 1.05 }}\n                  className=\"mx-auto w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-6 shadow-lg\"\n                >\n                  <UserPlus className=\"w-8 h-8 text-white\" />\n                </motion.div>\n                <h2 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                  Join XeroxHub\n                </h2>\n                <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n                  Create your account and start printing\n                </p>\n              </motion.div>\n\n              {/* Tab Navigation */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3, duration: 0.5 }}\n                className=\"flex space-x-1 rounded-2xl bg-white/10 dark:bg-black/20 p-1 mb-8\"\n              >\n                <button\n                  onClick={() => setActiveTab('student')}\n                  className={cn(\n                    \"flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-xl transition-all duration-200\",\n                    activeTab === 'student'\n                      ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg\"\n                      : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n                  )}\n                >\n                  <GraduationCap className=\"w-5 h-5\" />\n                  <span className=\"font-medium\">Student</span>\n                </button>\n                <button\n                  onClick={() => setActiveTab('xerox')}\n                  className={cn(\n                    \"flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-xl transition-all duration-200\",\n                    activeTab === 'xerox'\n                      ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg\"\n                      : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n                  )}\n                >\n                  <Store className=\"w-5 h-5\" />\n                  <span className=\"font-medium\">Xerox Center</span>\n                </button>\n              </motion.div>\n\n              {/* Error Alert */}\n              {error && (\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  className=\"mb-6 p-4 rounded-2xl bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400\"\n                >\n                  <div className=\"flex items-center space-x-2\">\n                    <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span className=\"text-sm\">{error}</span>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* Forms */}\n              <AnimatePresence mode=\"wait\">\n                {activeTab === 'student' ? (\n                  <motion.form\n                    key=\"student\"\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: 20 }}\n                    transition={{ duration: 0.3 }}\n                    onSubmit={handleStudentSubmit}\n                    className=\"space-y-6\"\n                  >\n                    {/* Student Form Fields */}\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Username\n                        </label>\n                        <div className=\"relative\">\n                          <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                            <User className=\"h-5 w-5 text-gray-400\" />\n                          </div>\n                          <input\n                            type=\"text\"\n                            required\n                            value={studentData.username}\n                            onChange={(e) => handleStudentChange('username', e.target.value)}\n                            disabled={isLoading}\n                            className={cn(\n                              \"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                              \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                              \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                              \"ring-gray-300 dark:ring-gray-600\",\n                              \"text-gray-900 dark:text-white\"\n                            )}\n                            placeholder=\"Enter username\"\n                          />\n                        </div>\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Student Number\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={studentData.studentNumber}\n                          onChange={(e) => handleStudentChange('studentNumber', e.target.value)}\n                          disabled={isLoading}\n                          className={cn(\n                            \"block w-full px-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                            \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                            \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                            \"ring-gray-300 dark:ring-gray-600\",\n                            \"text-gray-900 dark:text-white\"\n                          )}\n                          placeholder=\"Student number\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Email Address\n                      </label>\n                      <div className=\"relative\">\n                        <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                          <Mail className=\"h-5 w-5 text-gray-400\" />\n                        </div>\n                        <input\n                          type=\"email\"\n                          required\n                          value={studentData.email}\n                          onChange={(e) => handleStudentChange('email', e.target.value)}\n                          disabled={isLoading}\n                          className={cn(\n                            \"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                            \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                            \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                            \"ring-gray-300 dark:ring-gray-600\",\n                            \"text-gray-900 dark:text-white\"\n                          )}\n                          placeholder=\"Enter your email\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Password\n                      </label>\n                      <div className=\"relative\">\n                        <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                          <Lock className=\"h-5 w-5 text-gray-400\" />\n                        </div>\n                        <input\n                          type=\"password\"\n                          required\n                          value={studentData.password}\n                          onChange={(e) => handleStudentChange('password', e.target.value)}\n                          disabled={isLoading}\n                          className={cn(\n                            \"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                            \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                            \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                            \"ring-gray-300 dark:ring-gray-600\",\n                            \"text-gray-900 dark:text-white\"\n                          )}\n                          placeholder=\"Create a password\"\n                        />\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          First Name\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={studentData.firstName}\n                          onChange={(e) => handleStudentChange('firstName', e.target.value)}\n                          disabled={isLoading}\n                          className={cn(\n                            \"block w-full px-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                            \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                            \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                            \"ring-gray-300 dark:ring-gray-600\",\n                            \"text-gray-900 dark:text-white\"\n                          )}\n                          placeholder=\"First name\"\n                        />\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Last Name\n                        </label>\n                        <input\n                          type=\"text\"\n                          required\n                          value={studentData.lastName}\n                          onChange={(e) => handleStudentChange('lastName', e.target.value)}\n                          disabled={isLoading}\n                          className={cn(\n                            \"block w-full px-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                            \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                            \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                            \"ring-gray-300 dark:ring-gray-600\",\n                            \"text-gray-900 dark:text-white\"\n                          )}\n                          placeholder=\"Last name\"\n                        />\n                      </div>\n                    </div>\n\n                    {/* Submit Button */}\n                    <motion.button\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                      type=\"submit\"\n                      disabled={isLoading}\n                      className={cn(\n                        \"group relative w-full flex justify-center py-3 px-4 rounded-2xl text-sm font-medium text-white\",\n                        \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\",\n                        \"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                        \"disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\",\n                        \"shadow-lg hover:shadow-xl\"\n                      )}\n                    >\n                      {isLoading ? (\n                        <div className=\"flex items-center space-x-2\">\n                          <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n                          <span>Creating Account...</span>\n                        </div>\n                      ) : (\n                        <div className=\"flex items-center space-x-2\">\n                          <GraduationCap className=\"w-5 h-5\" />\n                          <span>Register as Student</span>\n                        </div>\n                      )}\n                    </motion.button>\n                  </motion.form>\n                ) : (\n                  <motion.form\n                    key=\"xerox\"\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: -20 }}\n                    transition={{ duration: 0.3 }}\n                    onSubmit={handleXeroxSubmit}\n                    className=\"space-y-6\"\n                  >\n                    {/* Xerox Center Form Fields */}\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Username\n                        </label>\n                        <div className=\"relative\">\n                          <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                            <User className=\"h-5 w-5 text-gray-400\" />\n                          </div>\n                          <input\n                            type=\"text\"\n                            required\n                            value={xeroxData.username}\n                            onChange={(e) => handleXeroxChange('username', e.target.value)}\n                            disabled={isLoading}\n                            className={cn(\n                              \"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                              \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                              \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                              \"ring-gray-300 dark:ring-gray-600\",\n                              \"text-gray-900 dark:text-white\"\n                            )}\n                            placeholder=\"Enter username\"\n                          />\n                        </div>\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Center Name\n                        </label>\n                        <div className=\"relative\">\n                          <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                            <Store className=\"h-5 w-5 text-gray-400\" />\n                          </div>\n                          <input\n                            type=\"text\"\n                            required\n                            value={xeroxData.xeroxCenterName}\n                            onChange={(e) => handleXeroxChange('xeroxCenterName', e.target.value)}\n                            disabled={isLoading}\n                            className={cn(\n                              \"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                              \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                              \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                              \"ring-gray-300 dark:ring-gray-600\",\n                              \"text-gray-900 dark:text-white\"\n                            )}\n                            placeholder=\"Center name\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Email Address\n                      </label>\n                      <div className=\"relative\">\n                        <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                          <Mail className=\"h-5 w-5 text-gray-400\" />\n                        </div>\n                        <input\n                          type=\"email\"\n                          required\n                          value={xeroxData.email}\n                          onChange={(e) => handleXeroxChange('email', e.target.value)}\n                          disabled={isLoading}\n                          className={cn(\n                            \"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                            \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                            \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                            \"ring-gray-300 dark:ring-gray-600\",\n                            \"text-gray-900 dark:text-white\"\n                          )}\n                          placeholder=\"Enter your email\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Password\n                      </label>\n                      <div className=\"relative\">\n                        <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                          <Lock className=\"h-5 w-5 text-gray-400\" />\n                        </div>\n                        <input\n                          type=\"password\"\n                          required\n                          value={xeroxData.password}\n                          onChange={(e) => handleXeroxChange('password', e.target.value)}\n                          disabled={isLoading}\n                          className={cn(\n                            \"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                            \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                            \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                            \"ring-gray-300 dark:ring-gray-600\",\n                            \"text-gray-900 dark:text-white\"\n                          )}\n                          placeholder=\"Create a password\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Location\n                      </label>\n                      <div className=\"relative\">\n                        <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                          <MapPin className=\"h-5 w-5 text-gray-400\" />\n                        </div>\n                        <input\n                          type=\"text\"\n                          required\n                          value={xeroxData.location}\n                          onChange={(e) => handleXeroxChange('location', e.target.value)}\n                          disabled={isLoading}\n                          className={cn(\n                            \"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                            \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                            \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                            \"ring-gray-300 dark:ring-gray-600\",\n                            \"text-gray-900 dark:text-white\"\n                          )}\n                          placeholder=\"Center location\"\n                        />\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Contact Person\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={xeroxData.contactPerson}\n                          onChange={(e) => handleXeroxChange('contactPerson', e.target.value)}\n                          disabled={isLoading}\n                          className={cn(\n                            \"block w-full px-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                            \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                            \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                            \"ring-gray-300 dark:ring-gray-600\",\n                            \"text-gray-900 dark:text-white\"\n                          )}\n                          placeholder=\"Contact person (optional)\"\n                        />\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Phone Number\n                        </label>\n                        <div className=\"relative\">\n                          <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                            <Phone className=\"h-5 w-5 text-gray-400\" />\n                          </div>\n                          <input\n                            type=\"tel\"\n                            required\n                            value={xeroxData.phoneNumber}\n                            onChange={(e) => handleXeroxChange('phoneNumber', e.target.value)}\n                            disabled={isLoading}\n                            className={cn(\n                              \"block w-full pl-12 pr-4 py-3 rounded-2xl border-0 ring-1 ring-inset transition-all duration-200\",\n                              \"placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                              \"bg-white/50 dark:bg-black/20 backdrop-blur-sm\",\n                              \"ring-gray-300 dark:ring-gray-600\",\n                              \"text-gray-900 dark:text-white\"\n                            )}\n                            placeholder=\"Phone number\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Submit Button */}\n                    <motion.button\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                      type=\"submit\"\n                      disabled={isLoading}\n                      className={cn(\n                        \"group relative w-full flex justify-center py-3 px-4 rounded-2xl text-sm font-medium text-white\",\n                        \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\",\n                        \"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                        \"disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\",\n                        \"shadow-lg hover:shadow-xl\"\n                      )}\n                    >\n                      {isLoading ? (\n                        <div className=\"flex items-center space-x-2\">\n                          <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n                          <span>Creating Account...</span>\n                        </div>\n                      ) : (\n                        <div className=\"flex items-center space-x-2\">\n                          <Store className=\"w-5 h-5\" />\n                          <span>Register as Xerox Center</span>\n                        </div>\n                      )}\n                    </motion.button>\n                  </motion.form>\n                )}\n              </AnimatePresence>\n\n              {/* Login Link */}\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.5, duration: 0.3 }}\n                className=\"mt-6 text-center\"\n              >\n                <Link\n                  to=\"/login\"\n                  className=\"text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors text-sm\"\n                >\n                  Already have an account? Sign in\n                </Link>\n              </motion.div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </AceternityLayout>\n  );\n};\n\nexport default AceternityRegister;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,QAAQ,EAAEC,aAAa,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,QAAQ,cAAc;AAC9F,SAASC,OAAO,QAA8D,4BAA4B;AAC1G,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAsB,SAAS,CAAC;EAC1E,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAA0B;IACtE0B,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAEC;EACR,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAA8B;IACtE0B,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZU,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBR,WAAW,EAAE,EAAE;IACfS,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM;IAAEC,eAAe;IAAEC,mBAAmB;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGhC,OAAO,CAAC,CAAC;EAClF,MAAM;IAAEiC;EAAW,CAAC,GAAGhC,QAAQ,CAAC,CAAC;EACjC,MAAMiC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,IAAI6C,IAAI,EAAE;MACRE,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACF,IAAI,EAAEE,QAAQ,CAAC,CAAC;EAEpB,MAAMC,mBAAmB,GAAG,MAAOC,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,OAAO,GAAG,MAAMV,eAAe,CAAClB,WAAW,CAAC;IAClD,IAAI4B,OAAO,EAAE;MACXJ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAG,MAAOH,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,OAAO,GAAG,MAAMT,mBAAmB,CAACP,SAAS,CAAC;IACpD,IAAIgB,OAAO,EAAE;MACXJ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMM,mBAAmB,GAAGA,CAACC,KAAoC,EAAEC,KAAkC,KAAK;IACxG/B,cAAc,CAACgC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACvD,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAACH,KAAwC,EAAEC,KAAa,KAAK;IACrFnB,YAAY,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,oBACErC,OAAA,CAACH,gBAAgB;IAAA2C,QAAA,eACfxC,OAAA;MAAKyC,SAAS,EAAC,0EAA0E;MAAAD,QAAA,eACvFxC,OAAA,CAACf,MAAM,CAACyD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5CC,OAAO,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QACxCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,4BAA4B;QAAAD,QAAA,eAGtCxC,OAAA;UAAKyC,SAAS,EAAE3C,EAAE,CAChB,6DAA6D,EAC7D8B,UAAU,GACN,6CAA6C,GAC7C,6CACN,CAAE;UAAAY,QAAA,gBAEAxC,OAAA;YAAKyC,SAAS,EAAC;UAA+G;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGrIrD,OAAA;YAAKyC,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAE5BxC,OAAA,CAACf,MAAM,CAACyD,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCE,OAAO,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BG,UAAU,EAAE;gBAAEM,KAAK,EAAE,GAAG;gBAAEL,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAE5BxC,OAAA,CAACf,MAAM,CAACyD,GAAG;gBACTa,UAAU,EAAE;kBAAEC,MAAM,EAAE,CAAC;kBAAEV,KAAK,EAAE;gBAAK,CAAE;gBACvCL,SAAS,EAAC,6HAA6H;gBAAAD,QAAA,eAEvIxC,OAAA,CAACb,QAAQ;kBAACsD,SAAS,EAAC;gBAAoB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACbrD,OAAA;gBAAIyC,SAAS,EAAC,+FAA+F;gBAAAD,QAAA,EAAC;cAE9G;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrD,OAAA;gBAAGyC,SAAS,EAAC,+CAA+C;gBAAAD,QAAA,EAAC;cAE7D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eAGbrD,OAAA,CAACf,MAAM,CAACyD,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BG,UAAU,EAAE;gBAAEM,KAAK,EAAE,GAAG;gBAAEL,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,kEAAkE;cAAAD,QAAA,gBAE5ExC,OAAA;gBACEyD,OAAO,EAAEA,CAAA,KAAMrD,YAAY,CAAC,SAAS,CAAE;gBACvCqC,SAAS,EAAE3C,EAAE,CACX,oGAAoG,EACpGK,SAAS,KAAK,SAAS,GACnB,mEAAmE,GACnE,4EACN,CAAE;gBAAAqC,QAAA,gBAEFxC,OAAA,CAACZ,aAAa;kBAACqD,SAAS,EAAC;gBAAS;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCrD,OAAA;kBAAMyC,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACTrD,OAAA;gBACEyD,OAAO,EAAEA,CAAA,KAAMrD,YAAY,CAAC,OAAO,CAAE;gBACrCqC,SAAS,EAAE3C,EAAE,CACX,oGAAoG,EACpGK,SAAS,KAAK,OAAO,GACjB,mEAAmE,GACnE,4EACN,CAAE;gBAAAqC,QAAA,gBAEFxC,OAAA,CAACX,KAAK;kBAACoD,SAAS,EAAC;gBAAS;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7BrD,OAAA;kBAAMyC,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAY;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGZ3B,KAAK,iBACJ1B,OAAA,CAACf,MAAM,CAACyD,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCX,OAAO,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAC9BjB,SAAS,EAAC,4FAA4F;cAAAD,QAAA,eAEtGxC,OAAA;gBAAKyC,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAC1CxC,OAAA;kBAAKyC,SAAS,EAAC,SAAS;kBAACkB,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAApB,QAAA,eAC9DxC,OAAA;oBAAM6D,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,mHAAmH;oBAACC,QAAQ,EAAC;kBAAS;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjK,CAAC,eACNrD,OAAA;kBAAMyC,SAAS,EAAC,SAAS;kBAAAD,QAAA,EAAEd;gBAAK;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGDrD,OAAA,CAACd,eAAe;cAAC8E,IAAI,EAAC,MAAM;cAAAxB,QAAA,EACzBrC,SAAS,KAAK,SAAS,gBACtBH,OAAA,CAACf,MAAM,CAACgF,IAAI;gBAEVtB,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCX,OAAO,EAAE;kBAAEH,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE;gBAAE,CAAE;gBAC9BQ,IAAI,EAAE;kBAAEtB,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE;gBAAG,CAAE;gBAC5BV,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAC9BkB,QAAQ,EAAErC,mBAAoB;gBAC9BW,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBAGrBxC,OAAA;kBAAKyC,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,gBACpDxC,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAOyC,SAAS,EAAC,iEAAiE;sBAAAD,QAAA,EAAC;oBAEnF;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrD,OAAA;sBAAKyC,SAAS,EAAC,UAAU;sBAAAD,QAAA,gBACvBxC,OAAA;wBAAKyC,SAAS,EAAC,sEAAsE;wBAAAD,QAAA,eACnFxC,OAAA,CAACR,IAAI;0BAACiD,SAAS,EAAC;wBAAuB;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNrD,OAAA;wBACEoE,IAAI,EAAC,MAAM;wBACXC,QAAQ;wBACRhC,KAAK,EAAEhC,WAAW,CAACE,QAAS;wBAC5B+D,QAAQ,EAAGvC,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;wBACjEmC,QAAQ,EAAE/C,SAAU;wBACpBgB,SAAS,EAAE3C,EAAE,CACX,iGAAiG,EACjG,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;wBACF2E,WAAW,EAAC;sBAAgB;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrD,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAOyC,SAAS,EAAC,iEAAiE;sBAAAD,QAAA,EAAC;oBAEnF;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrD,OAAA;sBACEoE,IAAI,EAAC,MAAM;sBACXC,QAAQ;sBACRhC,KAAK,EAAEhC,WAAW,CAACK,aAAc;sBACjC4D,QAAQ,EAAGvC,CAAC,IAAKI,mBAAmB,CAAC,eAAe,EAAEJ,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;sBACtEmC,QAAQ,EAAE/C,SAAU;sBACpBgB,SAAS,EAAE3C,EAAE,CACX,2FAA2F,EAC3F,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;sBACF2E,WAAW,EAAC;oBAAgB;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrD,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAOyC,SAAS,EAAC,iEAAiE;oBAAAD,QAAA,EAAC;kBAEnF;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrD,OAAA;oBAAKyC,SAAS,EAAC,UAAU;oBAAAD,QAAA,gBACvBxC,OAAA;sBAAKyC,SAAS,EAAC,sEAAsE;sBAAAD,QAAA,eACnFxC,OAAA,CAACV,IAAI;wBAACmD,SAAS,EAAC;sBAAuB;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNrD,OAAA;sBACEoE,IAAI,EAAC,OAAO;sBACZC,QAAQ;sBACRhC,KAAK,EAAEhC,WAAW,CAACG,KAAM;sBACzB8D,QAAQ,EAAGvC,CAAC,IAAKI,mBAAmB,CAAC,OAAO,EAAEJ,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;sBAC9DmC,QAAQ,EAAE/C,SAAU;sBACpBgB,SAAS,EAAE3C,EAAE,CACX,iGAAiG,EACjG,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;sBACF2E,WAAW,EAAC;oBAAkB;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrD,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAOyC,SAAS,EAAC,iEAAiE;oBAAAD,QAAA,EAAC;kBAEnF;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrD,OAAA;oBAAKyC,SAAS,EAAC,UAAU;oBAAAD,QAAA,gBACvBxC,OAAA;sBAAKyC,SAAS,EAAC,sEAAsE;sBAAAD,QAAA,eACnFxC,OAAA,CAACT,IAAI;wBAACkD,SAAS,EAAC;sBAAuB;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNrD,OAAA;sBACEoE,IAAI,EAAC,UAAU;sBACfC,QAAQ;sBACRhC,KAAK,EAAEhC,WAAW,CAACI,QAAS;sBAC5B6D,QAAQ,EAAGvC,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;sBACjEmC,QAAQ,EAAE/C,SAAU;sBACpBgB,SAAS,EAAE3C,EAAE,CACX,iGAAiG,EACjG,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;sBACF2E,WAAW,EAAC;oBAAmB;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrD,OAAA;kBAAKyC,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,gBACpDxC,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAOyC,SAAS,EAAC,iEAAiE;sBAAAD,QAAA,EAAC;oBAEnF;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrD,OAAA;sBACEoE,IAAI,EAAC,MAAM;sBACXC,QAAQ;sBACRhC,KAAK,EAAEhC,WAAW,CAACM,SAAU;sBAC7B2D,QAAQ,EAAGvC,CAAC,IAAKI,mBAAmB,CAAC,WAAW,EAAEJ,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;sBAClEmC,QAAQ,EAAE/C,SAAU;sBACpBgB,SAAS,EAAE3C,EAAE,CACX,2FAA2F,EAC3F,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;sBACF2E,WAAW,EAAC;oBAAY;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENrD,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAOyC,SAAS,EAAC,iEAAiE;sBAAAD,QAAA,EAAC;oBAEnF;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrD,OAAA;sBACEoE,IAAI,EAAC,MAAM;sBACXC,QAAQ;sBACRhC,KAAK,EAAEhC,WAAW,CAACO,QAAS;sBAC5B0D,QAAQ,EAAGvC,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;sBACjEmC,QAAQ,EAAE/C,SAAU;sBACpBgB,SAAS,EAAE3C,EAAE,CACX,2FAA2F,EAC3F,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;sBACF2E,WAAW,EAAC;oBAAW;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNrD,OAAA,CAACf,MAAM,CAACyF,MAAM;kBACZnB,UAAU,EAAE;oBAAET,KAAK,EAAE;kBAAK,CAAE;kBAC5B6B,QAAQ,EAAE;oBAAE7B,KAAK,EAAE;kBAAK,CAAE;kBAC1BsB,IAAI,EAAC,QAAQ;kBACbI,QAAQ,EAAE/C,SAAU;kBACpBgB,SAAS,EAAE3C,EAAE,CACX,gGAAgG,EAChG,sFAAsF,EACtF,yEAAyE,EACzE,6EAA6E,EAC7E,2BACF,CAAE;kBAAA0C,QAAA,EAEDf,SAAS,gBACRzB,OAAA;oBAAKyC,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1CxC,OAAA;sBAAKyC,SAAS,EAAC;oBAA2E;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjGrD,OAAA;sBAAAwC,QAAA,EAAM;oBAAmB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,gBAENrD,OAAA;oBAAKyC,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1CxC,OAAA,CAACZ,aAAa;sBAACqD,SAAS,EAAC;oBAAS;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrCrD,OAAA;sBAAAwC,QAAA,EAAM;oBAAmB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACY,CAAC;cAAA,GAnLZ,SAAS;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoLF,CAAC,gBAEdrD,OAAA,CAACf,MAAM,CAACgF,IAAI;gBAEVtB,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE;gBAAG,CAAE;gBAC/BX,OAAO,EAAE;kBAAEH,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE;gBAAE,CAAE;gBAC9BQ,IAAI,EAAE;kBAAEtB,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BV,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAC9BkB,QAAQ,EAAEjC,iBAAkB;gBAC5BO,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBAGrBxC,OAAA;kBAAKyC,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,gBACpDxC,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAOyC,SAAS,EAAC,iEAAiE;sBAAAD,QAAA,EAAC;oBAEnF;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrD,OAAA;sBAAKyC,SAAS,EAAC,UAAU;sBAAAD,QAAA,gBACvBxC,OAAA;wBAAKyC,SAAS,EAAC,sEAAsE;wBAAAD,QAAA,eACnFxC,OAAA,CAACR,IAAI;0BAACiD,SAAS,EAAC;wBAAuB;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNrD,OAAA;wBACEoE,IAAI,EAAC,MAAM;wBACXC,QAAQ;wBACRhC,KAAK,EAAEpB,SAAS,CAACV,QAAS;wBAC1B+D,QAAQ,EAAGvC,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;wBAC/DmC,QAAQ,EAAE/C,SAAU;wBACpBgB,SAAS,EAAE3C,EAAE,CACX,iGAAiG,EACjG,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;wBACF2E,WAAW,EAAC;sBAAgB;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrD,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAOyC,SAAS,EAAC,iEAAiE;sBAAAD,QAAA,EAAC;oBAEnF;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrD,OAAA;sBAAKyC,SAAS,EAAC,UAAU;sBAAAD,QAAA,gBACvBxC,OAAA;wBAAKyC,SAAS,EAAC,sEAAsE;wBAAAD,QAAA,eACnFxC,OAAA,CAACX,KAAK;0BAACoD,SAAS,EAAC;wBAAuB;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACNrD,OAAA;wBACEoE,IAAI,EAAC,MAAM;wBACXC,QAAQ;wBACRhC,KAAK,EAAEpB,SAAS,CAACE,eAAgB;wBACjCmD,QAAQ,EAAGvC,CAAC,IAAKQ,iBAAiB,CAAC,iBAAiB,EAAER,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;wBACtEmC,QAAQ,EAAE/C,SAAU;wBACpBgB,SAAS,EAAE3C,EAAE,CACX,iGAAiG,EACjG,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;wBACF2E,WAAW,EAAC;sBAAa;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrD,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAOyC,SAAS,EAAC,iEAAiE;oBAAAD,QAAA,EAAC;kBAEnF;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrD,OAAA;oBAAKyC,SAAS,EAAC,UAAU;oBAAAD,QAAA,gBACvBxC,OAAA;sBAAKyC,SAAS,EAAC,sEAAsE;sBAAAD,QAAA,eACnFxC,OAAA,CAACV,IAAI;wBAACmD,SAAS,EAAC;sBAAuB;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNrD,OAAA;sBACEoE,IAAI,EAAC,OAAO;sBACZC,QAAQ;sBACRhC,KAAK,EAAEpB,SAAS,CAACT,KAAM;sBACvB8D,QAAQ,EAAGvC,CAAC,IAAKQ,iBAAiB,CAAC,OAAO,EAAER,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;sBAC5DmC,QAAQ,EAAE/C,SAAU;sBACpBgB,SAAS,EAAE3C,EAAE,CACX,iGAAiG,EACjG,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;sBACF2E,WAAW,EAAC;oBAAkB;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrD,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAOyC,SAAS,EAAC,iEAAiE;oBAAAD,QAAA,EAAC;kBAEnF;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrD,OAAA;oBAAKyC,SAAS,EAAC,UAAU;oBAAAD,QAAA,gBACvBxC,OAAA;sBAAKyC,SAAS,EAAC,sEAAsE;sBAAAD,QAAA,eACnFxC,OAAA,CAACT,IAAI;wBAACkD,SAAS,EAAC;sBAAuB;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNrD,OAAA;sBACEoE,IAAI,EAAC,UAAU;sBACfC,QAAQ;sBACRhC,KAAK,EAAEpB,SAAS,CAACR,QAAS;sBAC1B6D,QAAQ,EAAGvC,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;sBAC/DmC,QAAQ,EAAE/C,SAAU;sBACpBgB,SAAS,EAAE3C,EAAE,CACX,iGAAiG,EACjG,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;sBACF2E,WAAW,EAAC;oBAAmB;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrD,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAOyC,SAAS,EAAC,iEAAiE;oBAAAD,QAAA,EAAC;kBAEnF;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrD,OAAA;oBAAKyC,SAAS,EAAC,UAAU;oBAAAD,QAAA,gBACvBxC,OAAA;sBAAKyC,SAAS,EAAC,sEAAsE;sBAAAD,QAAA,eACnFxC,OAAA,CAACN,MAAM;wBAAC+C,SAAS,EAAC;sBAAuB;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,eACNrD,OAAA;sBACEoE,IAAI,EAAC,MAAM;sBACXC,QAAQ;sBACRhC,KAAK,EAAEpB,SAAS,CAACG,QAAS;sBAC1BkD,QAAQ,EAAGvC,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;sBAC/DmC,QAAQ,EAAE/C,SAAU;sBACpBgB,SAAS,EAAE3C,EAAE,CACX,iGAAiG,EACjG,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;sBACF2E,WAAW,EAAC;oBAAiB;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrD,OAAA;kBAAKyC,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,gBACpDxC,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAOyC,SAAS,EAAC,iEAAiE;sBAAAD,QAAA,EAAC;oBAEnF;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrD,OAAA;sBACEoE,IAAI,EAAC,MAAM;sBACX/B,KAAK,EAAEpB,SAAS,CAACI,aAAc;sBAC/BiD,QAAQ,EAAGvC,CAAC,IAAKQ,iBAAiB,CAAC,eAAe,EAAER,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;sBACpEmC,QAAQ,EAAE/C,SAAU;sBACpBgB,SAAS,EAAE3C,EAAE,CACX,2FAA2F,EAC3F,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;sBACF2E,WAAW,EAAC;oBAA2B;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENrD,OAAA;oBAAAwC,QAAA,gBACExC,OAAA;sBAAOyC,SAAS,EAAC,iEAAiE;sBAAAD,QAAA,EAAC;oBAEnF;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrD,OAAA;sBAAKyC,SAAS,EAAC,UAAU;sBAAAD,QAAA,gBACvBxC,OAAA;wBAAKyC,SAAS,EAAC,sEAAsE;wBAAAD,QAAA,eACnFxC,OAAA,CAACP,KAAK;0BAACgD,SAAS,EAAC;wBAAuB;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACNrD,OAAA;wBACEoE,IAAI,EAAC,KAAK;wBACVC,QAAQ;wBACRhC,KAAK,EAAEpB,SAAS,CAACJ,WAAY;wBAC7ByD,QAAQ,EAAGvC,CAAC,IAAKQ,iBAAiB,CAAC,aAAa,EAAER,CAAC,CAACwC,MAAM,CAAClC,KAAK,CAAE;wBAClEmC,QAAQ,EAAE/C,SAAU;wBACpBgB,SAAS,EAAE3C,EAAE,CACX,iGAAiG,EACjG,6EAA6E,EAC7E,+CAA+C,EAC/C,kCAAkC,EAClC,+BACF,CAAE;wBACF2E,WAAW,EAAC;sBAAc;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNrD,OAAA,CAACf,MAAM,CAACyF,MAAM;kBACZnB,UAAU,EAAE;oBAAET,KAAK,EAAE;kBAAK,CAAE;kBAC5B6B,QAAQ,EAAE;oBAAE7B,KAAK,EAAE;kBAAK,CAAE;kBAC1BsB,IAAI,EAAC,QAAQ;kBACbI,QAAQ,EAAE/C,SAAU;kBACpBgB,SAAS,EAAE3C,EAAE,CACX,gGAAgG,EAChG,sFAAsF,EACtF,yEAAyE,EACzE,6EAA6E,EAC7E,2BACF,CAAE;kBAAA0C,QAAA,EAEDf,SAAS,gBACRzB,OAAA;oBAAKyC,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1CxC,OAAA;sBAAKyC,SAAS,EAAC;oBAA2E;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjGrD,OAAA;sBAAAwC,QAAA,EAAM;oBAAmB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,gBAENrD,OAAA;oBAAKyC,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1CxC,OAAA,CAACX,KAAK;sBAACoD,SAAS,EAAC;oBAAS;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7BrD,OAAA;sBAAAwC,QAAA,EAAM;oBAAwB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACY,CAAC;cAAA,GAtNZ,OAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuNA;YACd;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC,eAGlBrD,OAAA,CAACf,MAAM,CAACyD,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBG,OAAO,EAAE;gBAAEH,OAAO,EAAE;cAAE,CAAE;cACxBI,UAAU,EAAE;gBAAEM,KAAK,EAAE,GAAG;gBAAEL,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,kBAAkB;cAAAD,QAAA,eAE5BxC,OAAA,CAACjB,IAAI;gBACH6F,EAAE,EAAC,QAAQ;gBACXnC,SAAS,EAAC,yGAAyG;gBAAAD,QAAA,EACpH;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAEvB,CAAC;AAACnD,EAAA,CA/jBID,kBAA4B;EAAA,QAwByCN,OAAO,EACzDC,QAAQ,EACdZ,WAAW;AAAA;AAAA6F,EAAA,GA1BxB5E,kBAA4B;AAikBlC,eAAeA,kBAAkB;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}