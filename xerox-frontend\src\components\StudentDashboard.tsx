import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Upload,
  FileText,
  Clock,
  CheckCircle,
  DollarSign,
  Download,
  MessageCircle,
  Info,
  Check,
  Star,
  MapPin,
  TrendingUp,
  Activity,
  Users,
  Printer,
  Eye,
  Send,
  X,
  Plus,
  Filter,
  Search,
  Calendar,
  BarChart3
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../services/api';
import { cn } from '../lib/utils';

interface PrintJob {
  id: number;
  jobNumber: string;
  fileName: string;
  status: string;
  cost?: number;
  estimatedCompletionTime?: string;
  xeroxCenterName: string;
  created: string;
}

interface XeroxCenter {
  id: number;
  name: string;
  location: string;
  pendingJobs: number;
  averageRating: number;
  isActive: boolean;
}

const StudentDashboard: React.FC = () => {
  const { user } = useAuth();
  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);
  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showChatModal, setShowChatModal] = useState(false);
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [chatMessage, setChatMessage] = useState('');
  const [messages, setMessages] = useState<any[]>([]);
  const [uploadData, setUploadData] = useState({
    remarks: '',
    preferredXeroxCenterId: '',
    printType: 'Print',
    copies: 1,
    colorType: 'BlackWhite',
    paperSize: 'A4'
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch print jobs
        const printJobsResponse = await printJobApi.getStudentJobs();
        setPrintJobs(printJobsResponse.data);

        // Fetch xerox centers
        const xeroxCentersResponse = await xeroxCenterApi.getAll();
        setXeroxCenters(xeroxCentersResponse.data);
      } catch (error) {
        console.error('Error fetching data:', error);
        // Set empty arrays on error
        setPrintJobs([]);
        setXeroxCenters([]);
      }
    };

    fetchData();
  }, []);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Requested': { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: Clock },
      'UnderReview': { color: 'bg-blue-100 text-blue-800 border-blue-200', icon: Eye },
      'Quoted': { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: DollarSign },
      'WaitingConfirmation': { color: 'bg-orange-100 text-orange-800 border-orange-200', icon: Clock },
      'Confirmed': { color: 'bg-indigo-100 text-indigo-800 border-indigo-200', icon: Check },
      'InProgress': { color: 'bg-purple-100 text-purple-800 border-purple-200', icon: Activity },
      'Completed': { color: 'bg-green-100 text-green-800 border-green-200', icon: CheckCircle },
      'Delivered': { color: 'bg-emerald-100 text-emerald-800 border-emerald-200', icon: CheckCircle },
      'Rejected': { color: 'bg-red-100 text-red-800 border-red-200', icon: X },
      'Cancelled': { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: X }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      color: 'bg-gray-100 text-gray-800 border-gray-200',
      icon: Info
    };

    const IconComponent = config.icon;

    return (
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className={cn(
          "inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border",
          config.color
        )}
      >
        <IconComponent className="w-3 h-3" />
        {status}
      </motion.div>
    );
  };

  const getWorkloadColor = (pendingJobs: number) => {
    if (pendingJobs <= 5) return 'success';
    if (pendingJobs <= 10) return 'warning';
    return 'danger';
  };

  const handleFileUpload = async () => {
    if (!selectedFile) return;

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('remarks', uploadData.remarks);
      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);
      formData.append('printType', uploadData.printType);
      formData.append('copies', uploadData.copies.toString());
      formData.append('colorType', uploadData.colorType);
      formData.append('paperSize', uploadData.paperSize);

      await fileUploadApi.uploadFile(formData);

      // Refresh print jobs after upload
      const printJobsResponse = await printJobApi.getStudentJobs();
      setPrintJobs(printJobsResponse.data);

      setShowUploadModal(false);
      setSelectedFile(null);
      setUploadData({
        remarks: '',
        preferredXeroxCenterId: '',
        printType: 'Print',
        copies: 1,
        colorType: 'BlackWhite',
        paperSize: 'A4'
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      // You might want to show an error message to the user here
    }
  };

  const handleViewJob = (job: any) => {
    setSelectedJob(job);
    setShowViewModal(true);
  };

  const handleOpenChat = async (job: any) => {
    try {
      setSelectedJob(job);
      setShowChatModal(true);

      // Load messages for this job
      const response = await messageApi.getJobMessages(job.id);
      setMessages(response.data);
    } catch (error) {
      console.error('Error loading messages:', error);
      setMessages([]);
    }
  };

  const handleDownloadFile = async (jobId: number, fileName: string) => {
    try {
      const response = await fileUploadApi.downloadFile(jobId);

      // Create blob URL and trigger download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
      // You might want to show an error message to the user here
    }
  };

  const handleConfirmJob = async (jobId: number) => {
    try {
      await printJobApi.confirmJob(jobId);

      // Refresh print jobs after confirmation
      const printJobsResponse = await printJobApi.getStudentJobs();
      setPrintJobs(printJobsResponse.data);
    } catch (error) {
      console.error('Error confirming job:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!chatMessage.trim() || !selectedJob) return;

    try {
      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());

      // Add the new message to the list
      setMessages(prev => [...prev, response.data]);
      setChatMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const { isDarkMode } = useTheme();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
        >
          <div className="space-y-2">
            <motion.h1
              className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              Student Dashboard
            </motion.h1>
            <motion.p
              className="text-slate-600 dark:text-slate-300 text-lg"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              Welcome back, <span className="font-semibold text-blue-600 dark:text-blue-400">{user?.username}</span>!
            </motion.p>
          </div>

          <motion.button
            onClick={() => setShowUploadModal(true)}
            className={cn(
              "group relative px-6 py-3 rounded-xl font-medium transition-all duration-300",
              "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",
              "text-white shadow-lg hover:shadow-xl transform hover:scale-105",
              "border border-white/20 backdrop-blur-sm"
            )}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              <span>Upload Files</span>
            </div>
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-600/20 to-purple-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </motion.button>
        </motion.div>

        {/* Statistics Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, staggerChildren: 0.1 }}
        >
          {[
            {
              title: "Total Jobs",
              value: printJobs.length,
              icon: FileText,
              color: "from-blue-500 to-blue-600",
              bgColor: "bg-blue-50 dark:bg-blue-900/20",
              textColor: "text-blue-600 dark:text-blue-400",
              change: "+12%",
              changeType: "positive"
            },
            {
              title: "In Progress",
              value: printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length,
              icon: Clock,
              color: "from-orange-500 to-orange-600",
              bgColor: "bg-orange-50 dark:bg-orange-900/20",
              textColor: "text-orange-600 dark:text-orange-400",
              change: "+5%",
              changeType: "positive"
            },
            {
              title: "Completed",
              value: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,
              icon: CheckCircle,
              color: "from-green-500 to-green-600",
              bgColor: "bg-green-50 dark:bg-green-900/20",
              textColor: "text-green-600 dark:text-green-400",
              change: "+8%",
              changeType: "positive"
            },
            {
              title: "Total Spent",
              value: `$${printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)}`,
              icon: DollarSign,
              color: "from-purple-500 to-purple-600",
              bgColor: "bg-purple-50 dark:bg-purple-900/20",
              textColor: "text-purple-600 dark:text-purple-400",
              change: "+15%",
              changeType: "positive"
            }
          ].map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ delay: 0.6 + index * 0.1 }}
                whileHover={{ y: -5, scale: 1.02 }}
                className={cn(
                  "relative p-6 rounded-2xl border border-white/20 backdrop-blur-sm",
                  "bg-white/70 dark:bg-slate-800/70 shadow-lg hover:shadow-xl",
                  "transition-all duration-300 group cursor-pointer"
                )}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={cn("p-3 rounded-xl", stat.bgColor)}>
                    <IconComponent className={cn("w-6 h-6", stat.textColor)} />
                  </div>
                  <div className="flex items-center gap-1 text-sm">
                    <TrendingUp className="w-4 h-4 text-green-500" />
                    <span className="text-green-600 font-medium">{stat.change}</span>
                  </div>
                </div>

                <div className="space-y-1">
                  <p className="text-slate-600 dark:text-slate-300 text-sm font-medium">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-slate-900 dark:text-white">
                    {stat.value}
                  </p>
                </div>

                {/* Gradient overlay on hover */}
                <div className={cn(
                  "absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-10",
                  "bg-gradient-to-br transition-opacity duration-300",
                  stat.color
                )} />
              </motion.div>
            );
          })}
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Jobs */}
          <motion.div
            className="lg:col-span-2"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.8 }}
          >
            <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl border border-white/20 shadow-lg">
              <div className="p-6 border-b border-slate-200 dark:border-slate-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                      <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                      Recent Print Jobs
                    </h3>
                  </div>
                  <div className="flex items-center gap-2">
                    <button className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors">
                      <Filter className="w-4 h-4 text-slate-600 dark:text-slate-400" />
                    </button>
                    <button className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors">
                      <Search className="w-4 h-4 text-slate-600 dark:text-slate-400" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {printJobs.length > 0 ? (
                  <div className="space-y-4">
                    {printJobs.map((job, index) => (
                      <motion.div
                        key={job.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.9 + index * 0.1 }}
                        className="group p-4 rounded-xl border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300 hover:shadow-md"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 flex-1">
                            <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                              <FileText className="w-5 h-5 text-red-600 dark:text-red-400" />
                            </div>

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-semibold text-slate-900 dark:text-white">
                                  {job.jobNumber}
                                </span>
                                {getStatusBadge(job.status)}
                              </div>
                              <p className="text-sm text-slate-600 dark:text-slate-300 truncate">
                                {job.fileName}
                              </p>
                              <div className="flex items-center gap-4 mt-2 text-xs text-slate-500 dark:text-slate-400">
                                <span className="flex items-center gap-1">
                                  <MapPin className="w-3 h-3" />
                                  {job.xeroxCenterName}
                                </span>
                                <span className="flex items-center gap-1">
                                  <DollarSign className="w-3 h-3" />
                                  {job.cost ? `$${job.cost.toFixed(2)}` : 'Not quoted'}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Calendar className="w-3 h-3" />
                                  {new Date(job.created).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => handleDownloadFile(job.id, job.fileName)}
                              className="p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors"
                              title="Download File"
                            >
                              <Download className="w-4 h-4" />
                            </motion.button>

                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => handleViewJob(job)}
                              className="p-2 text-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors"
                              title="View Details"
                            >
                              <Info className="w-4 h-4" />
                            </motion.button>

                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => handleOpenChat(job)}
                              className="p-2 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-colors"
                              title="Chat"
                            >
                              <MessageCircle className="w-4 h-4" />
                            </motion.button>

                            {job.status === 'Quoted' && (
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => handleConfirmJob(job.id)}
                                className="p-2 text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
                                title="Confirm Quote"
                              >
                                <Check className="w-4 h-4" />
                              </motion.button>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="text-center py-12"
                  >
                    <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FileText className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                      No print jobs yet
                    </h3>
                    <p className="text-slate-600 dark:text-slate-300 mb-4">
                      Upload your first file to get started with printing!
                    </p>
                    <motion.button
                      onClick={() => setShowUploadModal(true)}
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Upload File
                    </motion.button>
                  </motion.div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Xerox Centers */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1.0 }}
          >
            <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl border border-white/20 shadow-lg">
              <div className="p-6 border-b border-slate-200 dark:border-slate-700">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <Printer className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                    Available Centers
                  </h3>
                </div>
              </div>

              <div className="p-6 space-y-4">
                {xeroxCenters.map((center, index) => (
                  <motion.div
                    key={center.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.1 + index * 0.1 }}
                    className="group p-4 rounded-xl border border-slate-200 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-300 hover:shadow-md"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="font-semibold text-slate-900 dark:text-white mb-1">
                          {center.name}
                        </h4>
                        <div className="flex items-center gap-1 text-sm text-slate-600 dark:text-slate-300 mb-2">
                          <MapPin className="w-3 h-3" />
                          {center.location}
                        </div>
                      </div>
                      <div className={cn(
                        "px-2.5 py-1 rounded-full text-xs font-medium",
                        center.pendingJobs <= 5
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                          : center.pendingJobs <= 10
                          ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                      )}>
                        {center.pendingJobs} jobs
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                          {center.averageRating.toFixed(1)}
                        </span>
                      </div>

                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors opacity-0 group-hover:opacity-100"
                      >
                        Select Center
                      </motion.button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Upload Modal */}
        <AnimatePresence>
          {showUploadModal && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
              onClick={() => setShowUploadModal(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
                className="w-full max-w-2xl bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-white/20"
              >
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                      <Upload className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
                      Upload Files for Printing
                    </h2>
                  </div>
                  <button
                    onClick={() => setShowUploadModal(false)}
                    className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                  </button>
                </div>

                {/* Body */}
                <div className="p-6 space-y-6">
                  {/* File Upload */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                      Select File
                    </label>
                    <div className="relative">
                      <input
                        type="file"
                        accept=".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png"
                        onChange={(e) => {
                          const files = (e.target as HTMLInputElement).files;
                          setSelectedFile(files ? files[0] : null);
                        }}
                        className="block w-full text-sm text-slate-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900/30 dark:file:text-blue-400"
                      />
                    </div>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)
                    </p>
                  </div>

                  {/* Form Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        Print Type
                      </label>
                      <select
                        value={uploadData.printType}
                        onChange={(e) => setUploadData(prev => ({ ...prev, printType: e.target.value }))}
                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="Print">Print</option>
                        <option value="Xerox">Xerox</option>
                        <option value="Binding">Binding</option>
                        <option value="Lamination">Lamination</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        Number of Copies
                      </label>
                      <input
                        type="number"
                        min="1"
                        value={uploadData.copies}
                        onChange={(e) => setUploadData(prev => ({ ...prev, copies: parseInt(e.target.value) }))}
                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        Color Type
                      </label>
                      <select
                        value={uploadData.colorType}
                        onChange={(e) => setUploadData(prev => ({ ...prev, colorType: e.target.value }))}
                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="BlackWhite">Black & White</option>
                        <option value="Color">Color</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        Paper Size
                      </label>
                      <select
                        value={uploadData.paperSize}
                        onChange={(e) => setUploadData(prev => ({ ...prev, paperSize: e.target.value }))}
                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="A4">A4</option>
                        <option value="A3">A3</option>
                        <option value="Letter">Letter</option>
                        <option value="Legal">Legal</option>
                      </select>
                    </div>
                  </div>

                  {/* Xerox Center Selection */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                      Preferred Xerox Center
                    </label>
                    <select
                      value={uploadData.preferredXeroxCenterId}
                      onChange={(e) => setUploadData(prev => ({ ...prev, preferredXeroxCenterId: e.target.value }))}
                      className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select a center (optional)</option>
                      {xeroxCenters.map(center => (
                        <option key={center.id} value={center.id}>
                          {center.name} - {center.pendingJobs} pending jobs
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Remarks */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                      Remarks
                    </label>
                    <textarea
                      rows={3}
                      placeholder="Any special instructions or remarks..."
                      value={uploadData.remarks}
                      onChange={(e) => setUploadData(prev => ({ ...prev, remarks: e.target.value }))}
                      className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                  </div>
                </div>

                {/* Footer */}
                <div className="flex items-center justify-end gap-3 p-6 border-t border-slate-200 dark:border-slate-700">
                  <motion.button
                    onClick={() => setShowUploadModal(false)}
                    className="px-4 py-2 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    onClick={handleFileUpload}
                    disabled={!selectedFile}
                    className={cn(
                      "px-6 py-2 rounded-lg font-medium transition-all duration-300",
                      selectedFile
                        ? "bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl"
                        : "bg-slate-300 dark:bg-slate-600 text-slate-500 dark:text-slate-400 cursor-not-allowed"
                    )}
                    whileHover={selectedFile ? { scale: 1.02 } : {}}
                    whileTap={selectedFile ? { scale: 0.98 } : {}}
                  >
                    <div className="flex items-center gap-2">
                      <Upload className="w-4 h-4" />
                      Upload File
                    </div>
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* View Job Details Modal */}
        <AnimatePresence>
          {showViewModal && selectedJob && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
              onClick={() => setShowViewModal(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
                className="w-full max-w-3xl bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-white/20"
              >
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                      <Eye className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
                      Job Details - {selectedJob.jobNumber}
                    </h2>
                  </div>
                  <button
                    onClick={() => setShowViewModal(false)}
                    className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                  </button>
                </div>

                {/* Body */}
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* File Information */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white border-b border-slate-200 dark:border-slate-700 pb-2">
                        File Information
                      </h3>
                      <div className="space-y-3">
                        <div>
                          <span className="text-sm font-medium text-slate-600 dark:text-slate-400">File Name:</span>
                          <p className="text-slate-900 dark:text-white">{selectedJob.fileName}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Print Type:</span>
                          <p className="text-slate-900 dark:text-white">{selectedJob.printType}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Copies:</span>
                          <p className="text-slate-900 dark:text-white">{selectedJob.copies}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Color Type:</span>
                          <p className="text-slate-900 dark:text-white">{selectedJob.colorType}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Paper Size:</span>
                          <p className="text-slate-900 dark:text-white">{selectedJob.paperSize}</p>
                        </div>
                      </div>
                    </div>

                    {/* Job Information */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white border-b border-slate-200 dark:border-slate-700 pb-2">
                        Job Information
                      </h3>
                      <div className="space-y-3">
                        <div>
                          <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Status:</span>
                          <div className="mt-1">{getStatusBadge(selectedJob.status)}</div>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Cost:</span>
                          <p className="text-slate-900 dark:text-white">
                            {selectedJob.cost ? `$${selectedJob.cost.toFixed(2)}` : 'Not quoted yet'}
                          </p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Xerox Center:</span>
                          <p className="text-slate-900 dark:text-white">{selectedJob.xeroxCenterName}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Created:</span>
                          <p className="text-slate-900 dark:text-white">{new Date(selectedJob.created).toLocaleString()}</p>
                        </div>
                        {selectedJob.estimatedCompletionTime && (
                          <div>
                            <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Estimated Completion:</span>
                            <p className="text-slate-900 dark:text-white">
                              {new Date(selectedJob.estimatedCompletionTime).toLocaleString()}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Remarks */}
                  {selectedJob.remarks && (
                    <div className="mt-6 p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                      <h4 className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-2">Remarks</h4>
                      <p className="text-slate-900 dark:text-white">{selectedJob.remarks}</p>
                    </div>
                  )}

                  {/* Quote Alert */}
                  {selectedJob.status === 'Quoted' && (
                    <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                      <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-400">
                        <Info className="w-5 h-5" />
                        <span className="font-medium">Quote Available</span>
                      </div>
                      <p className="text-yellow-700 dark:text-yellow-300 mt-1">
                        This job has been quoted. Please confirm to proceed with printing.
                      </p>
                    </div>
                  )}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-end gap-3 p-6 border-t border-slate-200 dark:border-slate-700">
                  {selectedJob.status === 'Quoted' && (
                    <motion.button
                      onClick={() => {
                        handleConfirmJob(selectedJob.id);
                        setShowViewModal(false);
                      }}
                      className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center gap-2">
                        <Check className="w-4 h-4" />
                        Confirm Quote
                      </div>
                    </motion.button>
                  )}
                  <motion.button
                    onClick={() => setShowViewModal(false)}
                    className="px-4 py-2 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Close
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Chat Modal */}
        <AnimatePresence>
          {showChatModal && selectedJob && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
              onClick={() => setShowChatModal(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
                className="w-full max-w-2xl bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-white/20 flex flex-col h-[600px]"
              >
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <MessageCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                    <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
                      Chat - {selectedJob.jobNumber}
                    </h2>
                  </div>
                  <button
                    onClick={() => setShowChatModal(false)}
                    className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                  </button>
                </div>

                {/* Messages */}
                <div className="flex-1 p-6 overflow-y-auto">
                  {messages.length > 0 ? (
                    <div className="space-y-4">
                      {messages.map((message) => (
                        <motion.div
                          key={message.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className={`flex ${message.isFromCurrentUser ? 'justify-end' : 'justify-start'}`}
                        >
                          <div className={cn(
                            "max-w-[70%] p-3 rounded-2xl",
                            message.isFromCurrentUser
                              ? "bg-blue-600 text-white rounded-br-md"
                              : "bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white rounded-bl-md"
                          )}>
                            <p className="text-sm">{message.content}</p>
                            <p className={cn(
                              "text-xs mt-1",
                              message.isFromCurrentUser
                                ? "text-blue-100"
                                : "text-slate-500 dark:text-slate-400"
                            )}>
                              {message.senderName} - {new Date(message.sentAt).toLocaleString()}
                            </p>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full text-center">
                      <div className="w-16 h-16 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center mb-4">
                        <MessageCircle className="w-8 h-8 text-slate-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                        No messages yet
                      </h3>
                      <p className="text-slate-600 dark:text-slate-300">
                        Start a conversation with the xerox center!
                      </p>
                    </div>
                  )}
                </div>

                {/* Message Input */}
                <div className="p-6 border-t border-slate-200 dark:border-slate-700">
                  <div className="flex gap-3">
                    <input
                      type="text"
                      placeholder="Type your message..."
                      value={chatMessage}
                      onChange={(e) => setChatMessage(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                      className="flex-1 px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <motion.button
                      onClick={handleSendMessage}
                      disabled={!chatMessage.trim()}
                      className={cn(
                        "px-4 py-2 rounded-lg transition-all duration-300",
                        chatMessage.trim()
                          ? "bg-blue-600 hover:bg-blue-700 text-white"
                          : "bg-slate-300 dark:bg-slate-600 text-slate-500 dark:text-slate-400 cursor-not-allowed"
                      )}
                      whileHover={chatMessage.trim() ? { scale: 1.02 } : {}}
                      whileTap={chatMessage.trim() ? { scale: 0.98 } : {}}
                    >
                      <Send className="w-4 h-4" />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default StudentDashboard;
