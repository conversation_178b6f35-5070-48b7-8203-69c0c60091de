{"ast": null, "code": "import { memo } from 'motion-utils';\nconst supportsPartialKeyframes = /*@__PURE__*/memo(() => {\n  try {\n    document.createElement(\"div\").animate({\n      opacity: [1]\n    });\n  } catch (e) {\n    return false;\n  }\n  return true;\n});\nexport { supportsPartialKeyframes };", "map": {"version": 3, "names": ["memo", "supportsPartialKeyframes", "document", "createElement", "animate", "opacity", "e"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/animation/waapi/supports/partial-keyframes.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\n\nconst supportsPartialKeyframes = /*@__PURE__*/ memo(() => {\n    try {\n        document.createElement(\"div\").animate({ opacity: [1] });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n});\n\nexport { supportsPartialKeyframes };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,cAAc;AAEnC,MAAMC,wBAAwB,GAAG,aAAcD,IAAI,CAAC,MAAM;EACtD,IAAI;IACAE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAACC,OAAO,CAAC;MAAEC,OAAO,EAAE,CAAC,CAAC;IAAE,CAAC,CAAC;EAC3D,CAAC,CACD,OAAOC,CAAC,EAAE;IACN,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC,CAAC;AAEF,SAASL,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}