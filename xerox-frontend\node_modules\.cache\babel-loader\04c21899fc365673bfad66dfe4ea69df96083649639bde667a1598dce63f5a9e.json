{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\AceternityStudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col } from 'react-bootstrap';\nimport { motion } from 'framer-motion';\nimport { Upload, FileText, Clock, CheckCircle, DollarSign, Download, MessageCircle, Star, MapPin, Printer, Eye, Plus, Activity } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../../services/api';\nimport { AceternityCard, AceternityStatsCard, AceternityButton, AceternityBadge } from './AceternityCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AceternityStudentDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [xeroxCenters, setXeroxCenters] = useState([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n    fetchData();\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        variant: 'default',\n        color: 'text-gray-600'\n      },\n      'UnderReview': {\n        variant: 'info',\n        color: 'text-blue-600'\n      },\n      'Quoted': {\n        variant: 'warning',\n        color: 'text-yellow-600'\n      },\n      'WaitingConfirmation': {\n        variant: 'warning',\n        color: 'text-orange-600'\n      },\n      'Confirmed': {\n        variant: 'info',\n        color: 'text-blue-600'\n      },\n      'InProgress': {\n        variant: 'info',\n        color: 'text-purple-600'\n      },\n      'Completed': {\n        variant: 'success',\n        color: 'text-green-600'\n      },\n      'Delivered': {\n        variant: 'success',\n        color: 'text-green-600'\n      },\n      'Rejected': {\n        variant: 'error',\n        color: 'text-red-600'\n      },\n      'Cancelled': {\n        variant: 'default',\n        color: 'text-gray-600'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'default',\n      color: 'text-gray-600'\n    };\n    return /*#__PURE__*/_jsxDEV(AceternityBadge, {\n      variant: config.variant,\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this);\n  };\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n      await fileUploadApi.uploadFile(formData);\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n    }\n  };\n  const handleViewJob = job => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n  const handleOpenChat = async job => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n  const handleDownloadFile = async (jobId, fileName) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n    }\n  };\n  const handleConfirmJob = async jobId => {\n    try {\n      await printJobApi.confirmJob(jobId);\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n  const totalSpent = printJobs.reduce((sum, job) => sum + (job.cost || 0), 0);\n  const inProgressJobs = printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length;\n  const completedJobs = printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      className: \"py-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2\",\n          children: \"Student Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 text-lg mb-6\",\n          children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: user === null || user === void 0 ? void 0 : user.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 27\n          }, this), \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: /*#__PURE__*/_jsxDEV(AceternityButton, {\n            onClick: () => setShowUploadModal(true),\n            className: \"px-8 py-3 text-lg\",\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), \"Upload Files\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-6 g-4\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"Total Jobs\",\n            value: printJobs.length,\n            icon: /*#__PURE__*/_jsxDEV(FileText, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 21\n            }, this),\n            color: \"blue\",\n            trend: {\n              value: 12,\n              isPositive: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"In Progress\",\n            value: inProgressJobs,\n            icon: /*#__PURE__*/_jsxDEV(Clock, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 21\n            }, this),\n            color: \"orange\",\n            trend: {\n              value: 5,\n              isPositive: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"Completed\",\n            value: completedJobs,\n            icon: /*#__PURE__*/_jsxDEV(CheckCircle, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 21\n            }, this),\n            color: \"green\",\n            trend: {\n              value: 8,\n              isPositive: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"Total Spent\",\n            value: `$${totalSpent.toFixed(2)}`,\n            icon: /*#__PURE__*/_jsxDEV(DollarSign, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 21\n            }, this),\n            color: \"purple\",\n            trend: {\n              value: 15,\n              isPositive: false\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"g-4\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 8,\n          children: /*#__PURE__*/_jsxDEV(AceternityCard, {\n            className: \"h-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(Activity, {\n                  className: \"w-5 h-5 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), \"Recent Print Jobs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(AceternityBadge, {\n                variant: \"info\",\n                children: [printJobs.length, \" jobs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), printJobs.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: printJobs.slice(0, 5).map((job, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: index * 0.1\n                },\n                className: \"p-4 rounded-xl bg-white/50 dark:bg-black/20 border border-white/20 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-all duration-200\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white\",\n                      children: /*#__PURE__*/_jsxDEV(FileText, {\n                        size: 20\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 302,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900 dark:text-white\",\n                        children: job.jobNumber\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 305,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: job.fileName.length > 40 ? job.fileName.slice(0, 40) + '...' : job.fileName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500 dark:text-gray-500\",\n                        children: job.xeroxCenterName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 311,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-right\",\n                      children: [getStatusBadge(job.status), job.cost && /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-semibold text-green-600 dark:text-green-400 mt-1\",\n                        children: [\"$\", job.cost.toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.1\n                        },\n                        whileTap: {\n                          scale: 0.9\n                        },\n                        onClick: () => handleDownloadFile(job.id, job.fileName),\n                        className: \"p-2 rounded-lg bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 transition-colors\",\n                        title: \"Download\",\n                        children: /*#__PURE__*/_jsxDEV(Download, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 335,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 328,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.1\n                        },\n                        whileTap: {\n                          scale: 0.9\n                        },\n                        onClick: () => handleViewJob(job),\n                        className: \"p-2 rounded-lg bg-purple-500/10 text-purple-600 hover:bg-purple-500/20 transition-colors\",\n                        title: \"View Details\",\n                        children: /*#__PURE__*/_jsxDEV(Eye, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 345,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 338,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.1\n                        },\n                        whileTap: {\n                          scale: 0.9\n                        },\n                        onClick: () => handleOpenChat(job),\n                        className: \"p-2 rounded-lg bg-green-500/10 text-green-600 hover:bg-green-500/20 transition-colors\",\n                        title: \"Chat\",\n                        children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 355,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 348,\n                        columnNumber: 29\n                      }, this), job.status === 'Quoted' && /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.1\n                        },\n                        whileTap: {\n                          scale: 0.9\n                        },\n                        onClick: () => handleConfirmJob(job.id),\n                        className: \"p-2 rounded-lg bg-green-500 text-white hover:bg-green-600 transition-colors\",\n                        title: \"Confirm Quote\",\n                        children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 366,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 359,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 327,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this)\n              }, job.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(Upload, {\n                  className: \"w-10 h-10 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                children: \"No print jobs yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                children: \"Upload your first file to get started!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(AceternityButton, {\n                onClick: () => setShowUploadModal(true),\n                children: [/*#__PURE__*/_jsxDEV(Plus, {\n                  className: \"w-4 h-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this), \"Upload File\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          children: /*#__PURE__*/_jsxDEV(AceternityCard, {\n            className: \"h-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(Printer, {\n                  className: \"w-5 h-5 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this), \"Available Centers\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(AceternityBadge, {\n                variant: \"success\",\n                children: [xeroxCenters.filter(c => c.isActive).length, \" active\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: xeroxCenters.slice(0, 4).map((center, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: index * 0.1\n                },\n                className: \"p-4 rounded-xl bg-white/50 dark:bg-black/20 border border-white/20 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-all duration-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-gray-900 dark:text-white\",\n                      children: center.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                        size: 14,\n                        className: \"mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 427,\n                        columnNumber: 27\n                      }, this), center.location]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 426,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(AceternityBadge, {\n                    variant: center.pendingJobs <= 5 ? 'success' : center.pendingJobs <= 10 ? 'warning' : 'error',\n                    size: \"sm\",\n                    children: [center.pendingJobs, \" jobs\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Star, {\n                      className: \"w-4 h-4 text-yellow-500 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                      children: center.averageRating.toFixed(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(AceternityButton, {\n                    variant: \"outline\",\n                    size: \"sm\",\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this)]\n              }, center.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this);\n};\n_s(AceternityStudentDashboard, \"8ErydrNvcotwx3lsAKqva+c5cB0=\", false, function () {\n  return [useAuth];\n});\n_c = AceternityStudentDashboard;\nexport default AceternityStudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"AceternityStudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "motion", "Upload", "FileText", "Clock", "CheckCircle", "DollarSign", "Download", "MessageCircle", "Star", "MapPin", "Printer", "Eye", "Plus", "Activity", "useAuth", "printJobApi", "xeroxCenterApi", "fileUploadApi", "messageApi", "AceternityCard", "AceternityStatsCard", "AceternityButton", "AceternityBadge", "jsxDEV", "_jsxDEV", "AceternityStudentDashboard", "_s", "user", "printJobs", "setPrintJobs", "xeroxCenters", "setXeroxCenters", "showUploadModal", "setShowUploadModal", "showViewModal", "setShowViewModal", "showChatModal", "setShowChatModal", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "selectedFile", "setSelectedFile", "chatMessage", "setChatMessage", "messages", "setMessages", "uploadData", "setUploadData", "remarks", "preferredXeroxCenterId", "printType", "copies", "colorType", "paperSize", "fetchData", "printJobsResponse", "getStudentJobs", "data", "xeroxCentersResponse", "getAll", "error", "console", "getStatusBadge", "status", "statusConfig", "variant", "color", "config", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleFileUpload", "formData", "FormData", "append", "toString", "uploadFile", "handleViewJob", "job", "handleOpenChat", "response", "getJobMessages", "id", "handleDownloadFile", "jobId", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleConfirmJob", "<PERSON><PERSON>ob", "handleSendMessage", "trim", "sendMessage", "prev", "totalSpent", "reduce", "sum", "cost", "inProgressJobs", "filter", "includes", "length", "completedJobs", "className", "fluid", "div", "initial", "opacity", "y", "animate", "transition", "duration", "username", "whileHover", "scale", "whileTap", "onClick", "md", "title", "value", "icon", "size", "trend", "isPositive", "toFixed", "lg", "slice", "map", "index", "x", "delay", "jobNumber", "xeroxCenterName", "button", "c", "isActive", "center", "name", "location", "pendingJobs", "averageRating", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/AceternityStudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Table, Badge, Alert, Form, Modal } from 'react-bootstrap';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Upload, \n  FileText, \n  Clock, \n  CheckCircle, \n  DollarSign, \n  Download, \n  MessageCircle, \n  Info, \n  Star, \n  MapPin,\n  Printer,\n  Eye,\n  Plus,\n  TrendingUp,\n  Activity\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../../services/api';\nimport { AceternityCard, AceternityStatsCard, AceternityButton, AceternityBadge } from './AceternityCard';\nimport { cn } from '../../lib/utils';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  xeroxCenterName: string;\n  created: string;\n}\n\ninterface XeroxCenter {\n  id: number;\n  name: string;\n  location: string;\n  pendingJobs: number;\n  averageRating: number;\n  isActive: boolean;\n}\n\nconst AceternityStudentDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState<any>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState<any[]>([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { variant: 'default' as const, color: 'text-gray-600' },\n      'UnderReview': { variant: 'info' as const, color: 'text-blue-600' },\n      'Quoted': { variant: 'warning' as const, color: 'text-yellow-600' },\n      'WaitingConfirmation': { variant: 'warning' as const, color: 'text-orange-600' },\n      'Confirmed': { variant: 'info' as const, color: 'text-blue-600' },\n      'InProgress': { variant: 'info' as const, color: 'text-purple-600' },\n      'Completed': { variant: 'success' as const, color: 'text-green-600' },\n      'Delivered': { variant: 'success' as const, color: 'text-green-600' },\n      'Rejected': { variant: 'error' as const, color: 'text-red-600' },\n      'Cancelled': { variant: 'default' as const, color: 'text-gray-600' }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'default' as const, color: 'text-gray-600' };\n    \n    return (\n      <AceternityBadge variant={config.variant}>\n        {status}\n      </AceternityBadge>\n    );\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n\n      await fileUploadApi.uploadFile(formData);\n\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n    }\n  };\n\n  const handleViewJob = (job: any) => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n\n  const handleOpenChat = async (job: any) => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n    }\n  };\n\n  const handleConfirmJob = async (jobId: number) => {\n    try {\n      await printJobApi.confirmJob(jobId);\n\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  const totalSpent = printJobs.reduce((sum, job) => sum + (job.cost || 0), 0);\n  const inProgressJobs = printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length;\n  const completedJobs = printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n\n  return (\n    <div className=\"min-h-screen\">\n      <Container fluid className=\"py-6\">\n        {/* Header Section */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-8\"\n        >\n          <h1 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2\">\n            Student Dashboard\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 text-lg mb-6\">\n            Welcome back, <span className=\"font-semibold\">{user?.username}</span>! \n          </p>\n          \n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <AceternityButton\n              onClick={() => setShowUploadModal(true)}\n              className=\"px-8 py-3 text-lg\"\n            >\n              <Upload className=\"w-5 h-5 mr-2\" />\n              Upload Files\n            </AceternityButton>\n          </motion.div>\n        </motion.div>\n\n        {/* Statistics Cards */}\n        <Row className=\"mb-6 g-4\">\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"Total Jobs\"\n              value={printJobs.length}\n              icon={<FileText size={24} />}\n              color=\"blue\"\n              trend={{ value: 12, isPositive: true }}\n            />\n          </Col>\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"In Progress\"\n              value={inProgressJobs}\n              icon={<Clock size={24} />}\n              color=\"orange\"\n              trend={{ value: 5, isPositive: true }}\n            />\n          </Col>\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"Completed\"\n              value={completedJobs}\n              icon={<CheckCircle size={24} />}\n              color=\"green\"\n              trend={{ value: 8, isPositive: true }}\n            />\n          </Col>\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"Total Spent\"\n              value={`$${totalSpent.toFixed(2)}`}\n              icon={<DollarSign size={24} />}\n              color=\"purple\"\n              trend={{ value: 15, isPositive: false }}\n            />\n          </Col>\n        </Row>\n\n        <Row className=\"g-4\">\n          {/* Recent Jobs */}\n          <Col lg={8}>\n            <AceternityCard className=\"h-100\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                  <Activity className=\"w-5 h-5 inline mr-2\" />\n                  Recent Print Jobs\n                </h3>\n                <AceternityBadge variant=\"info\">\n                  {printJobs.length} jobs\n                </AceternityBadge>\n              </div>\n\n              {printJobs.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {printJobs.slice(0, 5).map((job, index) => (\n                    <motion.div\n                      key={job.id}\n                      initial={{ opacity: 0, x: -20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: index * 0.1 }}\n                      className=\"p-4 rounded-xl bg-white/50 dark:bg-black/20 border border-white/20 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-all duration-200\"\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-4\">\n                          <div className=\"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white\">\n                            <FileText size={20} />\n                          </div>\n                          <div>\n                            <h4 className=\"font-semibold text-gray-900 dark:text-white\">\n                              {job.jobNumber}\n                            </h4>\n                            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                              {job.fileName.length > 40 ? job.fileName.slice(0, 40) + '...' : job.fileName}\n                            </p>\n                            <p className=\"text-xs text-gray-500 dark:text-gray-500\">\n                              {job.xeroxCenterName}\n                            </p>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"text-right\">\n                            {getStatusBadge(job.status)}\n                            {job.cost && (\n                              <p className=\"text-sm font-semibold text-green-600 dark:text-green-400 mt-1\">\n                                ${job.cost.toFixed(2)}\n                              </p>\n                            )}\n                          </div>\n                          \n                          <div className=\"flex space-x-1\">\n                            <motion.button\n                              whileHover={{ scale: 1.1 }}\n                              whileTap={{ scale: 0.9 }}\n                              onClick={() => handleDownloadFile(job.id, job.fileName)}\n                              className=\"p-2 rounded-lg bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 transition-colors\"\n                              title=\"Download\"\n                            >\n                              <Download size={16} />\n                            </motion.button>\n                            \n                            <motion.button\n                              whileHover={{ scale: 1.1 }}\n                              whileTap={{ scale: 0.9 }}\n                              onClick={() => handleViewJob(job)}\n                              className=\"p-2 rounded-lg bg-purple-500/10 text-purple-600 hover:bg-purple-500/20 transition-colors\"\n                              title=\"View Details\"\n                            >\n                              <Eye size={16} />\n                            </motion.button>\n                            \n                            <motion.button\n                              whileHover={{ scale: 1.1 }}\n                              whileTap={{ scale: 0.9 }}\n                              onClick={() => handleOpenChat(job)}\n                              className=\"p-2 rounded-lg bg-green-500/10 text-green-600 hover:bg-green-500/20 transition-colors\"\n                              title=\"Chat\"\n                            >\n                              <MessageCircle size={16} />\n                            </motion.button>\n                            \n                            {job.status === 'Quoted' && (\n                              <motion.button\n                                whileHover={{ scale: 1.1 }}\n                                whileTap={{ scale: 0.9 }}\n                                onClick={() => handleConfirmJob(job.id)}\n                                className=\"p-2 rounded-lg bg-green-500 text-white hover:bg-green-600 transition-colors\"\n                                title=\"Confirm Quote\"\n                              >\n                                <CheckCircle size={16} />\n                              </motion.button>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              ) : (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-12\"\n                >\n                  <div className=\"w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mx-auto mb-4\">\n                    <Upload className=\"w-10 h-10 text-white\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                    No print jobs yet\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n                    Upload your first file to get started!\n                  </p>\n                  <AceternityButton onClick={() => setShowUploadModal(true)}>\n                    <Plus className=\"w-4 h-4 mr-2\" />\n                    Upload File\n                  </AceternityButton>\n                </motion.div>\n              )}\n            </AceternityCard>\n          </Col>\n\n          {/* Xerox Centers */}\n          <Col lg={4}>\n            <AceternityCard className=\"h-100\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                  <Printer className=\"w-5 h-5 inline mr-2\" />\n                  Available Centers\n                </h3>\n                <AceternityBadge variant=\"success\">\n                  {xeroxCenters.filter(c => c.isActive).length} active\n                </AceternityBadge>\n              </div>\n\n              <div className=\"space-y-4\">\n                {xeroxCenters.slice(0, 4).map((center, index) => (\n                  <motion.div\n                    key={center.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    className=\"p-4 rounded-xl bg-white/50 dark:bg-black/20 border border-white/20 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-all duration-200\"\n                  >\n                    <div className=\"flex items-start justify-between mb-3\">\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900 dark:text-white\">\n                          {center.name}\n                        </h4>\n                        <div className=\"flex items-center text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                          <MapPin size={14} className=\"mr-1\" />\n                          {center.location}\n                        </div>\n                      </div>\n                      <AceternityBadge \n                        variant={center.pendingJobs <= 5 ? 'success' : center.pendingJobs <= 10 ? 'warning' : 'error'}\n                        size=\"sm\"\n                      >\n                        {center.pendingJobs} jobs\n                      </AceternityBadge>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <Star className=\"w-4 h-4 text-yellow-500 mr-1\" />\n                        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                          {center.averageRating.toFixed(1)}\n                        </span>\n                      </div>\n                      <AceternityButton variant=\"outline\" size=\"sm\">\n                        Select\n                      </AceternityButton>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </AceternityCard>\n          </Col>\n        </Row>\n      </Container>\n    </div>\n  );\n};\n\nexport default AceternityStudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,QAAkD,iBAAiB;AAC/F,SAASC,MAAM,QAAyB,eAAe;AACvD,SACEC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,QAAQ,EACRC,aAAa,EAEbC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,IAAI,EAEJC,QAAQ,QACH,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,EAAEC,cAAc,EAAEC,aAAa,EAAEC,UAAU,QAAQ,oBAAoB;AAC3F,SAASC,cAAc,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuB1G,MAAMC,0BAAoC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjD,MAAM;IAAEC;EAAK,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC;IAC3CqD,OAAO,EAAE,EAAE;IACXC,sBAAsB,EAAE,EAAE;IAC1BC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFzD,SAAS,CAAC,MAAM;IACd,MAAM0D,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,iBAAiB,GAAG,MAAMxC,WAAW,CAACyC,cAAc,CAAC,CAAC;QAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;QAEpC,MAAMC,oBAAoB,GAAG,MAAM1C,cAAc,CAAC2C,MAAM,CAAC,CAAC;QAC1D5B,eAAe,CAAC2B,oBAAoB,CAACD,IAAI,CAAC;MAC5C,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C/B,YAAY,CAAC,EAAE,CAAC;QAChBE,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC;IAEDuB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAgB,CAAC;MACpE,aAAa,EAAE;QAAED,OAAO,EAAE,MAAe;QAAEC,KAAK,EAAE;MAAgB,CAAC;MACnE,QAAQ,EAAE;QAAED,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAkB,CAAC;MACnE,qBAAqB,EAAE;QAAED,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAkB,CAAC;MAChF,WAAW,EAAE;QAAED,OAAO,EAAE,MAAe;QAAEC,KAAK,EAAE;MAAgB,CAAC;MACjE,YAAY,EAAE;QAAED,OAAO,EAAE,MAAe;QAAEC,KAAK,EAAE;MAAkB,CAAC;MACpE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAiB,CAAC;MACrE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAiB,CAAC;MACrE,UAAU,EAAE;QAAED,OAAO,EAAE,OAAgB;QAAEC,KAAK,EAAE;MAAe,CAAC;MAChE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAgB;IACrE,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAAEE,OAAO,EAAE,SAAkB;MAAEC,KAAK,EAAE;IAAgB,CAAC;IAE3H,oBACE1C,OAAA,CAACF,eAAe;MAAC2C,OAAO,EAAEE,MAAM,CAACF,OAAQ;MAAAG,QAAA,EACtCL;IAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAEtB,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACjC,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMkC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEpC,YAAY,CAAC;MACrCkC,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE9B,UAAU,CAACE,OAAO,CAAC;MAC9C0B,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAE9B,UAAU,CAACG,sBAAsB,CAAC;MAC5EyB,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE9B,UAAU,CAACI,SAAS,CAAC;MAClDwB,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE9B,UAAU,CAACK,MAAM,CAAC0B,QAAQ,CAAC,CAAC,CAAC;MACvDH,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE9B,UAAU,CAACM,SAAS,CAAC;MAClDsB,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE9B,UAAU,CAACO,SAAS,CAAC;MAElD,MAAMpC,aAAa,CAAC6D,UAAU,CAACJ,QAAQ,CAAC;MAExC,MAAMnB,iBAAiB,GAAG,MAAMxC,WAAW,CAACyC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;MAEpCxB,kBAAkB,CAAC,KAAK,CAAC;MACzBQ,eAAe,CAAC,IAAI,CAAC;MACrBM,aAAa,CAAC;QACZC,OAAO,EAAE,EAAE;QACXC,sBAAsB,EAAE,EAAE;QAC1BC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMmB,aAAa,GAAIC,GAAQ,IAAK;IAClCzC,cAAc,CAACyC,GAAG,CAAC;IACnB7C,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM8C,cAAc,GAAG,MAAOD,GAAQ,IAAK;IACzC,IAAI;MACFzC,cAAc,CAACyC,GAAG,CAAC;MACnB3C,gBAAgB,CAAC,IAAI,CAAC;MAEtB,MAAM6C,QAAQ,GAAG,MAAMhE,UAAU,CAACiE,cAAc,CAACH,GAAG,CAACI,EAAE,CAAC;MACxDvC,WAAW,CAACqC,QAAQ,CAACzB,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/Cf,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;EAED,MAAMwC,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAEjB,QAAgB,KAAK;IACpE,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAMjE,aAAa,CAACsE,YAAY,CAACD,KAAK,CAAC;MAExD,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACP,QAAQ,CAACzB,IAAI,CAAC,CAAC;MACtC,MAAMiC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAG7B,QAAQ;MACxB0B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAM4C,gBAAgB,GAAG,MAAOlB,KAAa,IAAK;IAChD,IAAI;MACF,MAAMvE,WAAW,CAAC0F,UAAU,CAACnB,KAAK,CAAC;MAEnC,MAAM/B,iBAAiB,GAAG,MAAMxC,WAAW,CAACyC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAM8C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAChE,WAAW,CAACiE,IAAI,CAAC,CAAC,IAAI,CAACrE,WAAW,EAAE;IAEzC,IAAI;MACF,MAAM4C,QAAQ,GAAG,MAAMhE,UAAU,CAAC0F,WAAW,CAACtE,WAAW,CAAC8C,EAAE,EAAE1C,WAAW,CAACiE,IAAI,CAAC,CAAC,CAAC;MAEjF9D,WAAW,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE3B,QAAQ,CAACzB,IAAI,CAAC,CAAC;MAC7Cd,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMkD,UAAU,GAAGlF,SAAS,CAACmF,MAAM,CAAC,CAACC,GAAG,EAAEhC,GAAG,KAAKgC,GAAG,IAAIhC,GAAG,CAACiC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E,MAAMC,cAAc,GAAGtF,SAAS,CAACuF,MAAM,CAACnC,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAACoC,QAAQ,CAACpC,GAAG,CAACjB,MAAM,CAAC,CAAC,CAACsD,MAAM;EACtH,MAAMC,aAAa,GAAG1F,SAAS,CAACuF,MAAM,CAACnC,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACoC,QAAQ,CAACpC,GAAG,CAACjB,MAAM,CAAC,CAAC,CAACsD,MAAM;EAErG,oBACE7F,OAAA;IAAK+F,SAAS,EAAC,cAAc;IAAAnD,QAAA,eAC3B5C,OAAA,CAAC3B,SAAS;MAAC2H,KAAK;MAACD,SAAS,EAAC,MAAM;MAAAnD,QAAA,gBAE/B5C,OAAA,CAACxB,MAAM,CAACyH,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,kBAAkB;QAAAnD,QAAA,gBAE5B5C,OAAA;UAAI+F,SAAS,EAAC,oGAAoG;UAAAnD,QAAA,EAAC;QAEnH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhD,OAAA;UAAG+F,SAAS,EAAC,+CAA+C;UAAAnD,QAAA,GAAC,gBAC7C,eAAA5C,OAAA;YAAM+F,SAAS,EAAC,eAAe;YAAAnD,QAAA,EAAEzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqG;UAAQ;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,KACvE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJhD,OAAA,CAACxB,MAAM,CAACyH,GAAG;UACTQ,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAA9D,QAAA,eAE1B5C,OAAA,CAACH,gBAAgB;YACf+G,OAAO,EAAEA,CAAA,KAAMnG,kBAAkB,CAAC,IAAI,CAAE;YACxCsF,SAAS,EAAC,mBAAmB;YAAAnD,QAAA,gBAE7B5C,OAAA,CAACvB,MAAM;cAACsH,SAAS,EAAC;YAAc;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGbhD,OAAA,CAAC1B,GAAG;QAACyH,SAAS,EAAC,UAAU;QAAAnD,QAAA,gBACvB5C,OAAA,CAACzB,GAAG;UAACsI,EAAE,EAAE,CAAE;UAAAjE,QAAA,eACT5C,OAAA,CAACJ,mBAAmB;YAClBkH,KAAK,EAAC,YAAY;YAClBC,KAAK,EAAE3G,SAAS,CAACyF,MAAO;YACxBmB,IAAI,eAAEhH,OAAA,CAACtB,QAAQ;cAACuI,IAAI,EAAE;YAAG;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BN,KAAK,EAAC,MAAM;YACZwE,KAAK,EAAE;cAAEH,KAAK,EAAE,EAAE;cAAEI,UAAU,EAAE;YAAK;UAAE;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhD,OAAA,CAACzB,GAAG;UAACsI,EAAE,EAAE,CAAE;UAAAjE,QAAA,eACT5C,OAAA,CAACJ,mBAAmB;YAClBkH,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAErB,cAAe;YACtBsB,IAAI,eAAEhH,OAAA,CAACrB,KAAK;cAACsI,IAAI,EAAE;YAAG;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BN,KAAK,EAAC,QAAQ;YACdwE,KAAK,EAAE;cAAEH,KAAK,EAAE,CAAC;cAAEI,UAAU,EAAE;YAAK;UAAE;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhD,OAAA,CAACzB,GAAG;UAACsI,EAAE,EAAE,CAAE;UAAAjE,QAAA,eACT5C,OAAA,CAACJ,mBAAmB;YAClBkH,KAAK,EAAC,WAAW;YACjBC,KAAK,EAAEjB,aAAc;YACrBkB,IAAI,eAAEhH,OAAA,CAACpB,WAAW;cAACqI,IAAI,EAAE;YAAG;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCN,KAAK,EAAC,OAAO;YACbwE,KAAK,EAAE;cAAEH,KAAK,EAAE,CAAC;cAAEI,UAAU,EAAE;YAAK;UAAE;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhD,OAAA,CAACzB,GAAG;UAACsI,EAAE,EAAE,CAAE;UAAAjE,QAAA,eACT5C,OAAA,CAACJ,mBAAmB;YAClBkH,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE,IAAIzB,UAAU,CAAC8B,OAAO,CAAC,CAAC,CAAC,EAAG;YACnCJ,IAAI,eAAEhH,OAAA,CAACnB,UAAU;cAACoI,IAAI,EAAE;YAAG;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BN,KAAK,EAAC,QAAQ;YACdwE,KAAK,EAAE;cAAEH,KAAK,EAAE,EAAE;cAAEI,UAAU,EAAE;YAAM;UAAE;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhD,OAAA,CAAC1B,GAAG;QAACyH,SAAS,EAAC,KAAK;QAAAnD,QAAA,gBAElB5C,OAAA,CAACzB,GAAG;UAAC8I,EAAE,EAAE,CAAE;UAAAzE,QAAA,eACT5C,OAAA,CAACL,cAAc;YAACoG,SAAS,EAAC,OAAO;YAAAnD,QAAA,gBAC/B5C,OAAA;cAAK+F,SAAS,EAAC,wCAAwC;cAAAnD,QAAA,gBACrD5C,OAAA;gBAAI+F,SAAS,EAAC,kGAAkG;gBAAAnD,QAAA,gBAC9G5C,OAAA,CAACX,QAAQ;kBAAC0G,SAAS,EAAC;gBAAqB;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBAE9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhD,OAAA,CAACF,eAAe;gBAAC2C,OAAO,EAAC,MAAM;gBAAAG,QAAA,GAC5BxC,SAAS,CAACyF,MAAM,EAAC,OACpB;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EAEL5C,SAAS,CAACyF,MAAM,GAAG,CAAC,gBACnB7F,OAAA;cAAK+F,SAAS,EAAC,WAAW;cAAAnD,QAAA,EACvBxC,SAAS,CAACkH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC/D,GAAG,EAAEgE,KAAK,kBACpCxH,OAAA,CAACxB,MAAM,CAACyH,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEsB,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCpB,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEsB,CAAC,EAAE;gBAAE,CAAE;gBAC9BnB,UAAU,EAAE;kBAAEoB,KAAK,EAAEF,KAAK,GAAG;gBAAI,CAAE;gBACnCzB,SAAS,EAAC,8JAA8J;gBAAAnD,QAAA,eAExK5C,OAAA;kBAAK+F,SAAS,EAAC,mCAAmC;kBAAAnD,QAAA,gBAChD5C,OAAA;oBAAK+F,SAAS,EAAC,6BAA6B;oBAAAnD,QAAA,gBAC1C5C,OAAA;sBAAK+F,SAAS,EAAC,gHAAgH;sBAAAnD,QAAA,eAC7H5C,OAAA,CAACtB,QAAQ;wBAACuI,IAAI,EAAE;sBAAG;wBAAApE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACNhD,OAAA;sBAAA4C,QAAA,gBACE5C,OAAA;wBAAI+F,SAAS,EAAC,6CAA6C;wBAAAnD,QAAA,EACxDY,GAAG,CAACmE;sBAAS;wBAAA9E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACLhD,OAAA;wBAAG+F,SAAS,EAAC,0CAA0C;wBAAAnD,QAAA,EACpDY,GAAG,CAACX,QAAQ,CAACgD,MAAM,GAAG,EAAE,GAAGrC,GAAG,CAACX,QAAQ,CAACyE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG9D,GAAG,CAACX;sBAAQ;wBAAAA,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3E,CAAC,eACJhD,OAAA;wBAAG+F,SAAS,EAAC,0CAA0C;wBAAAnD,QAAA,EACpDY,GAAG,CAACoE;sBAAe;wBAAA/E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENhD,OAAA;oBAAK+F,SAAS,EAAC,6BAA6B;oBAAAnD,QAAA,gBAC1C5C,OAAA;sBAAK+F,SAAS,EAAC,YAAY;sBAAAnD,QAAA,GACxBN,cAAc,CAACkB,GAAG,CAACjB,MAAM,CAAC,EAC1BiB,GAAG,CAACiC,IAAI,iBACPzF,OAAA;wBAAG+F,SAAS,EAAC,+DAA+D;wBAAAnD,QAAA,GAAC,GAC1E,EAACY,GAAG,CAACiC,IAAI,CAAC2B,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CACJ;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAENhD,OAAA;sBAAK+F,SAAS,EAAC,gBAAgB;sBAAAnD,QAAA,gBAC7B5C,OAAA,CAACxB,MAAM,CAACqJ,MAAM;wBACZpB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAI,CAAE;wBAC3BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAI,CAAE;wBACzBE,OAAO,EAAEA,CAAA,KAAM/C,kBAAkB,CAACL,GAAG,CAACI,EAAE,EAAEJ,GAAG,CAACX,QAAQ,CAAE;wBACxDkD,SAAS,EAAC,oFAAoF;wBAC9Fe,KAAK,EAAC,UAAU;wBAAAlE,QAAA,eAEhB5C,OAAA,CAAClB,QAAQ;0BAACmI,IAAI,EAAE;wBAAG;0BAAApE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC,eAEhBhD,OAAA,CAACxB,MAAM,CAACqJ,MAAM;wBACZpB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAI,CAAE;wBAC3BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAI,CAAE;wBACzBE,OAAO,EAAEA,CAAA,KAAMrD,aAAa,CAACC,GAAG,CAAE;wBAClCuC,SAAS,EAAC,0FAA0F;wBACpGe,KAAK,EAAC,cAAc;wBAAAlE,QAAA,eAEpB5C,OAAA,CAACb,GAAG;0BAAC8H,IAAI,EAAE;wBAAG;0BAAApE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eAEhBhD,OAAA,CAACxB,MAAM,CAACqJ,MAAM;wBACZpB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAI,CAAE;wBAC3BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAI,CAAE;wBACzBE,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAACD,GAAG,CAAE;wBACnCuC,SAAS,EAAC,uFAAuF;wBACjGe,KAAK,EAAC,MAAM;wBAAAlE,QAAA,eAEZ5C,OAAA,CAACjB,aAAa;0BAACkI,IAAI,EAAE;wBAAG;0BAAApE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,EAEfQ,GAAG,CAACjB,MAAM,KAAK,QAAQ,iBACtBvC,OAAA,CAACxB,MAAM,CAACqJ,MAAM;wBACZpB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAI,CAAE;wBAC3BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAI,CAAE;wBACzBE,OAAO,EAAEA,CAAA,KAAM5B,gBAAgB,CAACxB,GAAG,CAACI,EAAE,CAAE;wBACxCmC,SAAS,EAAC,6EAA6E;wBACvFe,KAAK,EAAC,eAAe;wBAAAlE,QAAA,eAErB5C,OAAA,CAACpB,WAAW;0BAACqI,IAAI,EAAE;wBAAG;0BAAApE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAChB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA9EDQ,GAAG,CAACI,EAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA+ED,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAENhD,OAAA,CAACxB,MAAM,CAACyH,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAI,CAAE;cACpCL,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,mBAAmB;cAAAnD,QAAA,gBAE7B5C,OAAA;gBAAK+F,SAAS,EAAC,oHAAoH;gBAAAnD,QAAA,eACjI5C,OAAA,CAACvB,MAAM;kBAACsH,SAAS,EAAC;gBAAsB;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACNhD,OAAA;gBAAI+F,SAAS,EAAC,0DAA0D;gBAAAnD,QAAA,EAAC;cAEzE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhD,OAAA;gBAAG+F,SAAS,EAAC,uCAAuC;gBAAAnD,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJhD,OAAA,CAACH,gBAAgB;gBAAC+G,OAAO,EAAEA,CAAA,KAAMnG,kBAAkB,CAAC,IAAI,CAAE;gBAAAmC,QAAA,gBACxD5C,OAAA,CAACZ,IAAI;kBAAC2G,SAAS,EAAC;gBAAc;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEnC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACa;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eAGNhD,OAAA,CAACzB,GAAG;UAAC8I,EAAE,EAAE,CAAE;UAAAzE,QAAA,eACT5C,OAAA,CAACL,cAAc;YAACoG,SAAS,EAAC,OAAO;YAAAnD,QAAA,gBAC/B5C,OAAA;cAAK+F,SAAS,EAAC,wCAAwC;cAAAnD,QAAA,gBACrD5C,OAAA;gBAAI+F,SAAS,EAAC,kGAAkG;gBAAAnD,QAAA,gBAC9G5C,OAAA,CAACd,OAAO;kBAAC6G,SAAS,EAAC;gBAAqB;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBAE7C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhD,OAAA,CAACF,eAAe;gBAAC2C,OAAO,EAAC,SAAS;gBAAAG,QAAA,GAC/BtC,YAAY,CAACqF,MAAM,CAACmC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAAClC,MAAM,EAAC,SAC/C;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAENhD,OAAA;cAAK+F,SAAS,EAAC,WAAW;cAAAnD,QAAA,EACvBtC,YAAY,CAACgH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACS,MAAM,EAAER,KAAK,kBAC1CxH,OAAA,CAACxB,MAAM,CAACyH,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,UAAU,EAAE;kBAAEoB,KAAK,EAAEF,KAAK,GAAG;gBAAI,CAAE;gBACnCzB,SAAS,EAAC,8JAA8J;gBAAAnD,QAAA,gBAExK5C,OAAA;kBAAK+F,SAAS,EAAC,uCAAuC;kBAAAnD,QAAA,gBACpD5C,OAAA;oBAAA4C,QAAA,gBACE5C,OAAA;sBAAI+F,SAAS,EAAC,6CAA6C;sBAAAnD,QAAA,EACxDoF,MAAM,CAACC;oBAAI;sBAAApF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACLhD,OAAA;sBAAK+F,SAAS,EAAC,iEAAiE;sBAAAnD,QAAA,gBAC9E5C,OAAA,CAACf,MAAM;wBAACgI,IAAI,EAAE,EAAG;wBAAClB,SAAS,EAAC;sBAAM;wBAAAlD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACpCgF,MAAM,CAACE,QAAQ;oBAAA;sBAAArF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNhD,OAAA,CAACF,eAAe;oBACd2C,OAAO,EAAEuF,MAAM,CAACG,WAAW,IAAI,CAAC,GAAG,SAAS,GAAGH,MAAM,CAACG,WAAW,IAAI,EAAE,GAAG,SAAS,GAAG,OAAQ;oBAC9FlB,IAAI,EAAC,IAAI;oBAAArE,QAAA,GAERoF,MAAM,CAACG,WAAW,EAAC,OACtB;kBAAA;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAENhD,OAAA;kBAAK+F,SAAS,EAAC,mCAAmC;kBAAAnD,QAAA,gBAChD5C,OAAA;oBAAK+F,SAAS,EAAC,mBAAmB;oBAAAnD,QAAA,gBAChC5C,OAAA,CAAChB,IAAI;sBAAC+G,SAAS,EAAC;oBAA8B;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjDhD,OAAA;sBAAM+F,SAAS,EAAC,sDAAsD;sBAAAnD,QAAA,EACnEoF,MAAM,CAACI,aAAa,CAAChB,OAAO,CAAC,CAAC;oBAAC;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNhD,OAAA,CAACH,gBAAgB;oBAAC4C,OAAO,EAAC,SAAS;oBAACwE,IAAI,EAAC,IAAI;oBAAArE,QAAA,EAAC;kBAE9C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAkB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA,GAlCDgF,MAAM,CAACpE,EAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmCJ,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA7ZID,0BAAoC;EAAA,QACvBX,OAAO;AAAA;AAAA+I,EAAA,GADpBpI,0BAAoC;AA+Z1C,eAAeA,0BAA0B;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}