{"ast": null, "code": "import { analyseComplexValue } from '../../../value/types/complex/index.mjs';\nimport { getAnimatableNone } from '../../../value/types/utils/animatable-none.mjs';\n\n/**\n * If we encounter keyframes like \"none\" or \"0\" and we also have keyframes like\n * \"#fff\" or \"200px 200px\" we want to find a keyframe to serve as a template for\n * the \"none\" keyframes. In this case \"#fff\" or \"200px 200px\" - then these get turned into\n * zero equivalents, i.e. \"#fff0\" or \"0px 0px\".\n */\nconst invalidTemplates = new Set([\"auto\", \"none\", \"0\"]);\nfunction makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name) {\n  let i = 0;\n  let animatableTemplate = undefined;\n  while (i < unresolvedKeyframes.length && !animatableTemplate) {\n    const keyframe = unresolvedKeyframes[i];\n    if (typeof keyframe === \"string\" && !invalidTemplates.has(keyframe) && analyseComplexValue(keyframe).values.length) {\n      animatableTemplate = unresolvedKeyframes[i];\n    }\n    i++;\n  }\n  if (animatableTemplate && name) {\n    for (const noneIndex of noneKeyframeIndexes) {\n      unresolvedKeyframes[noneIndex] = getAnimatableNone(name, animatableTemplate);\n    }\n  }\n}\nexport { makeNoneKeyframesAnimatable };", "map": {"version": 3, "names": ["analyseComplexValue", "getAnimatableNone", "invalidTemplates", "Set", "makeNoneKeyframesAnimatable", "unresolvedKeyframes", "noneKeyframeIndexes", "name", "i", "animatableTemplate", "undefined", "length", "keyframe", "has", "values", "noneIndex"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/animation/keyframes/utils/make-none-animatable.mjs"], "sourcesContent": ["import { analyseComplexValue } from '../../../value/types/complex/index.mjs';\nimport { getAnimatableNone } from '../../../value/types/utils/animatable-none.mjs';\n\n/**\n * If we encounter keyframes like \"none\" or \"0\" and we also have keyframes like\n * \"#fff\" or \"200px 200px\" we want to find a keyframe to serve as a template for\n * the \"none\" keyframes. In this case \"#fff\" or \"200px 200px\" - then these get turned into\n * zero equivalents, i.e. \"#fff0\" or \"0px 0px\".\n */\nconst invalidTemplates = new Set([\"auto\", \"none\", \"0\"]);\nfunction makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name) {\n    let i = 0;\n    let animatableTemplate = undefined;\n    while (i < unresolvedKeyframes.length && !animatableTemplate) {\n        const keyframe = unresolvedKeyframes[i];\n        if (typeof keyframe === \"string\" &&\n            !invalidTemplates.has(keyframe) &&\n            analyseComplexValue(keyframe).values.length) {\n            animatableTemplate = unresolvedKeyframes[i];\n        }\n        i++;\n    }\n    if (animatableTemplate && name) {\n        for (const noneIndex of noneKeyframeIndexes) {\n            unresolvedKeyframes[noneIndex] = getAnimatableNone(name, animatableTemplate);\n        }\n    }\n}\n\nexport { makeNoneKeyframesAnimatable };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,wCAAwC;AAC5E,SAASC,iBAAiB,QAAQ,gDAAgD;;AAElF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;AACvD,SAASC,2BAA2BA,CAACC,mBAAmB,EAAEC,mBAAmB,EAAEC,IAAI,EAAE;EACjF,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,kBAAkB,GAAGC,SAAS;EAClC,OAAOF,CAAC,GAAGH,mBAAmB,CAACM,MAAM,IAAI,CAACF,kBAAkB,EAAE;IAC1D,MAAMG,QAAQ,GAAGP,mBAAmB,CAACG,CAAC,CAAC;IACvC,IAAI,OAAOI,QAAQ,KAAK,QAAQ,IAC5B,CAACV,gBAAgB,CAACW,GAAG,CAACD,QAAQ,CAAC,IAC/BZ,mBAAmB,CAACY,QAAQ,CAAC,CAACE,MAAM,CAACH,MAAM,EAAE;MAC7CF,kBAAkB,GAAGJ,mBAAmB,CAACG,CAAC,CAAC;IAC/C;IACAA,CAAC,EAAE;EACP;EACA,IAAIC,kBAAkB,IAAIF,IAAI,EAAE;IAC5B,KAAK,MAAMQ,SAAS,IAAIT,mBAAmB,EAAE;MACzCD,mBAAmB,CAACU,SAAS,CAAC,GAAGd,iBAAiB,CAACM,IAAI,EAAEE,kBAAkB,CAAC;IAChF;EACJ;AACJ;AAEA,SAASL,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}