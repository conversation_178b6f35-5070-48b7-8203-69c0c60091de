import React, { useState, forwardRef } from 'react';
import { motion } from 'framer-motion';
import { Eye, EyeOff, Search, X } from 'lucide-react';
import { cn } from '../../lib/utils';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ComponentType<any>;
  rightIcon?: React.ComponentType<any>;
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'sm' | 'md' | 'lg';
  clearable?: boolean;
  onClear?: () => void;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  leftIcon: LeftIcon,
  rightIcon: RightIcon,
  variant = 'default',
  size = 'md',
  clearable = false,
  onClear,
  className,
  type = 'text',
  value,
  onChange,
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const baseClasses = "w-full transition-all duration-200 focus:outline-none";
  
  const variants = {
    default: cn(
      "border border-slate-300 dark:border-slate-600 rounded-lg",
      "bg-white dark:bg-slate-700 text-slate-900 dark:text-white",
      "focus:ring-2 focus:ring-blue-500 focus:border-transparent",
      error && "border-red-500 focus:ring-red-500"
    ),
    filled: cn(
      "border-0 rounded-lg",
      "bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white",
      "focus:ring-2 focus:ring-blue-500",
      error && "bg-red-50 dark:bg-red-900/20 focus:ring-red-500"
    ),
    outlined: cn(
      "border-2 border-slate-300 dark:border-slate-600 rounded-lg",
      "bg-transparent text-slate-900 dark:text-white",
      "focus:border-blue-500",
      error && "border-red-500 focus:border-red-500"
    )
  };

  const sizes = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-3 py-2 text-sm",
    lg: "px-4 py-3 text-base"
  };

  const iconSizes = {
    sm: "w-4 h-4",
    md: "w-5 h-5",
    lg: "w-6 h-6"
  };

  const handleClear = () => {
    if (onClear) {
      onClear();
    } else if (onChange) {
      onChange({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>);
    }
  };

  const inputType = type === 'password' && showPassword ? 'text' : type;

  return (
    <div className="space-y-1">
      {label && (
        <motion.label
          className={cn(
            "block text-sm font-medium transition-colors",
            error ? "text-red-600 dark:text-red-400" : "text-slate-700 dark:text-slate-300"
          )}
          animate={{ color: isFocused ? '#3b82f6' : undefined }}
        >
          {label}
        </motion.label>
      )}
      
      <div className="relative">
        {LeftIcon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
            <LeftIcon className={iconSizes[size]} />
          </div>
        )}
        
        <input
          ref={ref}
          type={inputType}
          value={value}
          onChange={onChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={cn(
            baseClasses,
            variants[variant],
            sizes[size],
            LeftIcon && "pl-10",
            (RightIcon || clearable || type === 'password') && "pr-10",
            className
          )}
          {...props}
        />
        
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
          {clearable && value && (
            <motion.button
              type="button"
              onClick={handleClear}
              className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <X className="w-4 h-4" />
            </motion.button>
          )}
          
          {type === 'password' && (
            <motion.button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              {showPassword ? (
                <EyeOff className={iconSizes[size]} />
              ) : (
                <Eye className={iconSizes[size]} />
              )}
            </motion.button>
          )}
          
          {RightIcon && (
            <div className="text-slate-400">
              <RightIcon className={iconSizes[size]} />
            </div>
          )}
        </div>
      </div>
      
      {(error || helperText) && (
        <motion.p
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          className={cn(
            "text-xs",
            error ? "text-red-600 dark:text-red-400" : "text-slate-500 dark:text-slate-400"
          )}
        >
          {error || helperText}
        </motion.p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

// Search Input Component
interface SearchInputProps extends Omit<InputProps, 'leftIcon' | 'type'> {
  onSearch?: (value: string) => void;
  searchDelay?: number;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  onSearch,
  searchDelay = 300,
  onChange,
  ...props
}) => {
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout>();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    if (onChange) {
      onChange(e);
    }

    if (onSearch) {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
      
      const timeout = setTimeout(() => {
        onSearch(value);
      }, searchDelay);
      
      setSearchTimeout(timeout);
    }
  };

  return (
    <Input
      leftIcon={Search}
      placeholder="Search..."
      onChange={handleChange}
      clearable
      {...props}
    />
  );
};

// Textarea Component
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'filled' | 'outlined';
  resize?: boolean;
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(({
  label,
  error,
  helperText,
  variant = 'default',
  resize = true,
  className,
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);

  const baseClasses = "w-full transition-all duration-200 focus:outline-none";
  
  const variants = {
    default: cn(
      "border border-slate-300 dark:border-slate-600 rounded-lg",
      "bg-white dark:bg-slate-700 text-slate-900 dark:text-white",
      "focus:ring-2 focus:ring-blue-500 focus:border-transparent",
      error && "border-red-500 focus:ring-red-500"
    ),
    filled: cn(
      "border-0 rounded-lg",
      "bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white",
      "focus:ring-2 focus:ring-blue-500",
      error && "bg-red-50 dark:bg-red-900/20 focus:ring-red-500"
    ),
    outlined: cn(
      "border-2 border-slate-300 dark:border-slate-600 rounded-lg",
      "bg-transparent text-slate-900 dark:text-white",
      "focus:border-blue-500",
      error && "border-red-500 focus:border-red-500"
    )
  };

  return (
    <div className="space-y-1">
      {label && (
        <motion.label
          className={cn(
            "block text-sm font-medium transition-colors",
            error ? "text-red-600 dark:text-red-400" : "text-slate-700 dark:text-slate-300"
          )}
          animate={{ color: isFocused ? '#3b82f6' : undefined }}
        >
          {label}
        </motion.label>
      )}
      
      <textarea
        ref={ref}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className={cn(
          baseClasses,
          variants[variant],
          "px-3 py-2 text-sm",
          !resize && "resize-none",
          className
        )}
        {...props}
      />
      
      {(error || helperText) && (
        <motion.p
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          className={cn(
            "text-xs",
            error ? "text-red-600 dark:text-red-400" : "text-slate-500 dark:text-slate-400"
          )}
        >
          {error || helperText}
        </motion.p>
      )}
    </div>
  );
});

Textarea.displayName = 'Textarea';
