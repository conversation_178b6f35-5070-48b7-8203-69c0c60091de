{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\Layout.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useLocation } from 'react-router-dom';\nimport ProfessionalNavbar from './ProfessionalNavbar';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport '../styles/Layout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children,\n  className = ''\n}) => {\n  _s();\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  const {\n    user\n  } = useAuth();\n  const location = useLocation();\n  const pageVariants = {\n    initial: {\n      opacity: 0,\n      y: 20,\n      scale: 0.98\n    },\n    in: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.4,\n        ease: [0.6, -0.05, 0.01, 0.99]\n      }\n    },\n    out: {\n      opacity: 0,\n      y: -20,\n      scale: 0.98,\n      transition: {\n        duration: 0.3,\n        ease: [0.6, -0.05, 0.01, 0.99]\n      }\n    }\n  };\n  const backgroundVariants = {\n    light: {\n      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n      transition: {\n        duration: 0.5\n      }\n    },\n    dark: {\n      background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `layout-wrapper ${isDarkMode ? 'dark-mode' : 'light-mode'}`,\n    children: [user && /*#__PURE__*/_jsxDEV(ProfessionalNavbar, {\n      onThemeToggle: toggleTheme,\n      isDarkMode: isDarkMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(motion.main, {\n      className: `main-content ${className}`,\n      variants: backgroundVariants,\n      animate: isDarkMode ? 'dark' : 'light',\n      style: {\n        paddingTop: user ? '100px' : '0',\n        // Account for fixed navbar\n        minHeight: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          variants: pageVariants,\n          initial: \"initial\",\n          animate: \"in\",\n          exit: \"out\",\n          className: \"page-content\",\n          children: children\n        }, location.pathname, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"background-decorations\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"decoration decoration-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"decoration decoration-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"decoration decoration-3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"floating-action-btn\",\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      initial: {\n        opacity: 0,\n        scale: 0\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      transition: {\n        delay: 1,\n        duration: 0.3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"fab-btn\",\n        onClick: () => {\n          // Quick action based on user type\n          if (user.userType === 'Student') {\n            window.location.href = '/upload';\n          } else {\n            window.location.href = '/job-queue';\n          }\n        },\n        title: user.userType === 'Student' ? 'Quick Upload' : 'View Jobs',\n        children: user.userType === 'Student' ? '📄' : '📋'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isDarkMode !== undefined && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"theme-indicator\",\n        initial: {\n          opacity: 0,\n          x: 100\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        exit: {\n          opacity: 0,\n          x: 100\n        },\n        transition: {\n          duration: 0.3\n        },\n        children: isDarkMode ? '🌙' : '☀️'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"7ulHA6OAQHBxWQMTzJNi7B9J1QA=\", false, function () {\n  return [useTheme, useAuth, useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "useLocation", "ProfessionalNavbar", "useTheme", "useAuth", "jsxDEV", "_jsxDEV", "Layout", "children", "className", "_s", "isDarkMode", "toggleTheme", "user", "location", "pageVariants", "initial", "opacity", "y", "scale", "in", "transition", "duration", "ease", "out", "backgroundVariants", "light", "background", "dark", "onThemeToggle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "main", "variants", "animate", "style", "paddingTop", "minHeight", "mode", "div", "exit", "pathname", "whileHover", "whileTap", "delay", "onClick", "userType", "window", "href", "title", "undefined", "x", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/Layout.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useLocation } from 'react-router-dom';\nimport ProfessionalNavbar from './ProfessionalNavbar';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport '../styles/Layout.css';\n\ninterface LayoutProps {\n  children: ReactNode;\n  className?: string;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children, className = '' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n  const { user } = useAuth();\n  const location = useLocation();\n\n  const pageVariants = {\n    initial: {\n      opacity: 0,\n      y: 20,\n      scale: 0.98\n    },\n    in: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.4,\n        ease: [0.6, -0.05, 0.01, 0.99]\n      }\n    },\n    out: {\n      opacity: 0,\n      y: -20,\n      scale: 0.98,\n      transition: {\n        duration: 0.3,\n        ease: [0.6, -0.05, 0.01, 0.99]\n      }\n    }\n  };\n\n  const backgroundVariants = {\n    light: {\n      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n      transition: { duration: 0.5 }\n    },\n    dark: {\n      background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',\n      transition: { duration: 0.5 }\n    }\n  };\n\n  return (\n    <div className={`layout-wrapper ${isDarkMode ? 'dark-mode' : 'light-mode'}`}>\n      {/* Professional Navbar */}\n      {user && (\n        <ProfessionalNavbar \n          onThemeToggle={toggleTheme} \n          isDarkMode={isDarkMode} \n        />\n      )}\n\n      {/* Main Content Area */}\n      <motion.main\n        className={`main-content ${className}`}\n        variants={backgroundVariants}\n        animate={isDarkMode ? 'dark' : 'light'}\n        style={{\n          paddingTop: user ? '100px' : '0', // Account for fixed navbar\n          minHeight: '100vh'\n        }}\n      >\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={location.pathname}\n            variants={pageVariants}\n            initial=\"initial\"\n            animate=\"in\"\n            exit=\"out\"\n            className=\"page-content\"\n          >\n            {children}\n          </motion.div>\n        </AnimatePresence>\n      </motion.main>\n\n      {/* Background Decorations */}\n      <div className=\"background-decorations\">\n        <div className=\"decoration decoration-1\"></div>\n        <div className=\"decoration decoration-2\"></div>\n        <div className=\"decoration decoration-3\"></div>\n      </div>\n\n      {/* Floating Action Button (Optional) */}\n      {user && (\n        <motion.div\n          className=\"floating-action-btn\"\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.9 }}\n          initial={{ opacity: 0, scale: 0 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ delay: 1, duration: 0.3 }}\n        >\n          <button\n            className=\"fab-btn\"\n            onClick={() => {\n              // Quick action based on user type\n              if (user.userType === 'Student') {\n                window.location.href = '/upload';\n              } else {\n                window.location.href = '/job-queue';\n              }\n            }}\n            title={user.userType === 'Student' ? 'Quick Upload' : 'View Jobs'}\n          >\n            {user.userType === 'Student' ? '📄' : '📋'}\n          </button>\n        </motion.div>\n      )}\n\n      {/* Theme Toggle Indicator */}\n      <AnimatePresence>\n        {isDarkMode !== undefined && (\n          <motion.div\n            className=\"theme-indicator\"\n            initial={{ opacity: 0, x: 100 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: 100 }}\n            transition={{ duration: 0.3 }}\n          >\n            {isDarkMode ? '🌙' : '☀️'}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO9B,MAAMC,MAA6B,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAGT,QAAQ,CAAC,CAAC;EAC9C,MAAM;IAAEU;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAMU,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAMc,YAAY,GAAG;IACnBC,OAAO,EAAE;MACPC,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,EAAE;MACLC,KAAK,EAAE;IACT,CAAC;IACDC,EAAE,EAAE;MACFH,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAE,CAAC;MACRE,UAAU,EAAE;QACVC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;MAC/B;IACF,CAAC;IACDC,GAAG,EAAE;MACHP,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,CAAC,EAAE;MACNC,KAAK,EAAE,IAAI;MACXE,UAAU,EAAE;QACVC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;MAC/B;IACF;EACF,CAAC;EAED,MAAME,kBAAkB,GAAG;IACzBC,KAAK,EAAE;MACLC,UAAU,EAAE,mDAAmD;MAC/DN,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAC9B,CAAC;IACDM,IAAI,EAAE;MACJD,UAAU,EAAE,mDAAmD;MAC/DN,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKG,SAAS,EAAE,kBAAkBE,UAAU,GAAG,WAAW,GAAG,YAAY,EAAG;IAAAH,QAAA,GAEzEK,IAAI,iBACHP,OAAA,CAACJ,kBAAkB;MACjB2B,aAAa,EAAEjB,WAAY;MAC3BD,UAAU,EAAEA;IAAW;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF,eAGD3B,OAAA,CAACP,MAAM,CAACmC,IAAI;MACVzB,SAAS,EAAE,gBAAgBA,SAAS,EAAG;MACvC0B,QAAQ,EAAEV,kBAAmB;MAC7BW,OAAO,EAAEzB,UAAU,GAAG,MAAM,GAAG,OAAQ;MACvC0B,KAAK,EAAE;QACLC,UAAU,EAAEzB,IAAI,GAAG,OAAO,GAAG,GAAG;QAAE;QAClC0B,SAAS,EAAE;MACb,CAAE;MAAA/B,QAAA,eAEFF,OAAA,CAACN,eAAe;QAACwC,IAAI,EAAC,MAAM;QAAAhC,QAAA,eAC1BF,OAAA,CAACP,MAAM,CAAC0C,GAAG;UAETN,QAAQ,EAAEpB,YAAa;UACvBC,OAAO,EAAC,SAAS;UACjBoB,OAAO,EAAC,IAAI;UACZM,IAAI,EAAC,KAAK;UACVjC,SAAS,EAAC,cAAc;UAAAD,QAAA,EAEvBA;QAAQ,GAPJM,QAAQ,CAAC6B,QAAQ;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGd3B,OAAA;MAAKG,SAAS,EAAC,wBAAwB;MAAAD,QAAA,gBACrCF,OAAA;QAAKG,SAAS,EAAC;MAAyB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/C3B,OAAA;QAAKG,SAAS,EAAC;MAAyB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/C3B,OAAA;QAAKG,SAAS,EAAC;MAAyB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,EAGLpB,IAAI,iBACHP,OAAA,CAACP,MAAM,CAAC0C,GAAG;MACThC,SAAS,EAAC,qBAAqB;MAC/BmC,UAAU,EAAE;QAAEzB,KAAK,EAAE;MAAI,CAAE;MAC3B0B,QAAQ,EAAE;QAAE1B,KAAK,EAAE;MAAI,CAAE;MACzBH,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEE,KAAK,EAAE;MAAE,CAAE;MAClCiB,OAAO,EAAE;QAAEnB,OAAO,EAAE,CAAC;QAAEE,KAAK,EAAE;MAAE,CAAE;MAClCE,UAAU,EAAE;QAAEyB,KAAK,EAAE,CAAC;QAAExB,QAAQ,EAAE;MAAI,CAAE;MAAAd,QAAA,eAExCF,OAAA;QACEG,SAAS,EAAC,SAAS;QACnBsC,OAAO,EAAEA,CAAA,KAAM;UACb;UACA,IAAIlC,IAAI,CAACmC,QAAQ,KAAK,SAAS,EAAE;YAC/BC,MAAM,CAACnC,QAAQ,CAACoC,IAAI,GAAG,SAAS;UAClC,CAAC,MAAM;YACLD,MAAM,CAACnC,QAAQ,CAACoC,IAAI,GAAG,YAAY;UACrC;QACF,CAAE;QACFC,KAAK,EAAEtC,IAAI,CAACmC,QAAQ,KAAK,SAAS,GAAG,cAAc,GAAG,WAAY;QAAAxC,QAAA,EAEjEK,IAAI,CAACmC,QAAQ,KAAK,SAAS,GAAG,IAAI,GAAG;MAAI;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACb,eAGD3B,OAAA,CAACN,eAAe;MAAAQ,QAAA,EACbG,UAAU,KAAKyC,SAAS,iBACvB9C,OAAA,CAACP,MAAM,CAAC0C,GAAG;QACThC,SAAS,EAAC,iBAAiB;QAC3BO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEoC,CAAC,EAAE;QAAI,CAAE;QAChCjB,OAAO,EAAE;UAAEnB,OAAO,EAAE,CAAC;UAAEoC,CAAC,EAAE;QAAE,CAAE;QAC9BX,IAAI,EAAE;UAAEzB,OAAO,EAAE,CAAC;UAAEoC,CAAC,EAAE;QAAI,CAAE;QAC7BhC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAd,QAAA,EAE7BG,UAAU,GAAG,IAAI,GAAG;MAAI;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV,CAAC;AAACvB,EAAA,CA9HIH,MAA6B;EAAA,QACGJ,QAAQ,EAC3BC,OAAO,EACPH,WAAW;AAAA;AAAAqD,EAAA,GAHxB/C,MAA6B;AAgInC,eAAeA,MAAM;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}