{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\AceternityStudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Modal, InputGroup } from 'react-bootstrap';\nimport { motion } from 'framer-motion';\nimport { Upload, FileText, Clock, CheckCircle, DollarSign, Download, MessageCircle, Star, MapPin, Printer, Eye, Plus, Activity } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../../services/api';\n// import { AceternityCard, AceternityStatsCard, AceternityButton, AceternityBadge } from './AceternityCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AceternityStudentDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [xeroxCenters, setXeroxCenters] = useState([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n    fetchData();\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        variant: 'default',\n        color: 'text-gray-600'\n      },\n      'UnderReview': {\n        variant: 'info',\n        color: 'text-blue-600'\n      },\n      'Quoted': {\n        variant: 'warning',\n        color: 'text-yellow-600'\n      },\n      'WaitingConfirmation': {\n        variant: 'warning',\n        color: 'text-orange-600'\n      },\n      'Confirmed': {\n        variant: 'info',\n        color: 'text-blue-600'\n      },\n      'InProgress': {\n        variant: 'info',\n        color: 'text-purple-600'\n      },\n      'Completed': {\n        variant: 'success',\n        color: 'text-green-600'\n      },\n      'Delivered': {\n        variant: 'success',\n        color: 'text-green-600'\n      },\n      'Rejected': {\n        variant: 'error',\n        color: 'text-red-600'\n      },\n      'Cancelled': {\n        variant: 'default',\n        color: 'text-gray-600'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'default',\n      color: 'text-gray-600'\n    };\n    return /*#__PURE__*/_jsxDEV(AceternityBadge, {\n      variant: config.variant,\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this);\n  };\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n      await fileUploadApi.uploadFile(formData);\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n    }\n  };\n  const handleViewJob = job => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n  const handleOpenChat = async job => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n  const handleDownloadFile = async (jobId, fileName) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n    }\n  };\n  const handleConfirmJob = async jobId => {\n    try {\n      await printJobApi.confirmJob(jobId);\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n  const totalSpent = printJobs.reduce((sum, job) => sum + (job.cost || 0), 0);\n  const inProgressJobs = printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length;\n  const completedJobs = printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      className: \"py-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2\",\n          children: \"Student Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 text-lg mb-6\",\n          children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: user === null || user === void 0 ? void 0 : user.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 27\n          }, this), \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: /*#__PURE__*/_jsxDEV(AceternityButton, {\n            onClick: () => setShowUploadModal(true),\n            className: \"px-8 py-3 text-lg\",\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), \"Upload Files\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-6 g-4\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"Total Jobs\",\n            value: printJobs.length,\n            icon: /*#__PURE__*/_jsxDEV(FileText, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 21\n            }, this),\n            color: \"blue\",\n            trend: {\n              value: 12,\n              isPositive: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"In Progress\",\n            value: inProgressJobs,\n            icon: /*#__PURE__*/_jsxDEV(Clock, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 21\n            }, this),\n            color: \"orange\",\n            trend: {\n              value: 5,\n              isPositive: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"Completed\",\n            value: completedJobs,\n            icon: /*#__PURE__*/_jsxDEV(CheckCircle, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 21\n            }, this),\n            color: \"green\",\n            trend: {\n              value: 8,\n              isPositive: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"Total Spent\",\n            value: `$${totalSpent.toFixed(2)}`,\n            icon: /*#__PURE__*/_jsxDEV(DollarSign, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 21\n            }, this),\n            color: \"purple\",\n            trend: {\n              value: 15,\n              isPositive: false\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"g-4\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 8,\n          children: /*#__PURE__*/_jsxDEV(AceternityCard, {\n            className: \"h-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(Activity, {\n                  className: \"w-5 h-5 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), \"Recent Print Jobs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(AceternityBadge, {\n                variant: \"info\",\n                children: [printJobs.length, \" jobs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), printJobs.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: printJobs.slice(0, 5).map((job, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: index * 0.1\n                },\n                className: \"p-4 rounded-xl bg-white/50 dark:bg-black/20 border border-white/20 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-all duration-200\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white\",\n                      children: /*#__PURE__*/_jsxDEV(FileText, {\n                        size: 20\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 302,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900 dark:text-white\",\n                        children: job.jobNumber\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 305,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: job.fileName.length > 40 ? job.fileName.slice(0, 40) + '...' : job.fileName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500 dark:text-gray-500\",\n                        children: job.xeroxCenterName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 311,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-right\",\n                      children: [getStatusBadge(job.status), job.cost && /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-semibold text-green-600 dark:text-green-400 mt-1\",\n                        children: [\"$\", job.cost.toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.1\n                        },\n                        whileTap: {\n                          scale: 0.9\n                        },\n                        onClick: () => handleDownloadFile(job.id, job.fileName),\n                        className: \"p-2 rounded-lg bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 transition-colors\",\n                        title: \"Download\",\n                        children: /*#__PURE__*/_jsxDEV(Download, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 335,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 328,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.1\n                        },\n                        whileTap: {\n                          scale: 0.9\n                        },\n                        onClick: () => handleViewJob(job),\n                        className: \"p-2 rounded-lg bg-purple-500/10 text-purple-600 hover:bg-purple-500/20 transition-colors\",\n                        title: \"View Details\",\n                        children: /*#__PURE__*/_jsxDEV(Eye, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 345,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 338,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.1\n                        },\n                        whileTap: {\n                          scale: 0.9\n                        },\n                        onClick: () => handleOpenChat(job),\n                        className: \"p-2 rounded-lg bg-green-500/10 text-green-600 hover:bg-green-500/20 transition-colors\",\n                        title: \"Chat\",\n                        children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 355,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 348,\n                        columnNumber: 29\n                      }, this), job.status === 'Quoted' && /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.1\n                        },\n                        whileTap: {\n                          scale: 0.9\n                        },\n                        onClick: () => handleConfirmJob(job.id),\n                        className: \"p-2 rounded-lg bg-green-500 text-white hover:bg-green-600 transition-colors\",\n                        title: \"Confirm Quote\",\n                        children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 366,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 359,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 327,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this)\n              }, job.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(Upload, {\n                  className: \"w-10 h-10 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                children: \"No print jobs yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                children: \"Upload your first file to get started!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(AceternityButton, {\n                onClick: () => setShowUploadModal(true),\n                children: [/*#__PURE__*/_jsxDEV(Plus, {\n                  className: \"w-4 h-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this), \"Upload File\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          children: /*#__PURE__*/_jsxDEV(AceternityCard, {\n            className: \"h-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(Printer, {\n                  className: \"w-5 h-5 inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this), \"Available Centers\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(AceternityBadge, {\n                variant: \"success\",\n                children: [xeroxCenters.filter(c => c.isActive).length, \" active\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: xeroxCenters.slice(0, 4).map((center, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: index * 0.1\n                },\n                className: \"p-4 rounded-xl bg-white/50 dark:bg-black/20 border border-white/20 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-all duration-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-gray-900 dark:text-white\",\n                      children: center.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                        size: 14,\n                        className: \"mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 427,\n                        columnNumber: 27\n                      }, this), center.location]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 426,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(AceternityBadge, {\n                    variant: center.pendingJobs <= 5 ? 'success' : center.pendingJobs <= 10 ? 'warning' : 'error',\n                    size: \"sm\",\n                    children: [center.pendingJobs, \" jobs\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Star, {\n                      className: \"w-4 h-4 text-yellow-500 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                      children: center.averageRating.toFixed(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(AceternityButton, {\n                    variant: \"outline\",\n                    size: \"sm\",\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this)]\n              }, center.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showUploadModal,\n        onHide: () => setShowUploadModal(false),\n        size: \"lg\",\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: \"Upload Files for Printing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Select File\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"file\",\n                accept: \".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\",\n                onChange: e => {\n                  const files = e.target.files;\n                  setSelectedFile(files ? files[0] : null);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                className: \"text-muted\",\n                children: \"Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Print Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: uploadData.printType,\n                    onChange: e => setUploadData(prev => ({\n                      ...prev,\n                      printType: e.target.value\n                    })),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Print\",\n                      children: \"Print\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Xerox\",\n                      children: \"Xerox\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Binding\",\n                      children: \"Binding\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Lamination\",\n                      children: \"Lamination\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Number of Copies\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"number\",\n                    min: \"1\",\n                    value: uploadData.copies,\n                    onChange: e => setUploadData(prev => ({\n                      ...prev,\n                      copies: parseInt(e.target.value)\n                    }))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Color Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: uploadData.colorType,\n                    onChange: e => setUploadData(prev => ({\n                      ...prev,\n                      colorType: e.target.value\n                    })),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"BlackWhite\",\n                      children: \"Black & White\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Color\",\n                      children: \"Color\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Paper Size\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: uploadData.paperSize,\n                    onChange: e => setUploadData(prev => ({\n                      ...prev,\n                      paperSize: e.target.value\n                    })),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"A4\",\n                      children: \"A4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"A3\",\n                      children: \"A3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Letter\",\n                      children: \"Letter\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Legal\",\n                      children: \"Legal\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Preferred Xerox Center\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: uploadData.preferredXeroxCenterId,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  preferredXeroxCenterId: e.target.value\n                })),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select a center (optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 19\n                }, this), xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: center.id,\n                  children: [center.name, \" - \", center.pendingJobs, \" pending jobs\"]\n                }, center.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Remarks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"textarea\",\n                rows: 3,\n                placeholder: \"Any special instructions or remarks...\",\n                value: uploadData.remarks,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  remarks: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowUploadModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleFileUpload,\n            disabled: !selectedFile,\n            children: \"Upload File\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showViewModal,\n        onHide: () => setShowViewModal(false),\n        size: \"lg\",\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: \"Job Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: selectedJob && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Job Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Job Number:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 20\n              }, this), \" \", selectedJob.jobNumber]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 20\n              }, this), \" \", getStatusBadge(selectedJob.status)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"File Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 20\n              }, this), \" \", selectedJob.fileName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Xerox Center:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 20\n              }, this), \" \", selectedJob.xeroxCenterName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 17\n            }, this), selectedJob.cost && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Cost:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 41\n              }, this), \" $\", selectedJob.cost.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 38\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Created:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 20\n              }, this), \" \", new Date(selectedJob.created).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this), selectedJob.estimatedCompletionTime && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Estimated Completion:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 22\n              }, this), \" \", new Date(selectedJob.estimatedCompletionTime).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowViewModal(false),\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showChatModal,\n        onHide: () => setShowChatModal(false),\n        size: \"lg\",\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: [\"Chat - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          style: {\n            height: '400px',\n            overflowY: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `mb-2 ${message.isFromStudent ? 'text-end' : 'text-start'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `d-inline-block p-2 rounded ${message.isFromStudent ? 'bg-primary text-white' : 'bg-light'}`,\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"small text-muted\",\n                children: new Date(message.timestamp).toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: /*#__PURE__*/_jsxDEV(InputGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              placeholder: \"Type your message...\",\n              value: chatMessage,\n              onChange: e => setChatMessage(e.target.value),\n              onKeyPress: e => e.key === 'Enter' && handleSendMessage()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              onClick: handleSendMessage,\n              children: \"Send\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this);\n};\n_s(AceternityStudentDashboard, \"8ErydrNvcotwx3lsAKqva+c5cB0=\", false, function () {\n  return [useAuth];\n});\n_c = AceternityStudentDashboard;\nexport default AceternityStudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"AceternityStudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Form", "Modal", "InputGroup", "motion", "Upload", "FileText", "Clock", "CheckCircle", "DollarSign", "Download", "MessageCircle", "Star", "MapPin", "Printer", "Eye", "Plus", "Activity", "useAuth", "printJobApi", "xeroxCenterApi", "fileUploadApi", "messageApi", "jsxDEV", "_jsxDEV", "AceternityStudentDashboard", "_s", "user", "printJobs", "setPrintJobs", "xeroxCenters", "setXeroxCenters", "showUploadModal", "setShowUploadModal", "showViewModal", "setShowViewModal", "showChatModal", "setShowChatModal", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "selectedFile", "setSelectedFile", "chatMessage", "setChatMessage", "messages", "setMessages", "uploadData", "setUploadData", "remarks", "preferredXeroxCenterId", "printType", "copies", "colorType", "paperSize", "fetchData", "printJobsResponse", "getStudentJobs", "data", "xeroxCentersResponse", "getAll", "error", "console", "getStatusBadge", "status", "statusConfig", "variant", "color", "config", "AceternityBadge", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleFileUpload", "formData", "FormData", "append", "toString", "uploadFile", "handleViewJob", "job", "handleOpenChat", "response", "getJobMessages", "id", "handleDownloadFile", "jobId", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleConfirmJob", "<PERSON><PERSON>ob", "handleSendMessage", "trim", "sendMessage", "prev", "totalSpent", "reduce", "sum", "cost", "inProgressJobs", "filter", "includes", "length", "completedJobs", "className", "fluid", "div", "initial", "opacity", "y", "animate", "transition", "duration", "username", "whileHover", "scale", "whileTap", "AceternityButton", "onClick", "md", "AceternityStatsCard", "title", "value", "icon", "size", "trend", "isPositive", "toFixed", "lg", "AceternityCard", "slice", "map", "index", "x", "delay", "jobNumber", "xeroxCenterName", "button", "c", "isActive", "center", "name", "location", "pendingJobs", "averageRating", "show", "onHide", "Header", "closeButton", "Title", "Body", "Group", "Label", "Control", "type", "accept", "onChange", "e", "files", "target", "Text", "Select", "min", "parseInt", "as", "rows", "placeholder", "Footer", "disabled", "Date", "created", "toLocaleString", "estimatedCompletionTime", "style", "height", "overflowY", "message", "isFromStudent", "content", "timestamp", "onKeyPress", "key", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/AceternityStudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Table, Badge, Alert, Form, Modal, InputGroup } from 'react-bootstrap';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Upload, \n  FileText, \n  Clock, \n  CheckCircle, \n  DollarSign, \n  Download, \n  MessageCircle, \n  Info, \n  Star, \n  MapPin,\n  Printer,\n  Eye,\n  Plus,\n  TrendingUp,\n  Activity\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../../services/api';\n// import { AceternityCard, AceternityStatsCard, AceternityButton, AceternityBadge } from './AceternityCard';\nimport { cn } from '../../lib/utils';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  xeroxCenterName: string;\n  created: string;\n}\n\ninterface XeroxCenter {\n  id: number;\n  name: string;\n  location: string;\n  pendingJobs: number;\n  averageRating: number;\n  isActive: boolean;\n}\n\nconst AceternityStudentDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState<any>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState<any[]>([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { variant: 'default' as const, color: 'text-gray-600' },\n      'UnderReview': { variant: 'info' as const, color: 'text-blue-600' },\n      'Quoted': { variant: 'warning' as const, color: 'text-yellow-600' },\n      'WaitingConfirmation': { variant: 'warning' as const, color: 'text-orange-600' },\n      'Confirmed': { variant: 'info' as const, color: 'text-blue-600' },\n      'InProgress': { variant: 'info' as const, color: 'text-purple-600' },\n      'Completed': { variant: 'success' as const, color: 'text-green-600' },\n      'Delivered': { variant: 'success' as const, color: 'text-green-600' },\n      'Rejected': { variant: 'error' as const, color: 'text-red-600' },\n      'Cancelled': { variant: 'default' as const, color: 'text-gray-600' }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'default' as const, color: 'text-gray-600' };\n    \n    return (\n      <AceternityBadge variant={config.variant}>\n        {status}\n      </AceternityBadge>\n    );\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n\n      await fileUploadApi.uploadFile(formData);\n\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n    }\n  };\n\n  const handleViewJob = (job: any) => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n\n  const handleOpenChat = async (job: any) => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n    }\n  };\n\n  const handleConfirmJob = async (jobId: number) => {\n    try {\n      await printJobApi.confirmJob(jobId);\n\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  const totalSpent = printJobs.reduce((sum, job) => sum + (job.cost || 0), 0);\n  const inProgressJobs = printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length;\n  const completedJobs = printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n\n  return (\n    <div className=\"min-h-screen\">\n      <Container fluid className=\"py-6\">\n        {/* Header Section */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-8\"\n        >\n          <h1 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2\">\n            Student Dashboard\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 text-lg mb-6\">\n            Welcome back, <span className=\"font-semibold\">{user?.username}</span>! \n          </p>\n          \n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <AceternityButton\n              onClick={() => setShowUploadModal(true)}\n              className=\"px-8 py-3 text-lg\"\n            >\n              <Upload className=\"w-5 h-5 mr-2\" />\n              Upload Files\n            </AceternityButton>\n          </motion.div>\n        </motion.div>\n\n        {/* Statistics Cards */}\n        <Row className=\"mb-6 g-4\">\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"Total Jobs\"\n              value={printJobs.length}\n              icon={<FileText size={24} />}\n              color=\"blue\"\n              trend={{ value: 12, isPositive: true }}\n            />\n          </Col>\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"In Progress\"\n              value={inProgressJobs}\n              icon={<Clock size={24} />}\n              color=\"orange\"\n              trend={{ value: 5, isPositive: true }}\n            />\n          </Col>\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"Completed\"\n              value={completedJobs}\n              icon={<CheckCircle size={24} />}\n              color=\"green\"\n              trend={{ value: 8, isPositive: true }}\n            />\n          </Col>\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"Total Spent\"\n              value={`$${totalSpent.toFixed(2)}`}\n              icon={<DollarSign size={24} />}\n              color=\"purple\"\n              trend={{ value: 15, isPositive: false }}\n            />\n          </Col>\n        </Row>\n\n        <Row className=\"g-4\">\n          {/* Recent Jobs */}\n          <Col lg={8}>\n            <AceternityCard className=\"h-100\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                  <Activity className=\"w-5 h-5 inline mr-2\" />\n                  Recent Print Jobs\n                </h3>\n                <AceternityBadge variant=\"info\">\n                  {printJobs.length} jobs\n                </AceternityBadge>\n              </div>\n\n              {printJobs.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {printJobs.slice(0, 5).map((job, index) => (\n                    <motion.div\n                      key={job.id}\n                      initial={{ opacity: 0, x: -20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: index * 0.1 }}\n                      className=\"p-4 rounded-xl bg-white/50 dark:bg-black/20 border border-white/20 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-all duration-200\"\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-4\">\n                          <div className=\"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white\">\n                            <FileText size={20} />\n                          </div>\n                          <div>\n                            <h4 className=\"font-semibold text-gray-900 dark:text-white\">\n                              {job.jobNumber}\n                            </h4>\n                            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                              {job.fileName.length > 40 ? job.fileName.slice(0, 40) + '...' : job.fileName}\n                            </p>\n                            <p className=\"text-xs text-gray-500 dark:text-gray-500\">\n                              {job.xeroxCenterName}\n                            </p>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"text-right\">\n                            {getStatusBadge(job.status)}\n                            {job.cost && (\n                              <p className=\"text-sm font-semibold text-green-600 dark:text-green-400 mt-1\">\n                                ${job.cost.toFixed(2)}\n                              </p>\n                            )}\n                          </div>\n                          \n                          <div className=\"flex space-x-1\">\n                            <motion.button\n                              whileHover={{ scale: 1.1 }}\n                              whileTap={{ scale: 0.9 }}\n                              onClick={() => handleDownloadFile(job.id, job.fileName)}\n                              className=\"p-2 rounded-lg bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 transition-colors\"\n                              title=\"Download\"\n                            >\n                              <Download size={16} />\n                            </motion.button>\n                            \n                            <motion.button\n                              whileHover={{ scale: 1.1 }}\n                              whileTap={{ scale: 0.9 }}\n                              onClick={() => handleViewJob(job)}\n                              className=\"p-2 rounded-lg bg-purple-500/10 text-purple-600 hover:bg-purple-500/20 transition-colors\"\n                              title=\"View Details\"\n                            >\n                              <Eye size={16} />\n                            </motion.button>\n                            \n                            <motion.button\n                              whileHover={{ scale: 1.1 }}\n                              whileTap={{ scale: 0.9 }}\n                              onClick={() => handleOpenChat(job)}\n                              className=\"p-2 rounded-lg bg-green-500/10 text-green-600 hover:bg-green-500/20 transition-colors\"\n                              title=\"Chat\"\n                            >\n                              <MessageCircle size={16} />\n                            </motion.button>\n                            \n                            {job.status === 'Quoted' && (\n                              <motion.button\n                                whileHover={{ scale: 1.1 }}\n                                whileTap={{ scale: 0.9 }}\n                                onClick={() => handleConfirmJob(job.id)}\n                                className=\"p-2 rounded-lg bg-green-500 text-white hover:bg-green-600 transition-colors\"\n                                title=\"Confirm Quote\"\n                              >\n                                <CheckCircle size={16} />\n                              </motion.button>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              ) : (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-12\"\n                >\n                  <div className=\"w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mx-auto mb-4\">\n                    <Upload className=\"w-10 h-10 text-white\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                    No print jobs yet\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n                    Upload your first file to get started!\n                  </p>\n                  <AceternityButton onClick={() => setShowUploadModal(true)}>\n                    <Plus className=\"w-4 h-4 mr-2\" />\n                    Upload File\n                  </AceternityButton>\n                </motion.div>\n              )}\n            </AceternityCard>\n          </Col>\n\n          {/* Xerox Centers */}\n          <Col lg={4}>\n            <AceternityCard className=\"h-100\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                  <Printer className=\"w-5 h-5 inline mr-2\" />\n                  Available Centers\n                </h3>\n                <AceternityBadge variant=\"success\">\n                  {xeroxCenters.filter(c => c.isActive).length} active\n                </AceternityBadge>\n              </div>\n\n              <div className=\"space-y-4\">\n                {xeroxCenters.slice(0, 4).map((center, index) => (\n                  <motion.div\n                    key={center.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    className=\"p-4 rounded-xl bg-white/50 dark:bg-black/20 border border-white/20 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-all duration-200\"\n                  >\n                    <div className=\"flex items-start justify-between mb-3\">\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900 dark:text-white\">\n                          {center.name}\n                        </h4>\n                        <div className=\"flex items-center text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                          <MapPin size={14} className=\"mr-1\" />\n                          {center.location}\n                        </div>\n                      </div>\n                      <AceternityBadge \n                        variant={center.pendingJobs <= 5 ? 'success' : center.pendingJobs <= 10 ? 'warning' : 'error'}\n                        size=\"sm\"\n                      >\n                        {center.pendingJobs} jobs\n                      </AceternityBadge>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <Star className=\"w-4 h-4 text-yellow-500 mr-1\" />\n                        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                          {center.averageRating.toFixed(1)}\n                        </span>\n                      </div>\n                      <AceternityButton variant=\"outline\" size=\"sm\">\n                        Select\n                      </AceternityButton>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </AceternityCard>\n          </Col>\n        </Row>\n\n        {/* Upload Modal */}\n        <Modal show={showUploadModal} onHide={() => setShowUploadModal(false)} size=\"lg\">\n          <Modal.Header closeButton>\n            <Modal.Title>Upload Files for Printing</Modal.Title>\n          </Modal.Header>\n          <Modal.Body>\n            <Form>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Select File</Form.Label>\n                <Form.Control\n                  type=\"file\"\n                  accept=\".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\"\n                  onChange={(e) => {\n                    const files = (e.target as HTMLInputElement).files;\n                    setSelectedFile(files ? files[0] : null);\n                  }}\n                />\n                <Form.Text className=\"text-muted\">\n                  Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\n                </Form.Text>\n              </Form.Group>\n\n              <Row>\n                <Col md={6}>\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Print Type</Form.Label>\n                    <Form.Select\n                      value={uploadData.printType}\n                      onChange={(e) => setUploadData(prev => ({ ...prev, printType: e.target.value }))}\n                    >\n                      <option value=\"Print\">Print</option>\n                      <option value=\"Xerox\">Xerox</option>\n                      <option value=\"Binding\">Binding</option>\n                      <option value=\"Lamination\">Lamination</option>\n                    </Form.Select>\n                  </Form.Group>\n                </Col>\n                <Col md={6}>\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Number of Copies</Form.Label>\n                    <Form.Control\n                      type=\"number\"\n                      min=\"1\"\n                      value={uploadData.copies}\n                      onChange={(e) => setUploadData(prev => ({ ...prev, copies: parseInt(e.target.value) }))}\n                    />\n                  </Form.Group>\n                </Col>\n              </Row>\n\n              <Row>\n                <Col md={6}>\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Color Type</Form.Label>\n                    <Form.Select\n                      value={uploadData.colorType}\n                      onChange={(e) => setUploadData(prev => ({ ...prev, colorType: e.target.value }))}\n                    >\n                      <option value=\"BlackWhite\">Black & White</option>\n                      <option value=\"Color\">Color</option>\n                    </Form.Select>\n                  </Form.Group>\n                </Col>\n                <Col md={6}>\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Paper Size</Form.Label>\n                    <Form.Select\n                      value={uploadData.paperSize}\n                      onChange={(e) => setUploadData(prev => ({ ...prev, paperSize: e.target.value }))}\n                    >\n                      <option value=\"A4\">A4</option>\n                      <option value=\"A3\">A3</option>\n                      <option value=\"Letter\">Letter</option>\n                      <option value=\"Legal\">Legal</option>\n                    </Form.Select>\n                  </Form.Group>\n                </Col>\n              </Row>\n\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Preferred Xerox Center</Form.Label>\n                <Form.Select\n                  value={uploadData.preferredXeroxCenterId}\n                  onChange={(e) => setUploadData(prev => ({ ...prev, preferredXeroxCenterId: e.target.value }))}\n                >\n                  <option value=\"\">Select a center (optional)</option>\n                  {xeroxCenters.map(center => (\n                    <option key={center.id} value={center.id}>\n                      {center.name} - {center.pendingJobs} pending jobs\n                    </option>\n                  ))}\n                </Form.Select>\n              </Form.Group>\n\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Remarks</Form.Label>\n                <Form.Control\n                  as=\"textarea\"\n                  rows={3}\n                  placeholder=\"Any special instructions or remarks...\"\n                  value={uploadData.remarks}\n                  onChange={(e) => setUploadData(prev => ({ ...prev, remarks: e.target.value }))}\n                />\n              </Form.Group>\n            </Form>\n          </Modal.Body>\n          <Modal.Footer>\n            <Button variant=\"secondary\" onClick={() => setShowUploadModal(false)}>\n              Cancel\n            </Button>\n            <Button\n              variant=\"primary\"\n              onClick={handleFileUpload}\n              disabled={!selectedFile}\n            >\n              Upload File\n            </Button>\n          </Modal.Footer>\n        </Modal>\n\n        {/* View Job Modal */}\n        <Modal show={showViewModal} onHide={() => setShowViewModal(false)} size=\"lg\">\n          <Modal.Header closeButton>\n            <Modal.Title>Job Details</Modal.Title>\n          </Modal.Header>\n          <Modal.Body>\n            {selectedJob && (\n              <div>\n                <h6>Job Information</h6>\n                <p><strong>Job Number:</strong> {selectedJob.jobNumber}</p>\n                <p><strong>Status:</strong> {getStatusBadge(selectedJob.status)}</p>\n                <p><strong>File Name:</strong> {selectedJob.fileName}</p>\n                <p><strong>Xerox Center:</strong> {selectedJob.xeroxCenterName}</p>\n                {selectedJob.cost && <p><strong>Cost:</strong> ${selectedJob.cost.toFixed(2)}</p>}\n                <p><strong>Created:</strong> {new Date(selectedJob.created).toLocaleString()}</p>\n                {selectedJob.estimatedCompletionTime && (\n                  <p><strong>Estimated Completion:</strong> {new Date(selectedJob.estimatedCompletionTime).toLocaleString()}</p>\n                )}\n              </div>\n            )}\n          </Modal.Body>\n          <Modal.Footer>\n            <Button variant=\"secondary\" onClick={() => setShowViewModal(false)}>\n              Close\n            </Button>\n          </Modal.Footer>\n        </Modal>\n\n        {/* Chat Modal */}\n        <Modal show={showChatModal} onHide={() => setShowChatModal(false)} size=\"lg\">\n          <Modal.Header closeButton>\n            <Modal.Title>Chat - {selectedJob?.jobNumber}</Modal.Title>\n          </Modal.Header>\n          <Modal.Body style={{ height: '400px', overflowY: 'auto' }}>\n            <div className=\"mb-3\">\n              {messages.map((message, index) => (\n                <div key={index} className={`mb-2 ${message.isFromStudent ? 'text-end' : 'text-start'}`}>\n                  <div className={`d-inline-block p-2 rounded ${message.isFromStudent ? 'bg-primary text-white' : 'bg-light'}`}>\n                    {message.content}\n                  </div>\n                  <div className=\"small text-muted\">\n                    {new Date(message.timestamp).toLocaleString()}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </Modal.Body>\n          <Modal.Footer>\n            <InputGroup>\n              <Form.Control\n                type=\"text\"\n                placeholder=\"Type your message...\"\n                value={chatMessage}\n                onChange={(e) => setChatMessage(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}\n              />\n              <Button variant=\"primary\" onClick={handleSendMessage}>\n                Send\n              </Button>\n            </InputGroup>\n          </Modal.Footer>\n        </Modal>\n      </Container>\n    </div>\n  );\n};\n\nexport default AceternityStudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAuBC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,iBAAiB;AAC3G,SAASC,MAAM,QAAyB,eAAe;AACvD,SACEC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,QAAQ,EACRC,aAAa,EAEbC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,IAAI,EAEJC,QAAQ,QACH,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,EAAEC,cAAc,EAAEC,aAAa,EAAEC,UAAU,QAAQ,oBAAoB;AAC3F;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAuBA,MAAMC,0BAAoC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjD,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC;IAC3CqD,OAAO,EAAE,EAAE;IACXC,sBAAsB,EAAE,EAAE;IAC1BC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFzD,SAAS,CAAC,MAAM;IACd,MAAM0D,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,iBAAiB,GAAG,MAAMpC,WAAW,CAACqC,cAAc,CAAC,CAAC;QAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;QAEpC,MAAMC,oBAAoB,GAAG,MAAMtC,cAAc,CAACuC,MAAM,CAAC,CAAC;QAC1D5B,eAAe,CAAC2B,oBAAoB,CAACD,IAAI,CAAC;MAC5C,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C/B,YAAY,CAAC,EAAE,CAAC;QAChBE,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC;IAEDuB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAgB,CAAC;MACpE,aAAa,EAAE;QAAED,OAAO,EAAE,MAAe;QAAEC,KAAK,EAAE;MAAgB,CAAC;MACnE,QAAQ,EAAE;QAAED,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAkB,CAAC;MACnE,qBAAqB,EAAE;QAAED,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAkB,CAAC;MAChF,WAAW,EAAE;QAAED,OAAO,EAAE,MAAe;QAAEC,KAAK,EAAE;MAAgB,CAAC;MACjE,YAAY,EAAE;QAAED,OAAO,EAAE,MAAe;QAAEC,KAAK,EAAE;MAAkB,CAAC;MACpE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAiB,CAAC;MACrE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAiB,CAAC;MACrE,UAAU,EAAE;QAAED,OAAO,EAAE,OAAgB;QAAEC,KAAK,EAAE;MAAe,CAAC;MAChE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAkB;QAAEC,KAAK,EAAE;MAAgB;IACrE,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAAEE,OAAO,EAAE,SAAkB;MAAEC,KAAK,EAAE;IAAgB,CAAC;IAE3H,oBACE1C,OAAA,CAAC4C,eAAe;MAACH,OAAO,EAAEE,MAAM,CAACF,OAAQ;MAAAI,QAAA,EACtCN;IAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAEtB,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAClC,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMmC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAErC,YAAY,CAAC;MACrCmC,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE/B,UAAU,CAACE,OAAO,CAAC;MAC9C2B,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAE/B,UAAU,CAACG,sBAAsB,CAAC;MAC5E0B,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE/B,UAAU,CAACI,SAAS,CAAC;MAClDyB,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE/B,UAAU,CAACK,MAAM,CAAC2B,QAAQ,CAAC,CAAC,CAAC;MACvDH,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE/B,UAAU,CAACM,SAAS,CAAC;MAClDuB,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE/B,UAAU,CAACO,SAAS,CAAC;MAElD,MAAMhC,aAAa,CAAC0D,UAAU,CAACJ,QAAQ,CAAC;MAExC,MAAMpB,iBAAiB,GAAG,MAAMpC,WAAW,CAACqC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;MAEpCxB,kBAAkB,CAAC,KAAK,CAAC;MACzBQ,eAAe,CAAC,IAAI,CAAC;MACrBM,aAAa,CAAC;QACZC,OAAO,EAAE,EAAE;QACXC,sBAAsB,EAAE,EAAE;QAC1BC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMoB,aAAa,GAAIC,GAAQ,IAAK;IAClC1C,cAAc,CAAC0C,GAAG,CAAC;IACnB9C,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM+C,cAAc,GAAG,MAAOD,GAAQ,IAAK;IACzC,IAAI;MACF1C,cAAc,CAAC0C,GAAG,CAAC;MACnB5C,gBAAgB,CAAC,IAAI,CAAC;MAEtB,MAAM8C,QAAQ,GAAG,MAAM7D,UAAU,CAAC8D,cAAc,CAACH,GAAG,CAACI,EAAE,CAAC;MACxDxC,WAAW,CAACsC,QAAQ,CAAC1B,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/Cf,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;EAED,MAAMyC,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAEjB,QAAgB,KAAK;IACpE,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAM9D,aAAa,CAACmE,YAAY,CAACD,KAAK,CAAC;MAExD,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACP,QAAQ,CAAC1B,IAAI,CAAC,CAAC;MACtC,MAAMkC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAG7B,QAAQ;MACxB0B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAM6C,gBAAgB,GAAG,MAAOlB,KAAa,IAAK;IAChD,IAAI;MACF,MAAMpE,WAAW,CAACuF,UAAU,CAACnB,KAAK,CAAC;MAEnC,MAAMhC,iBAAiB,GAAG,MAAMpC,WAAW,CAACqC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAM+C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACjE,WAAW,CAACkE,IAAI,CAAC,CAAC,IAAI,CAACtE,WAAW,EAAE;IAEzC,IAAI;MACF,MAAM6C,QAAQ,GAAG,MAAM7D,UAAU,CAACuF,WAAW,CAACvE,WAAW,CAAC+C,EAAE,EAAE3C,WAAW,CAACkE,IAAI,CAAC,CAAC,CAAC;MAEjF/D,WAAW,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE3B,QAAQ,CAAC1B,IAAI,CAAC,CAAC;MAC7Cd,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMmD,UAAU,GAAGnF,SAAS,CAACoF,MAAM,CAAC,CAACC,GAAG,EAAEhC,GAAG,KAAKgC,GAAG,IAAIhC,GAAG,CAACiC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E,MAAMC,cAAc,GAAGvF,SAAS,CAACwF,MAAM,CAACnC,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAACoC,QAAQ,CAACpC,GAAG,CAAClB,MAAM,CAAC,CAAC,CAACuD,MAAM;EACtH,MAAMC,aAAa,GAAG3F,SAAS,CAACwF,MAAM,CAACnC,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACoC,QAAQ,CAACpC,GAAG,CAAClB,MAAM,CAAC,CAAC,CAACuD,MAAM;EAErG,oBACE9F,OAAA;IAAKgG,SAAS,EAAC,cAAc;IAAAnD,QAAA,eAC3B7C,OAAA,CAAC3B,SAAS;MAAC4H,KAAK;MAACD,SAAS,EAAC,MAAM;MAAAnD,QAAA,gBAE/B7C,OAAA,CAACpB,MAAM,CAACsH,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,kBAAkB;QAAAnD,QAAA,gBAE5B7C,OAAA;UAAIgG,SAAS,EAAC,oGAAoG;UAAAnD,QAAA,EAAC;QAEnH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjD,OAAA;UAAGgG,SAAS,EAAC,+CAA+C;UAAAnD,QAAA,GAAC,gBAC7C,eAAA7C,OAAA;YAAMgG,SAAS,EAAC,eAAe;YAAAnD,QAAA,EAAE1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsG;UAAQ;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,KACvE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJjD,OAAA,CAACpB,MAAM,CAACsH,GAAG;UACTQ,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAA9D,QAAA,eAE1B7C,OAAA,CAAC6G,gBAAgB;YACfC,OAAO,EAAEA,CAAA,KAAMrG,kBAAkB,CAAC,IAAI,CAAE;YACxCuF,SAAS,EAAC,mBAAmB;YAAAnD,QAAA,gBAE7B7C,OAAA,CAACnB,MAAM;cAACmH,SAAS,EAAC;YAAc;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGbjD,OAAA,CAAC1B,GAAG;QAAC0H,SAAS,EAAC,UAAU;QAAAnD,QAAA,gBACvB7C,OAAA,CAACzB,GAAG;UAACwI,EAAE,EAAE,CAAE;UAAAlE,QAAA,eACT7C,OAAA,CAACgH,mBAAmB;YAClBC,KAAK,EAAC,YAAY;YAClBC,KAAK,EAAE9G,SAAS,CAAC0F,MAAO;YACxBqB,IAAI,eAAEnH,OAAA,CAAClB,QAAQ;cAACsI,IAAI,EAAE;YAAG;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BP,KAAK,EAAC,MAAM;YACZ2E,KAAK,EAAE;cAAEH,KAAK,EAAE,EAAE;cAAEI,UAAU,EAAE;YAAK;UAAE;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNjD,OAAA,CAACzB,GAAG;UAACwI,EAAE,EAAE,CAAE;UAAAlE,QAAA,eACT7C,OAAA,CAACgH,mBAAmB;YAClBC,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAEvB,cAAe;YACtBwB,IAAI,eAAEnH,OAAA,CAACjB,KAAK;cAACqI,IAAI,EAAE;YAAG;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BP,KAAK,EAAC,QAAQ;YACd2E,KAAK,EAAE;cAAEH,KAAK,EAAE,CAAC;cAAEI,UAAU,EAAE;YAAK;UAAE;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNjD,OAAA,CAACzB,GAAG;UAACwI,EAAE,EAAE,CAAE;UAAAlE,QAAA,eACT7C,OAAA,CAACgH,mBAAmB;YAClBC,KAAK,EAAC,WAAW;YACjBC,KAAK,EAAEnB,aAAc;YACrBoB,IAAI,eAAEnH,OAAA,CAAChB,WAAW;cAACoI,IAAI,EAAE;YAAG;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCP,KAAK,EAAC,OAAO;YACb2E,KAAK,EAAE;cAAEH,KAAK,EAAE,CAAC;cAAEI,UAAU,EAAE;YAAK;UAAE;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNjD,OAAA,CAACzB,GAAG;UAACwI,EAAE,EAAE,CAAE;UAAAlE,QAAA,eACT7C,OAAA,CAACgH,mBAAmB;YAClBC,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE,IAAI3B,UAAU,CAACgC,OAAO,CAAC,CAAC,CAAC,EAAG;YACnCJ,IAAI,eAAEnH,OAAA,CAACf,UAAU;cAACmI,IAAI,EAAE;YAAG;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BP,KAAK,EAAC,QAAQ;YACd2E,KAAK,EAAE;cAAEH,KAAK,EAAE,EAAE;cAAEI,UAAU,EAAE;YAAM;UAAE;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjD,OAAA,CAAC1B,GAAG;QAAC0H,SAAS,EAAC,KAAK;QAAAnD,QAAA,gBAElB7C,OAAA,CAACzB,GAAG;UAACiJ,EAAE,EAAE,CAAE;UAAA3E,QAAA,eACT7C,OAAA,CAACyH,cAAc;YAACzB,SAAS,EAAC,OAAO;YAAAnD,QAAA,gBAC/B7C,OAAA;cAAKgG,SAAS,EAAC,wCAAwC;cAAAnD,QAAA,gBACrD7C,OAAA;gBAAIgG,SAAS,EAAC,kGAAkG;gBAAAnD,QAAA,gBAC9G7C,OAAA,CAACP,QAAQ;kBAACuG,SAAS,EAAC;gBAAqB;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBAE9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjD,OAAA,CAAC4C,eAAe;gBAACH,OAAO,EAAC,MAAM;gBAAAI,QAAA,GAC5BzC,SAAS,CAAC0F,MAAM,EAAC,OACpB;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EAEL7C,SAAS,CAAC0F,MAAM,GAAG,CAAC,gBACnB9F,OAAA;cAAKgG,SAAS,EAAC,WAAW;cAAAnD,QAAA,EACvBzC,SAAS,CAACsH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAAClE,GAAG,EAAEmE,KAAK,kBACpC5H,OAAA,CAACpB,MAAM,CAACsH,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEyB,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCvB,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEyB,CAAC,EAAE;gBAAE,CAAE;gBAC9BtB,UAAU,EAAE;kBAAEuB,KAAK,EAAEF,KAAK,GAAG;gBAAI,CAAE;gBACnC5B,SAAS,EAAC,8JAA8J;gBAAAnD,QAAA,eAExK7C,OAAA;kBAAKgG,SAAS,EAAC,mCAAmC;kBAAAnD,QAAA,gBAChD7C,OAAA;oBAAKgG,SAAS,EAAC,6BAA6B;oBAAAnD,QAAA,gBAC1C7C,OAAA;sBAAKgG,SAAS,EAAC,gHAAgH;sBAAAnD,QAAA,eAC7H7C,OAAA,CAAClB,QAAQ;wBAACsI,IAAI,EAAE;sBAAG;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACNjD,OAAA;sBAAA6C,QAAA,gBACE7C,OAAA;wBAAIgG,SAAS,EAAC,6CAA6C;wBAAAnD,QAAA,EACxDY,GAAG,CAACsE;sBAAS;wBAAAjF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACLjD,OAAA;wBAAGgG,SAAS,EAAC,0CAA0C;wBAAAnD,QAAA,EACpDY,GAAG,CAACX,QAAQ,CAACgD,MAAM,GAAG,EAAE,GAAGrC,GAAG,CAACX,QAAQ,CAAC4E,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGjE,GAAG,CAACX;sBAAQ;wBAAAA,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3E,CAAC,eACJjD,OAAA;wBAAGgG,SAAS,EAAC,0CAA0C;wBAAAnD,QAAA,EACpDY,GAAG,CAACuE;sBAAe;wBAAAlF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENjD,OAAA;oBAAKgG,SAAS,EAAC,6BAA6B;oBAAAnD,QAAA,gBAC1C7C,OAAA;sBAAKgG,SAAS,EAAC,YAAY;sBAAAnD,QAAA,GACxBP,cAAc,CAACmB,GAAG,CAAClB,MAAM,CAAC,EAC1BkB,GAAG,CAACiC,IAAI,iBACP1F,OAAA;wBAAGgG,SAAS,EAAC,+DAA+D;wBAAAnD,QAAA,GAAC,GAC1E,EAACY,GAAG,CAACiC,IAAI,CAAC6B,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAzE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CACJ;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAENjD,OAAA;sBAAKgG,SAAS,EAAC,gBAAgB;sBAAAnD,QAAA,gBAC7B7C,OAAA,CAACpB,MAAM,CAACqJ,MAAM;wBACZvB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAI,CAAE;wBAC3BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAI,CAAE;wBACzBG,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAACL,GAAG,CAACI,EAAE,EAAEJ,GAAG,CAACX,QAAQ,CAAE;wBACxDkD,SAAS,EAAC,oFAAoF;wBAC9FiB,KAAK,EAAC,UAAU;wBAAApE,QAAA,eAEhB7C,OAAA,CAACd,QAAQ;0BAACkI,IAAI,EAAE;wBAAG;0BAAAtE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC,eAEhBjD,OAAA,CAACpB,MAAM,CAACqJ,MAAM;wBACZvB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAI,CAAE;wBAC3BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAI,CAAE;wBACzBG,OAAO,EAAEA,CAAA,KAAMtD,aAAa,CAACC,GAAG,CAAE;wBAClCuC,SAAS,EAAC,0FAA0F;wBACpGiB,KAAK,EAAC,cAAc;wBAAApE,QAAA,eAEpB7C,OAAA,CAACT,GAAG;0BAAC6H,IAAI,EAAE;wBAAG;0BAAAtE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eAEhBjD,OAAA,CAACpB,MAAM,CAACqJ,MAAM;wBACZvB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAI,CAAE;wBAC3BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAI,CAAE;wBACzBG,OAAO,EAAEA,CAAA,KAAMpD,cAAc,CAACD,GAAG,CAAE;wBACnCuC,SAAS,EAAC,uFAAuF;wBACjGiB,KAAK,EAAC,MAAM;wBAAApE,QAAA,eAEZ7C,OAAA,CAACb,aAAa;0BAACiI,IAAI,EAAE;wBAAG;0BAAAtE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,EAEfQ,GAAG,CAAClB,MAAM,KAAK,QAAQ,iBACtBvC,OAAA,CAACpB,MAAM,CAACqJ,MAAM;wBACZvB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAI,CAAE;wBAC3BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAI,CAAE;wBACzBG,OAAO,EAAEA,CAAA,KAAM7B,gBAAgB,CAACxB,GAAG,CAACI,EAAE,CAAE;wBACxCmC,SAAS,EAAC,6EAA6E;wBACvFiB,KAAK,EAAC,eAAe;wBAAApE,QAAA,eAErB7C,OAAA,CAAChB,WAAW;0BAACoI,IAAI,EAAE;wBAAG;0BAAAtE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAChB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA9EDQ,GAAG,CAACI,EAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA+ED,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAENjD,OAAA,CAACpB,MAAM,CAACsH,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAI,CAAE;cACpCL,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAE,CAAE;cAClCX,SAAS,EAAC,mBAAmB;cAAAnD,QAAA,gBAE7B7C,OAAA;gBAAKgG,SAAS,EAAC,oHAAoH;gBAAAnD,QAAA,eACjI7C,OAAA,CAACnB,MAAM;kBAACmH,SAAS,EAAC;gBAAsB;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACNjD,OAAA;gBAAIgG,SAAS,EAAC,0DAA0D;gBAAAnD,QAAA,EAAC;cAEzE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjD,OAAA;gBAAGgG,SAAS,EAAC,uCAAuC;gBAAAnD,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJjD,OAAA,CAAC6G,gBAAgB;gBAACC,OAAO,EAAEA,CAAA,KAAMrG,kBAAkB,CAAC,IAAI,CAAE;gBAAAoC,QAAA,gBACxD7C,OAAA,CAACR,IAAI;kBAACwG,SAAS,EAAC;gBAAc;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEnC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACa;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eAGNjD,OAAA,CAACzB,GAAG;UAACiJ,EAAE,EAAE,CAAE;UAAA3E,QAAA,eACT7C,OAAA,CAACyH,cAAc;YAACzB,SAAS,EAAC,OAAO;YAAAnD,QAAA,gBAC/B7C,OAAA;cAAKgG,SAAS,EAAC,wCAAwC;cAAAnD,QAAA,gBACrD7C,OAAA;gBAAIgG,SAAS,EAAC,kGAAkG;gBAAAnD,QAAA,gBAC9G7C,OAAA,CAACV,OAAO;kBAAC0G,SAAS,EAAC;gBAAqB;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBAE7C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjD,OAAA,CAAC4C,eAAe;gBAACH,OAAO,EAAC,SAAS;gBAAAI,QAAA,GAC/BvC,YAAY,CAACsF,MAAM,CAACsC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAACrC,MAAM,EAAC,SAC/C;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAENjD,OAAA;cAAKgG,SAAS,EAAC,WAAW;cAAAnD,QAAA,EACvBvC,YAAY,CAACoH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACS,MAAM,EAAER,KAAK,kBAC1C5H,OAAA,CAACpB,MAAM,CAACsH,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,UAAU,EAAE;kBAAEuB,KAAK,EAAEF,KAAK,GAAG;gBAAI,CAAE;gBACnC5B,SAAS,EAAC,8JAA8J;gBAAAnD,QAAA,gBAExK7C,OAAA;kBAAKgG,SAAS,EAAC,uCAAuC;kBAAAnD,QAAA,gBACpD7C,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAIgG,SAAS,EAAC,6CAA6C;sBAAAnD,QAAA,EACxDuF,MAAM,CAACC;oBAAI;sBAAAvF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACLjD,OAAA;sBAAKgG,SAAS,EAAC,iEAAiE;sBAAAnD,QAAA,gBAC9E7C,OAAA,CAACX,MAAM;wBAAC+H,IAAI,EAAE,EAAG;wBAACpB,SAAS,EAAC;sBAAM;wBAAAlD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACpCmF,MAAM,CAACE,QAAQ;oBAAA;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNjD,OAAA,CAAC4C,eAAe;oBACdH,OAAO,EAAE2F,MAAM,CAACG,WAAW,IAAI,CAAC,GAAG,SAAS,GAAGH,MAAM,CAACG,WAAW,IAAI,EAAE,GAAG,SAAS,GAAG,OAAQ;oBAC9FnB,IAAI,EAAC,IAAI;oBAAAvE,QAAA,GAERuF,MAAM,CAACG,WAAW,EAAC,OACtB;kBAAA;oBAAAzF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAENjD,OAAA;kBAAKgG,SAAS,EAAC,mCAAmC;kBAAAnD,QAAA,gBAChD7C,OAAA;oBAAKgG,SAAS,EAAC,mBAAmB;oBAAAnD,QAAA,gBAChC7C,OAAA,CAACZ,IAAI;sBAAC4G,SAAS,EAAC;oBAA8B;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjDjD,OAAA;sBAAMgG,SAAS,EAAC,sDAAsD;sBAAAnD,QAAA,EACnEuF,MAAM,CAACI,aAAa,CAACjB,OAAO,CAAC,CAAC;oBAAC;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNjD,OAAA,CAAC6G,gBAAgB;oBAACpE,OAAO,EAAC,SAAS;oBAAC2E,IAAI,EAAC,IAAI;oBAAAvE,QAAA,EAAC;kBAE9C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAkB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA,GAlCDmF,MAAM,CAACvE,EAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmCJ,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjD,OAAA,CAACtB,KAAK;QAAC+J,IAAI,EAAEjI,eAAgB;QAACkI,MAAM,EAAEA,CAAA,KAAMjI,kBAAkB,CAAC,KAAK,CAAE;QAAC2G,IAAI,EAAC,IAAI;QAAAvE,QAAA,gBAC9E7C,OAAA,CAACtB,KAAK,CAACiK,MAAM;UAACC,WAAW;UAAA/F,QAAA,eACvB7C,OAAA,CAACtB,KAAK,CAACmK,KAAK;YAAAhG,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACfjD,OAAA,CAACtB,KAAK,CAACoK,IAAI;UAAAjG,QAAA,eACT7C,OAAA,CAACvB,IAAI;YAAAoE,QAAA,gBACH7C,OAAA,CAACvB,IAAI,CAACsK,KAAK;cAAC/C,SAAS,EAAC,MAAM;cAAAnD,QAAA,gBAC1B7C,OAAA,CAACvB,IAAI,CAACuK,KAAK;gBAAAnG,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCjD,OAAA,CAACvB,IAAI,CAACwK,OAAO;gBACXC,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,2CAA2C;gBAClDC,QAAQ,EAAGC,CAAC,IAAK;kBACf,MAAMC,KAAK,GAAID,CAAC,CAACE,MAAM,CAAsBD,KAAK;kBAClDrI,eAAe,CAACqI,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC1C;cAAE;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFjD,OAAA,CAACvB,IAAI,CAAC+K,IAAI;gBAACxD,SAAS,EAAC,YAAY;gBAAAnD,QAAA,EAAC;cAElC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEbjD,OAAA,CAAC1B,GAAG;cAAAuE,QAAA,gBACF7C,OAAA,CAACzB,GAAG;gBAACwI,EAAE,EAAE,CAAE;gBAAAlE,QAAA,eACT7C,OAAA,CAACvB,IAAI,CAACsK,KAAK;kBAAC/C,SAAS,EAAC,MAAM;kBAAAnD,QAAA,gBAC1B7C,OAAA,CAACvB,IAAI,CAACuK,KAAK;oBAAAnG,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnCjD,OAAA,CAACvB,IAAI,CAACgL,MAAM;oBACVvC,KAAK,EAAE5F,UAAU,CAACI,SAAU;oBAC5B0H,QAAQ,EAAGC,CAAC,IAAK9H,aAAa,CAAC+D,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE5D,SAAS,EAAE2H,CAAC,CAACE,MAAM,CAACrC;oBAAM,CAAC,CAAC,CAAE;oBAAArE,QAAA,gBAEjF7C,OAAA;sBAAQkH,KAAK,EAAC,OAAO;sBAAArE,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCjD,OAAA;sBAAQkH,KAAK,EAAC,OAAO;sBAAArE,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCjD,OAAA;sBAAQkH,KAAK,EAAC,SAAS;sBAAArE,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCjD,OAAA;sBAAQkH,KAAK,EAAC,YAAY;sBAAArE,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjD,OAAA,CAACzB,GAAG;gBAACwI,EAAE,EAAE,CAAE;gBAAAlE,QAAA,eACT7C,OAAA,CAACvB,IAAI,CAACsK,KAAK;kBAAC/C,SAAS,EAAC,MAAM;kBAAAnD,QAAA,gBAC1B7C,OAAA,CAACvB,IAAI,CAACuK,KAAK;oBAAAnG,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzCjD,OAAA,CAACvB,IAAI,CAACwK,OAAO;oBACXC,IAAI,EAAC,QAAQ;oBACbQ,GAAG,EAAC,GAAG;oBACPxC,KAAK,EAAE5F,UAAU,CAACK,MAAO;oBACzByH,QAAQ,EAAGC,CAAC,IAAK9H,aAAa,CAAC+D,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE3D,MAAM,EAAEgI,QAAQ,CAACN,CAAC,CAACE,MAAM,CAACrC,KAAK;oBAAE,CAAC,CAAC;kBAAE;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjD,OAAA,CAAC1B,GAAG;cAAAuE,QAAA,gBACF7C,OAAA,CAACzB,GAAG;gBAACwI,EAAE,EAAE,CAAE;gBAAAlE,QAAA,eACT7C,OAAA,CAACvB,IAAI,CAACsK,KAAK;kBAAC/C,SAAS,EAAC,MAAM;kBAAAnD,QAAA,gBAC1B7C,OAAA,CAACvB,IAAI,CAACuK,KAAK;oBAAAnG,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnCjD,OAAA,CAACvB,IAAI,CAACgL,MAAM;oBACVvC,KAAK,EAAE5F,UAAU,CAACM,SAAU;oBAC5BwH,QAAQ,EAAGC,CAAC,IAAK9H,aAAa,CAAC+D,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE1D,SAAS,EAAEyH,CAAC,CAACE,MAAM,CAACrC;oBAAM,CAAC,CAAC,CAAE;oBAAArE,QAAA,gBAEjF7C,OAAA;sBAAQkH,KAAK,EAAC,YAAY;sBAAArE,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjDjD,OAAA;sBAAQkH,KAAK,EAAC,OAAO;sBAAArE,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjD,OAAA,CAACzB,GAAG;gBAACwI,EAAE,EAAE,CAAE;gBAAAlE,QAAA,eACT7C,OAAA,CAACvB,IAAI,CAACsK,KAAK;kBAAC/C,SAAS,EAAC,MAAM;kBAAAnD,QAAA,gBAC1B7C,OAAA,CAACvB,IAAI,CAACuK,KAAK;oBAAAnG,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnCjD,OAAA,CAACvB,IAAI,CAACgL,MAAM;oBACVvC,KAAK,EAAE5F,UAAU,CAACO,SAAU;oBAC5BuH,QAAQ,EAAGC,CAAC,IAAK9H,aAAa,CAAC+D,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEzD,SAAS,EAAEwH,CAAC,CAACE,MAAM,CAACrC;oBAAM,CAAC,CAAC,CAAE;oBAAArE,QAAA,gBAEjF7C,OAAA;sBAAQkH,KAAK,EAAC,IAAI;sBAAArE,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9BjD,OAAA;sBAAQkH,KAAK,EAAC,IAAI;sBAAArE,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9BjD,OAAA;sBAAQkH,KAAK,EAAC,QAAQ;sBAAArE,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCjD,OAAA;sBAAQkH,KAAK,EAAC,OAAO;sBAAArE,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjD,OAAA,CAACvB,IAAI,CAACsK,KAAK;cAAC/C,SAAS,EAAC,MAAM;cAAAnD,QAAA,gBAC1B7C,OAAA,CAACvB,IAAI,CAACuK,KAAK;gBAAAnG,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/CjD,OAAA,CAACvB,IAAI,CAACgL,MAAM;gBACVvC,KAAK,EAAE5F,UAAU,CAACG,sBAAuB;gBACzC2H,QAAQ,EAAGC,CAAC,IAAK9H,aAAa,CAAC+D,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE7D,sBAAsB,EAAE4H,CAAC,CAACE,MAAM,CAACrC;gBAAM,CAAC,CAAC,CAAE;gBAAArE,QAAA,gBAE9F7C,OAAA;kBAAQkH,KAAK,EAAC,EAAE;kBAAArE,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACnD3C,YAAY,CAACqH,GAAG,CAACS,MAAM,iBACtBpI,OAAA;kBAAwBkH,KAAK,EAAEkB,MAAM,CAACvE,EAAG;kBAAAhB,QAAA,GACtCuF,MAAM,CAACC,IAAI,EAAC,KAAG,EAACD,MAAM,CAACG,WAAW,EAAC,eACtC;gBAAA,GAFaH,MAAM,CAACvE,EAAE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEd,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEbjD,OAAA,CAACvB,IAAI,CAACsK,KAAK;cAAC/C,SAAS,EAAC,MAAM;cAAAnD,QAAA,gBAC1B7C,OAAA,CAACvB,IAAI,CAACuK,KAAK;gBAAAnG,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChCjD,OAAA,CAACvB,IAAI,CAACwK,OAAO;gBACXW,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAE,CAAE;gBACRC,WAAW,EAAC,wCAAwC;gBACpD5C,KAAK,EAAE5F,UAAU,CAACE,OAAQ;gBAC1B4H,QAAQ,EAAGC,CAAC,IAAK9H,aAAa,CAAC+D,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE9D,OAAO,EAAE6H,CAAC,CAACE,MAAM,CAACrC;gBAAM,CAAC,CAAC;cAAE;gBAAApE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACbjD,OAAA,CAACtB,KAAK,CAACqL,MAAM;UAAAlH,QAAA,gBACX7C,OAAA,CAACxB,MAAM;YAACiE,OAAO,EAAC,WAAW;YAACqE,OAAO,EAAEA,CAAA,KAAMrG,kBAAkB,CAAC,KAAK,CAAE;YAAAoC,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjD,OAAA,CAACxB,MAAM;YACLiE,OAAO,EAAC,SAAS;YACjBqE,OAAO,EAAE5D,gBAAiB;YAC1B8G,QAAQ,EAAE,CAAChJ,YAAa;YAAA6B,QAAA,EACzB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGRjD,OAAA,CAACtB,KAAK;QAAC+J,IAAI,EAAE/H,aAAc;QAACgI,MAAM,EAAEA,CAAA,KAAM/H,gBAAgB,CAAC,KAAK,CAAE;QAACyG,IAAI,EAAC,IAAI;QAAAvE,QAAA,gBAC1E7C,OAAA,CAACtB,KAAK,CAACiK,MAAM;UAACC,WAAW;UAAA/F,QAAA,eACvB7C,OAAA,CAACtB,KAAK,CAACmK,KAAK;YAAAhG,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACfjD,OAAA,CAACtB,KAAK,CAACoK,IAAI;UAAAjG,QAAA,EACR/B,WAAW,iBACVd,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAA6C,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBjD,OAAA;cAAA6C,QAAA,gBAAG7C,OAAA;gBAAA6C,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACnC,WAAW,CAACiH,SAAS;YAAA;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DjD,OAAA;cAAA6C,QAAA,gBAAG7C,OAAA;gBAAA6C,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACX,cAAc,CAACxB,WAAW,CAACyB,MAAM,CAAC;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpEjD,OAAA;cAAA6C,QAAA,gBAAG7C,OAAA;gBAAA6C,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACnC,WAAW,CAACgC,QAAQ;YAAA;cAAAA,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDjD,OAAA;cAAA6C,QAAA,gBAAG7C,OAAA;gBAAA6C,QAAA,EAAQ;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACnC,WAAW,CAACkH,eAAe;YAAA;cAAAlF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAClEnC,WAAW,CAAC4E,IAAI,iBAAI1F,OAAA;cAAA6C,QAAA,gBAAG7C,OAAA;gBAAA6C,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,MAAE,EAACnC,WAAW,CAAC4E,IAAI,CAAC6B,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFjD,OAAA;cAAA6C,QAAA,gBAAG7C,OAAA;gBAAA6C,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,IAAIgH,IAAI,CAACnJ,WAAW,CAACoJ,OAAO,CAAC,CAACC,cAAc,CAAC,CAAC;YAAA;cAAArH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChFnC,WAAW,CAACsJ,uBAAuB,iBAClCpK,OAAA;cAAA6C,QAAA,gBAAG7C,OAAA;gBAAA6C,QAAA,EAAQ;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,IAAIgH,IAAI,CAACnJ,WAAW,CAACsJ,uBAAuB,CAAC,CAACD,cAAc,CAAC,CAAC;YAAA;cAAArH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC9G;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eACbjD,OAAA,CAACtB,KAAK,CAACqL,MAAM;UAAAlH,QAAA,eACX7C,OAAA,CAACxB,MAAM;YAACiE,OAAO,EAAC,WAAW;YAACqE,OAAO,EAAEA,CAAA,KAAMnG,gBAAgB,CAAC,KAAK,CAAE;YAAAkC,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGRjD,OAAA,CAACtB,KAAK;QAAC+J,IAAI,EAAE7H,aAAc;QAAC8H,MAAM,EAAEA,CAAA,KAAM7H,gBAAgB,CAAC,KAAK,CAAE;QAACuG,IAAI,EAAC,IAAI;QAAAvE,QAAA,gBAC1E7C,OAAA,CAACtB,KAAK,CAACiK,MAAM;UAACC,WAAW;UAAA/F,QAAA,eACvB7C,OAAA,CAACtB,KAAK,CAACmK,KAAK;YAAAhG,QAAA,GAAC,SAAO,EAAC/B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiH,SAAS;UAAA;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACfjD,OAAA,CAACtB,KAAK,CAACoK,IAAI;UAACuB,KAAK,EAAE;YAAEC,MAAM,EAAE,OAAO;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAA1H,QAAA,eACxD7C,OAAA;YAAKgG,SAAS,EAAC,MAAM;YAAAnD,QAAA,EAClBzB,QAAQ,CAACuG,GAAG,CAAC,CAAC6C,OAAO,EAAE5C,KAAK,kBAC3B5H,OAAA;cAAiBgG,SAAS,EAAE,QAAQwE,OAAO,CAACC,aAAa,GAAG,UAAU,GAAG,YAAY,EAAG;cAAA5H,QAAA,gBACtF7C,OAAA;gBAAKgG,SAAS,EAAE,8BAA8BwE,OAAO,CAACC,aAAa,GAAG,uBAAuB,GAAG,UAAU,EAAG;gBAAA5H,QAAA,EAC1G2H,OAAO,CAACE;cAAO;gBAAA5H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACNjD,OAAA;gBAAKgG,SAAS,EAAC,kBAAkB;gBAAAnD,QAAA,EAC9B,IAAIoH,IAAI,CAACO,OAAO,CAACG,SAAS,CAAC,CAACR,cAAc,CAAC;cAAC;gBAAArH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA,GANE2E,KAAK;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACbjD,OAAA,CAACtB,KAAK,CAACqL,MAAM;UAAAlH,QAAA,eACX7C,OAAA,CAACrB,UAAU;YAAAkE,QAAA,gBACT7C,OAAA,CAACvB,IAAI,CAACwK,OAAO;cACXC,IAAI,EAAC,MAAM;cACXY,WAAW,EAAC,sBAAsB;cAClC5C,KAAK,EAAEhG,WAAY;cACnBkI,QAAQ,EAAGC,CAAC,IAAKlI,cAAc,CAACkI,CAAC,CAACE,MAAM,CAACrC,KAAK,CAAE;cAChD0D,UAAU,EAAGvB,CAAC,IAAKA,CAAC,CAACwB,GAAG,KAAK,OAAO,IAAI1F,iBAAiB,CAAC;YAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACFjD,OAAA,CAACxB,MAAM;cAACiE,OAAO,EAAC,SAAS;cAACqE,OAAO,EAAE3B,iBAAkB;cAAAtC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAC/C,EAAA,CAplBID,0BAAoC;EAAA,QACvBP,OAAO;AAAA;AAAAoL,EAAA,GADpB7K,0BAAoC;AAslB1C,eAAeA,0BAA0B;AAAC,IAAA6K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}