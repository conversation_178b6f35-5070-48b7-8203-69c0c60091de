{"ast": null, "code": "import { isCSSVar } from '../../render/dom/is-css-var.mjs';\nimport { transformProps } from '../../render/utils/keys-transform.mjs';\nimport { isHTMLElement } from '../../utils/is-html-element.mjs';\nimport { MotionValue } from '../../value/index.mjs';\nimport { createSelectorEffect } from '../utils/create-dom-effect.mjs';\nimport { createEffect } from '../utils/create-effect.mjs';\nimport { buildTransform } from './transform.mjs';\nconst originProps = new Set([\"originX\", \"originY\", \"originZ\"]);\nconst addStyleValue = (element, state, key, value) => {\n  let render = undefined;\n  let computed = undefined;\n  if (transformProps.has(key)) {\n    if (!state.get(\"transform\")) {\n      // If this is an HTML element, we need to set the transform-box to fill-box\n      // to normalise the transform relative to the element's bounding box\n      if (!isHTMLElement(element) && !state.get(\"transformBox\")) {\n        addStyleValue(element, state, \"transformBox\", new MotionValue(\"fill-box\"));\n      }\n      state.set(\"transform\", new MotionValue(\"none\"), () => {\n        element.style.transform = buildTransform(state);\n      });\n    }\n    computed = state.get(\"transform\");\n  } else if (originProps.has(key)) {\n    if (!state.get(\"transformOrigin\")) {\n      state.set(\"transformOrigin\", new MotionValue(\"\"), () => {\n        const originX = state.latest.originX ?? \"50%\";\n        const originY = state.latest.originY ?? \"50%\";\n        const originZ = state.latest.originZ ?? 0;\n        element.style.transformOrigin = `${originX} ${originY} ${originZ}`;\n      });\n    }\n    computed = state.get(\"transformOrigin\");\n  } else if (isCSSVar(key)) {\n    render = () => {\n      element.style.setProperty(key, state.latest[key]);\n    };\n  } else {\n    render = () => {\n      element.style[key] = state.latest[key];\n    };\n  }\n  return state.set(key, value, render, computed);\n};\nconst styleEffect = /*@__PURE__*/createSelectorEffect(/*@__PURE__*/createEffect(addStyleValue));\nexport { addStyleValue, styleEffect };", "map": {"version": 3, "names": ["isCSSVar", "transformProps", "isHTMLElement", "MotionValue", "createSelectorEffect", "createEffect", "buildTransform", "originProps", "Set", "addStyleValue", "element", "state", "key", "value", "render", "undefined", "computed", "has", "get", "set", "style", "transform", "originX", "latest", "originY", "originZ", "transform<PERSON><PERSON>in", "setProperty", "styleEffect"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/effects/style/index.mjs"], "sourcesContent": ["import { isCSSVar } from '../../render/dom/is-css-var.mjs';\nimport { transformProps } from '../../render/utils/keys-transform.mjs';\nimport { isHTMLElement } from '../../utils/is-html-element.mjs';\nimport { MotionValue } from '../../value/index.mjs';\nimport { createSelectorEffect } from '../utils/create-dom-effect.mjs';\nimport { createEffect } from '../utils/create-effect.mjs';\nimport { buildTransform } from './transform.mjs';\n\nconst originProps = new Set([\"originX\", \"originY\", \"originZ\"]);\nconst addStyleValue = (element, state, key, value) => {\n    let render = undefined;\n    let computed = undefined;\n    if (transformProps.has(key)) {\n        if (!state.get(\"transform\")) {\n            // If this is an HTML element, we need to set the transform-box to fill-box\n            // to normalise the transform relative to the element's bounding box\n            if (!isHTMLElement(element) && !state.get(\"transformBox\")) {\n                addStyleValue(element, state, \"transformBox\", new MotionValue(\"fill-box\"));\n            }\n            state.set(\"transform\", new MotionValue(\"none\"), () => {\n                element.style.transform = buildTransform(state);\n            });\n        }\n        computed = state.get(\"transform\");\n    }\n    else if (originProps.has(key)) {\n        if (!state.get(\"transformOrigin\")) {\n            state.set(\"transformOrigin\", new MotionValue(\"\"), () => {\n                const originX = state.latest.originX ?? \"50%\";\n                const originY = state.latest.originY ?? \"50%\";\n                const originZ = state.latest.originZ ?? 0;\n                element.style.transformOrigin = `${originX} ${originY} ${originZ}`;\n            });\n        }\n        computed = state.get(\"transformOrigin\");\n    }\n    else if (isCSSVar(key)) {\n        render = () => {\n            element.style.setProperty(key, state.latest[key]);\n        };\n    }\n    else {\n        render = () => {\n            element.style[key] = state.latest[key];\n        };\n    }\n    return state.set(key, value, render, computed);\n};\nconst styleEffect = /*@__PURE__*/ createSelectorEffect(\n/*@__PURE__*/ createEffect(addStyleValue));\n\nexport { addStyleValue, styleEffect };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iCAAiC;AAC1D,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,cAAc,QAAQ,iBAAiB;AAEhD,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AAC9D,MAAMC,aAAa,GAAGA,CAACC,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,KAAK;EAClD,IAAIC,MAAM,GAAGC,SAAS;EACtB,IAAIC,QAAQ,GAAGD,SAAS;EACxB,IAAId,cAAc,CAACgB,GAAG,CAACL,GAAG,CAAC,EAAE;IACzB,IAAI,CAACD,KAAK,CAACO,GAAG,CAAC,WAAW,CAAC,EAAE;MACzB;MACA;MACA,IAAI,CAAChB,aAAa,CAACQ,OAAO,CAAC,IAAI,CAACC,KAAK,CAACO,GAAG,CAAC,cAAc,CAAC,EAAE;QACvDT,aAAa,CAACC,OAAO,EAAEC,KAAK,EAAE,cAAc,EAAE,IAAIR,WAAW,CAAC,UAAU,CAAC,CAAC;MAC9E;MACAQ,KAAK,CAACQ,GAAG,CAAC,WAAW,EAAE,IAAIhB,WAAW,CAAC,MAAM,CAAC,EAAE,MAAM;QAClDO,OAAO,CAACU,KAAK,CAACC,SAAS,GAAGf,cAAc,CAACK,KAAK,CAAC;MACnD,CAAC,CAAC;IACN;IACAK,QAAQ,GAAGL,KAAK,CAACO,GAAG,CAAC,WAAW,CAAC;EACrC,CAAC,MACI,IAAIX,WAAW,CAACU,GAAG,CAACL,GAAG,CAAC,EAAE;IAC3B,IAAI,CAACD,KAAK,CAACO,GAAG,CAAC,iBAAiB,CAAC,EAAE;MAC/BP,KAAK,CAACQ,GAAG,CAAC,iBAAiB,EAAE,IAAIhB,WAAW,CAAC,EAAE,CAAC,EAAE,MAAM;QACpD,MAAMmB,OAAO,GAAGX,KAAK,CAACY,MAAM,CAACD,OAAO,IAAI,KAAK;QAC7C,MAAME,OAAO,GAAGb,KAAK,CAACY,MAAM,CAACC,OAAO,IAAI,KAAK;QAC7C,MAAMC,OAAO,GAAGd,KAAK,CAACY,MAAM,CAACE,OAAO,IAAI,CAAC;QACzCf,OAAO,CAACU,KAAK,CAACM,eAAe,GAAG,GAAGJ,OAAO,IAAIE,OAAO,IAAIC,OAAO,EAAE;MACtE,CAAC,CAAC;IACN;IACAT,QAAQ,GAAGL,KAAK,CAACO,GAAG,CAAC,iBAAiB,CAAC;EAC3C,CAAC,MACI,IAAIlB,QAAQ,CAACY,GAAG,CAAC,EAAE;IACpBE,MAAM,GAAGA,CAAA,KAAM;MACXJ,OAAO,CAACU,KAAK,CAACO,WAAW,CAACf,GAAG,EAAED,KAAK,CAACY,MAAM,CAACX,GAAG,CAAC,CAAC;IACrD,CAAC;EACL,CAAC,MACI;IACDE,MAAM,GAAGA,CAAA,KAAM;MACXJ,OAAO,CAACU,KAAK,CAACR,GAAG,CAAC,GAAGD,KAAK,CAACY,MAAM,CAACX,GAAG,CAAC;IAC1C,CAAC;EACL;EACA,OAAOD,KAAK,CAACQ,GAAG,CAACP,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAEE,QAAQ,CAAC;AAClD,CAAC;AACD,MAAMY,WAAW,GAAG,aAAcxB,oBAAoB,CACtD,aAAcC,YAAY,CAACI,aAAa,CAAC,CAAC;AAE1C,SAASA,aAAa,EAAEmB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}