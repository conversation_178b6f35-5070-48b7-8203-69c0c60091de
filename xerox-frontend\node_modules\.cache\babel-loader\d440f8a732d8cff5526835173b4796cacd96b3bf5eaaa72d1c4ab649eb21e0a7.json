{"ast": null, "code": "/**\n * Take an array of times that represent repeated keyframes. For instance\n * if we have original times of [0, 0.5, 1] then our repeated times will\n * be [0, 0.5, 1, 1, 1.5, 2]. Loop over the times and scale them back\n * down to a 0-1 scale.\n */\nfunction normalizeTimes(times, repeat) {\n  for (let i = 0; i < times.length; i++) {\n    times[i] = times[i] / (repeat + 1);\n  }\n}\nexport { normalizeTimes };", "map": {"version": 3, "names": ["normalizeTimes", "times", "repeat", "i", "length"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/animation/sequence/utils/normalize-times.mjs"], "sourcesContent": ["/**\n * Take an array of times that represent repeated keyframes. For instance\n * if we have original times of [0, 0.5, 1] then our repeated times will\n * be [0, 0.5, 1, 1, 1.5, 2]. Loop over the times and scale them back\n * down to a 0-1 scale.\n */\nfunction normalizeTimes(times, repeat) {\n    for (let i = 0; i < times.length; i++) {\n        times[i] = times[i] / (repeat + 1);\n    }\n}\n\nexport { normalizeTimes };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAcA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCF,KAAK,CAACE,CAAC,CAAC,GAAGF,KAAK,CAACE,CAAC,CAAC,IAAID,MAAM,GAAG,CAAC,CAAC;EACtC;AACJ;AAEA,SAASF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}