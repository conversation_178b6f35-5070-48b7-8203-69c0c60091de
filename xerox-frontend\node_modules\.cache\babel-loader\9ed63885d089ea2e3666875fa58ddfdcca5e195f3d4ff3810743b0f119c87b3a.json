{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\XeroxCenterDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, fileUploadApi, messageApi } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst XeroxCenterDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [quoteData, setQuoteData] = useState({\n    cost: '',\n    estimatedHours: '',\n    notes: ''\n  });\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [sortBy, setSortBy] = useState('created');\n  const [sortOrder, setSortOrder] = useState('desc');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [viewMode, setViewMode] = useState('table');\n  const [showJobDetailsModal, setShowJobDetailsModal] = useState(false);\n  const [refreshInterval, setRefreshInterval] = useState(null);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs for this xerox center\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error fetching print jobs:', error);\n        setPrintJobs([]);\n      }\n    };\n    fetchData();\n\n    // Set up auto-refresh every 30 seconds\n    const interval = setInterval(fetchData, 30000);\n    setRefreshInterval(interval);\n\n    // Cleanup interval on unmount\n    return () => {\n      if (refreshInterval) {\n        clearInterval(refreshInterval);\n      }\n      clearInterval(interval);\n    };\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        variant: 'secondary',\n        icon: 'clock'\n      },\n      'UnderReview': {\n        variant: 'info',\n        icon: 'eye'\n      },\n      'Quoted': {\n        variant: 'warning',\n        icon: 'dollar-sign'\n      },\n      'WaitingConfirmation': {\n        variant: 'warning',\n        icon: 'hourglass-half'\n      },\n      'Confirmed': {\n        variant: 'primary',\n        icon: 'check'\n      },\n      'InProgress': {\n        variant: 'info',\n        icon: 'cog'\n      },\n      'Completed': {\n        variant: 'success',\n        icon: 'check-circle'\n      },\n      'Delivered': {\n        variant: 'success',\n        icon: 'truck'\n      },\n      'Rejected': {\n        variant: 'danger',\n        icon: 'times'\n      },\n      'Cancelled': {\n        variant: 'dark',\n        icon: 'ban'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      icon: 'question'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `fas fa-${config.icon} me-1`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this);\n  };\n  const getPriorityBadge = (priority = 'Normal') => {\n    const priorityConfig = {\n      'Low': {\n        variant: 'light',\n        icon: 'arrow-down',\n        color: 'text-muted'\n      },\n      'Normal': {\n        variant: 'secondary',\n        icon: 'minus',\n        color: 'text-dark'\n      },\n      'High': {\n        variant: 'warning',\n        icon: 'arrow-up',\n        color: 'text-warning'\n      },\n      'Urgent': {\n        variant: 'danger',\n        icon: 'exclamation',\n        color: 'text-danger'\n      }\n    };\n    const config = priorityConfig[priority] || priorityConfig['Normal'];\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      className: config.color,\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `fas fa-${config.icon} me-1`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), priority]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this);\n  };\n  const getTimeAgo = dateString => {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n    return `${Math.floor(diffInMinutes / 1440)}d ago`;\n  };\n  const formatFileSize = (bytes = 0) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  const handleQuoteSubmit = async () => {\n    if (selectedJob) {\n      try {\n        const estimatedCompletion = new Date();\n        estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));\n        await printJobApi.setJobQuote(selectedJob.id, parseFloat(quoteData.cost), estimatedCompletion.toISOString(), quoteData.notes);\n\n        // Refresh print jobs after update\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error submitting quote:', error);\n      }\n    }\n    setShowQuoteModal(false);\n    setSelectedJob(null);\n    setQuoteData({\n      cost: '',\n      estimatedHours: '',\n      notes: ''\n    });\n  };\n  const handleStatusUpdate = async (jobId, newStatus) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n\n      // Refresh print jobs after update\n      const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error updating status:', error);\n    }\n  };\n  const handleDownloadFile = async (jobId, fileName) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n  const handleOpenChat = async job => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      // Load messages for this job\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n  const handleSendMessage = async () => {\n    if (!newMessage.trim() || !selectedJob) return;\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, newMessage.trim());\n\n      // Add the new message to the list\n      setMessages(prev => [...prev, response.data]);\n      setNewMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n  const getSortedAndFilteredJobs = () => {\n    let filtered = printJobs;\n\n    // Apply status filter\n    if (filterStatus !== 'all') {\n      filtered = filtered.filter(job => job.status === filterStatus);\n    }\n\n    // Apply search filter\n    if (searchTerm) {\n      const term = searchTerm.toLowerCase();\n      filtered = filtered.filter(job => job.jobNumber.toLowerCase().includes(term) || job.fileName.toLowerCase().includes(term) || job.studentName.toLowerCase().includes(term) || job.studentEmail.toLowerCase().includes(term) || job.printType.toLowerCase().includes(term));\n    }\n\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let aValue, bValue;\n      switch (sortBy) {\n        case 'created':\n          aValue = new Date(a.created).getTime();\n          bValue = new Date(b.created).getTime();\n          break;\n        case 'priority':\n          const priorityOrder = {\n            'Urgent': 4,\n            'High': 3,\n            'Normal': 2,\n            'Low': 1\n          };\n          aValue = priorityOrder[a.priority] || 2;\n          bValue = priorityOrder[b.priority] || 2;\n          break;\n        case 'status':\n          aValue = a.status;\n          bValue = b.status;\n          break;\n        case 'cost':\n          aValue = a.cost || 0;\n          bValue = b.cost || 0;\n          break;\n        default:\n          aValue = a.created;\n          bValue = b.created;\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    return filtered;\n  };\n  const filteredJobs = getSortedAndFilteredJobs();\n  const stats = {\n    total: printJobs.length,\n    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,\n    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,\n    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"job-queue-container\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-store me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), \"Xerox Center Dashboard\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted\",\n            children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"stats-card text-center border-left-primary\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-file-alt fa-2x text-primary mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Total Jobs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-primary\",\n                children: stats.total\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"stats-card text-center border-left-warning\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-clock fa-2x text-warning mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Pending\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-warning\",\n                children: stats.pending\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"stats-card text-center border-left-info\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-cog fa-2x text-info mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"In Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-info\",\n                children: stats.inProgress\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"stats-card text-center border-left-success\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-dollar-sign fa-2x text-success mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Revenue\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-success\",\n                children: [\"$\", stats.revenue.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            className: \"align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-tasks me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 17\n                }, this), \"Job Queue (\", filteredJobs.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                className: \"g-2\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 3,\n                  children: /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    placeholder: \"Search jobs...\",\n                    value: searchTerm,\n                    onChange: e => setSearchTerm(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 3,\n                  children: /*#__PURE__*/_jsxDEV(Form.Select, {\n                    size: \"sm\",\n                    value: filterStatus,\n                    onChange: e => setFilterStatus(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Requested\",\n                      children: \"Requested\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 406,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UnderReview\",\n                      children: \"Under Review\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Quoted\",\n                      children: \"Quoted\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"WaitingConfirmation\",\n                      children: \"Waiting Confirmation\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 409,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Confirmed\",\n                      children: \"Confirmed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"InProgress\",\n                      children: \"In Progress\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Completed\",\n                      children: \"Completed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 3,\n                  children: /*#__PURE__*/_jsxDEV(Form.Select, {\n                    size: \"sm\",\n                    value: sortBy,\n                    onChange: e => setSortBy(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"created\",\n                      children: \"Sort by Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"priority\",\n                      children: \"Sort by Priority\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 422,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"status\",\n                      children: \"Sort by Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"cost\",\n                      children: \"Sort by Cost\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 3,\n                  children: /*#__PURE__*/_jsxDEV(ButtonGroup, {\n                    size: \"sm\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: sortOrder === 'desc' ? 'primary' : 'outline-primary',\n                      onClick: () => setSortOrder('desc'),\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-sort-amount-down\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 433,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 429,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: sortOrder === 'asc' ? 'primary' : 'outline-primary',\n                      onClick: () => setSortOrder('asc'),\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-sort-amount-up\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 439,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: viewMode === 'table' ? 'primary' : 'outline-primary',\n                      onClick: () => setViewMode('table'),\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-table\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 445,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: viewMode === 'cards' ? 'primary' : 'outline-primary',\n                      onClick: () => setViewMode('cards'),\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-th-large\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 451,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: filteredJobs.length > 0 ? viewMode === 'table' ? /*#__PURE__*/_jsxDEV(Table, {\n            responsive: true,\n            hover: true,\n            className: \"job-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Job #\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Priority\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"File Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Print Specs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Cost\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: filteredJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: job.jobNumber\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: getTimeAgo(job.created)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: getPriorityBadge(job.priority)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: job.studentName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 491,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: job.studentEmail\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 493,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-file-pdf me-2 text-danger\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 498,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 499,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 500,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: job.fileSize ? formatFileSize(job.fileSize) : 'Unknown size'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"small\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Type:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 508,\n                        columnNumber: 32\n                      }, this), \" \", job.printType]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Copies:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 509,\n                        columnNumber: 32\n                      }, this), \" \", job.copies]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Color:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 510,\n                        columnNumber: 32\n                      }, this), \" \", job.colorType]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Size:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 511,\n                        columnNumber: 32\n                      }, this), \" \", job.paperSize]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: getStatusBadge(job.status)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"small\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Created:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 520,\n                        columnNumber: 32\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: new Date(job.created).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 521,\n                      columnNumber: 27\n                    }, this), job.estimatedCompletionTime && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"ETA:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 524,\n                          columnNumber: 36\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 524,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: new Date(job.estimatedCompletionTime).toLocaleDateString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 525,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"btn-group-vertical\",\n                    role: \"group\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-info\",\n                      size: \"sm\",\n                      onClick: () => handleDownloadFile(job.id, job.fileName),\n                      title: \"Download File\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-download me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 539,\n                        columnNumber: 27\n                      }, this), \"Download\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 533,\n                      columnNumber: 25\n                    }, this), job.status === 'Requested' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-primary\",\n                        size: \"sm\",\n                        onClick: () => {\n                          setSelectedJob(job);\n                          setShowQuoteModal(true);\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-dollar-sign me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 553,\n                          columnNumber: 31\n                        }, this), \"Quote\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 545,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleStatusUpdate(job.id, 'Rejected'),\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-times me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 561,\n                          columnNumber: 31\n                        }, this), \"Reject\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 556,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true), job.status === 'Confirmed' && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-info\",\n                      size: \"sm\",\n                      onClick: () => handleStatusUpdate(job.id, 'InProgress'),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-play me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 573,\n                        columnNumber: 29\n                      }, this), \"Start\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 27\n                    }, this), job.status === 'InProgress' && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-success\",\n                      size: \"sm\",\n                      onClick: () => handleStatusUpdate(job.id, 'Completed'),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-check me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 584,\n                        columnNumber: 29\n                      }, this), \"Complete\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 579,\n                      columnNumber: 27\n                    }, this), job.status === 'Completed' && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-success\",\n                      size: \"sm\",\n                      onClick: () => handleStatusUpdate(job.id, 'Delivered'),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-truck me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 29\n                      }, this), \"Deliver\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-secondary\",\n                      size: \"sm\",\n                      onClick: () => handleOpenChat(job),\n                      title: \"Chat\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-comment\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 606,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 21\n                }, this)]\n              }, job.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this) :\n          /*#__PURE__*/\n          // Card View\n          _jsxDEV(Row, {\n            children: filteredJobs.map(job => {\n              var _job$priority;\n              return /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                lg: 4,\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: `job-card h-100 shadow-sm ${job.priority === 'Urgent' ? 'priority-urgent' : ''} priority-${((_job$priority = job.priority) === null || _job$priority === void 0 ? void 0 : _job$priority.toLowerCase()) || 'normal'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                    className: \"d-flex justify-content-between align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: job.jobNumber\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 622,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 623,\n                        columnNumber: 27\n                      }, this), getPriorityBadge(job.priority)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 621,\n                      columnNumber: 25\n                    }, this), getStatusBadge(job.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: job.studentName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 631,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted\",\n                        children: job.studentEmail\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 632,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 629,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-file-pdf me-2 text-danger\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 635,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        children: job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 636,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 634,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-2 small\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Type:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 639,\n                          columnNumber: 32\n                        }, this), \" \", job.printType]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 639,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Copies:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 640,\n                          columnNumber: 32\n                        }, this), \" \", job.copies]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Color:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 641,\n                          columnNumber: 32\n                        }, this), \" \", job.colorType]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 641,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Size:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 642,\n                          columnNumber: 32\n                        }, this), \" \", job.paperSize]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 642,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 25\n                    }, this), job.cost && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-success\",\n                        children: [\"$\", job.cost.toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 646,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 645,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small text-muted\",\n                      children: getTimeAgo(job.created)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 628,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Card.Footer, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"action-buttons d-flex flex-wrap gap-1\",\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-info\",\n                        size: \"sm\",\n                        onClick: () => handleDownloadFile(job.id, job.fileName),\n                        title: \"Download File\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-download\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 661,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 655,\n                        columnNumber: 27\n                      }, this), job.status === 'Requested' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-primary\",\n                          size: \"sm\",\n                          onClick: () => {\n                            setSelectedJob(job);\n                            setShowQuoteModal(true);\n                          },\n                          title: \"Quote\",\n                          children: /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-dollar-sign\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 675,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 666,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-danger\",\n                          size: \"sm\",\n                          onClick: () => handleStatusUpdate(job.id, 'Rejected'),\n                          title: \"Reject\",\n                          children: /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-times\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 683,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 677,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true), job.status === 'Confirmed' && /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-info\",\n                        size: \"sm\",\n                        onClick: () => handleStatusUpdate(job.id, 'InProgress'),\n                        title: \"Start\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-play\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 695,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 689,\n                        columnNumber: 29\n                      }, this), job.status === 'InProgress' && /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-success\",\n                        size: \"sm\",\n                        onClick: () => handleStatusUpdate(job.id, 'Completed'),\n                        title: \"Complete\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-check\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 706,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 700,\n                        columnNumber: 29\n                      }, this), job.status === 'Completed' && /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-success\",\n                        size: \"sm\",\n                        onClick: () => handleStatusUpdate(job.id, 'Delivered'),\n                        title: \"Deliver\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-truck\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 717,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 711,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-secondary\",\n                        size: \"sm\",\n                        onClick: () => handleOpenChat(job),\n                        title: \"Chat\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-comment\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 727,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 721,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-dark\",\n                        size: \"sm\",\n                        onClick: () => {\n                          setSelectedJob(job);\n                          setShowJobDetailsModal(true);\n                        },\n                        title: \"View Details\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-eye\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 739,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 654,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 21\n                }, this)\n              }, job.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"info\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-info-circle me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 750,\n              columnNumber: 15\n            }, this), \"No jobs found for the selected filter.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showQuoteModal,\n        onHide: () => setShowQuoteModal(false),\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-dollar-sign me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 13\n            }, this), \"Provide Quote - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: selectedJob && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3 p-3 bg-light rounded\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Job Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"File:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.fileName, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 66\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Type:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 773,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.printType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 773,\n                    columnNumber: 67\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Copies:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 774,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.copies]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Color:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 777,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.colorType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 777,\n                    columnNumber: 68\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Size:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.paperSize, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 67\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Student:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.studentName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 17\n              }, this), selectedJob.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Remarks:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 784,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.remarks]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Cost ($)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 793,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n                      children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                        children: \"$\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 795,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"number\",\n                        step: \"0.01\",\n                        placeholder: \"0.00\",\n                        value: quoteData.cost,\n                        onChange: e => setQuoteData(prev => ({\n                          ...prev,\n                          cost: e.target.value\n                        })),\n                        required: true\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 796,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 794,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 792,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Estimated Hours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 809,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"number\",\n                      min: \"1\",\n                      placeholder: \"Hours to complete\",\n                      value: quoteData.estimatedHours,\n                      onChange: e => setQuoteData(prev => ({\n                        ...prev,\n                        estimatedHours: e.target.value\n                      })),\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 810,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 808,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Notes (Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 823,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  as: \"textarea\",\n                  rows: 3,\n                  placeholder: \"Any additional notes for the student...\",\n                  value: quoteData.notes,\n                  onChange: e => setQuoteData(prev => ({\n                    ...prev,\n                    notes: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowQuoteModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 837,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleQuoteSubmit,\n            disabled: !quoteData.cost || !quoteData.estimatedHours,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-paper-plane me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 13\n            }, this), \"Send Quote\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 836,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showChatModal,\n        onHide: () => setShowChatModal(false),\n        size: \"lg\",\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-comment me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 13\n            }, this), \"Chat - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 854,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 853,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: '400px',\n              overflowY: 'auto',\n              border: '1px solid #dee2e6',\n              borderRadius: '0.375rem',\n              padding: '1rem',\n              marginBottom: '1rem'\n            },\n            children: messages.length > 0 ? messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `mb-3 ${message.isFromCurrentUser ? 'text-end' : 'text-start'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `d-inline-block p-2 rounded ${message.isFromCurrentUser ? 'bg-primary text-white' : 'bg-light'}`,\n                style: {\n                  maxWidth: '70%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 865,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: `d-block mt-1 ${message.isFromCurrentUser ? 'text-light' : 'text-muted'}`,\n                  children: [message.senderName, \" - \", new Date(message.sentAt).toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 19\n              }, this)\n            }, message.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 863,\n              columnNumber: 17\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-muted\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-comments fa-3x mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No messages yet. Start a conversation!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              placeholder: \"Type your message...\",\n              value: newMessage,\n              onChange: e => setNewMessage(e.target.value),\n              onKeyDown: e => e.key === 'Enter' && handleSendMessage(),\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              onClick: handleSendMessage,\n              disabled: !newMessage.trim(),\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-paper-plane\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 889,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 859,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowChatModal(false),\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 894,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 893,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 852,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showJobDetailsModal,\n        onHide: () => setShowJobDetailsModal(false),\n        size: \"lg\",\n        className: \"job-details-modal\",\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-info-circle me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 13\n            }, this), \"Job Details - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 902,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: selectedJob && /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-user me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 915,\n                      columnNumber: 23\n                    }, this), \"Student Information\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 913,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Name:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 920,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.studentName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 920,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Email:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 921,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.studentEmail]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 921,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 912,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-file me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 928,\n                      columnNumber: 23\n                    }, this), \"File Information\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 927,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 926,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"File Name:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 933,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.fileName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 933,\n                    columnNumber: 21\n                  }, this), selectedJob.fileSize && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"File Size:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 935,\n                      columnNumber: 28\n                    }, this), \" \", formatFileSize(selectedJob.fileSize)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 935,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      onClick: () => handleDownloadFile(selectedJob.id, selectedJob.fileName),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-download me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 943,\n                        columnNumber: 25\n                      }, this), \"Download File\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 938,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 932,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 925,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-print me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 955,\n                      columnNumber: 23\n                    }, this), \"Print Specifications\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 954,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Print Type:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 960,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.printType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 960,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Copies:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 961,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.copies]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 961,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Color Type:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 962,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.colorType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 962,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Paper Size:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 963,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.paperSize]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 963,\n                    columnNumber: 21\n                  }, this), selectedJob.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Remarks:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 966,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 bg-light rounded mt-1\",\n                      children: selectedJob.remarks\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 967,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 965,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-info me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 978,\n                      columnNumber: 23\n                    }, this), \"Job Status\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 977,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 976,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Status:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 984,\n                      columnNumber: 23\n                    }, this), \" \", getStatusBadge(selectedJob.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 983,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Priority:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 987,\n                      columnNumber: 23\n                    }, this), \" \", getPriorityBadge(selectedJob.priority)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 21\n                  }, this), selectedJob.cost && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Cost:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 991,\n                      columnNumber: 25\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-success\",\n                      children: [\"$\", selectedJob.cost.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 991,\n                      columnNumber: 48\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 990,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Created:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 995,\n                      columnNumber: 23\n                    }, this), \" \", new Date(selectedJob.created).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 994,\n                    columnNumber: 21\n                  }, this), selectedJob.estimatedCompletionTime && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Estimated Completion:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 999,\n                      columnNumber: 25\n                    }, this), \" \", new Date(selectedJob.estimatedCompletionTime).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 998,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowJobDetailsModal(false),\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1009,\n            columnNumber: 11\n          }, this), selectedJob && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: () => {\n              setShowJobDetailsModal(false);\n              handleOpenChat(selectedJob);\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-comment me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1020,\n              columnNumber: 15\n            }, this), \"Start Chat\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1013,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1008,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 901,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 326,\n    columnNumber: 5\n  }, this);\n};\n_s(XeroxCenterDashboard, \"yOBp+pTRgzl3M3JWyhOKa/OV4y4=\", false, function () {\n  return [useAuth];\n});\n_c = XeroxCenterDashboard;\nexport default XeroxCenterDashboard;\nvar _c;\n$RefreshReg$(_c, \"XeroxCenterDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "printJobApi", "fileUploadApi", "messageApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "XeroxCenterDashboard", "_s", "user", "printJobs", "setPrintJobs", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "showQuoteModal", "setShowQuoteModal", "quoteData", "setQuoteData", "cost", "estimatedHours", "notes", "filterStatus", "setFilterStatus", "showChatModal", "setShowChatModal", "messages", "setMessages", "newMessage", "setNewMessage", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "searchTerm", "setSearchTerm", "viewMode", "setViewMode", "showJobDetailsModal", "setShowJobDetailsModal", "refreshInterval", "setRefreshInterval", "fetchData", "printJobsResponse", "getXeroxCenterJobs", "data", "error", "console", "interval", "setInterval", "clearInterval", "getStatusBadge", "status", "statusConfig", "variant", "icon", "config", "Badge", "bg", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getPriorityBadge", "priority", "priorityConfig", "color", "getTimeAgo", "dateString", "now", "Date", "date", "diffInMinutes", "Math", "floor", "getTime", "formatFileSize", "bytes", "k", "sizes", "i", "log", "parseFloat", "pow", "toFixed", "handleQuoteSubmit", "estimatedCompletion", "setHours", "getHours", "parseInt", "setJobQuote", "id", "toISOString", "handleStatusUpdate", "jobId", "newStatus", "updateJobStatus", "handleDownloadFile", "response", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleOpenChat", "job", "getJobMessages", "handleSendMessage", "trim", "sendMessage", "prev", "getSortedAndFilteredJobs", "filtered", "filter", "term", "toLowerCase", "jobNumber", "includes", "studentName", "studentEmail", "printType", "sort", "a", "b", "aValue", "bValue", "created", "priorityOrder", "filteredJobs", "stats", "total", "length", "pending", "inProgress", "completed", "revenue", "reduce", "sum", "Container", "fluid", "Row", "Col", "username", "md", "Card", "Body", "Header", "Form", "Control", "type", "placeholder", "value", "onChange", "e", "target", "size", "Select", "ButtonGroup", "<PERSON><PERSON>", "onClick", "Table", "responsive", "hover", "map", "slice", "fileSize", "copies", "colorType", "paperSize", "toLocaleDateString", "estimatedCompletionTime", "role", "title", "_job$priority", "lg", "Footer", "<PERSON><PERSON>", "Modal", "show", "onHide", "closeButton", "Title", "remarks", "Group", "Label", "InputGroup", "Text", "step", "required", "min", "as", "rows", "disabled", "style", "height", "overflowY", "border", "borderRadius", "padding", "marginBottom", "message", "isFromCurrentUser", "max<PERSON><PERSON><PERSON>", "content", "sender<PERSON>ame", "sentAt", "toLocaleString", "onKeyDown", "key", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/XeroxCenterDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  BarChart3,\n  FileText,\n  Clock,\n  CheckCircle,\n  DollarSign,\n  Download,\n  MessageCircle,\n  Info,\n  Check,\n  Star,\n  MapPin,\n  TrendingUp,\n  Activity,\n  Users,\n  Printer,\n  Eye,\n  Send,\n  X,\n  Plus,\n  Filter,\n  Search,\n  Calendar,\n  Settings,\n  AlertTriangle,\n  ArrowUp,\n  ArrowDown,\n  Minus,\n  ExternalLink,\n  RefreshCw,\n  Grid,\n  List,\n  SortAsc,\n  SortDesc\n} from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../services/api';\nimport { cn } from '../lib/utils';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  studentName: string;\n  studentEmail: string;\n  printType: string;\n  copies: number;\n  colorType: string;\n  paperSize: string;\n  remarks?: string;\n  created: string;\n  priority?: 'Low' | 'Normal' | 'High' | 'Urgent';\n  fileSize?: number;\n  estimatedDuration?: number;\n}\n\nconst XeroxCenterDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [selectedJob, setSelectedJob] = useState<PrintJob | null>(null);\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [quoteData, setQuoteData] = useState({\n    cost: '',\n    estimatedHours: '',\n    notes: ''\n  });\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [messages, setMessages] = useState<any[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [sortBy, setSortBy] = useState<'created' | 'priority' | 'status' | 'cost'>('created');\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');\n  const [showJobDetailsModal, setShowJobDetailsModal] = useState(false);\n  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs for this xerox center\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error fetching print jobs:', error);\n        setPrintJobs([]);\n      }\n    };\n\n    fetchData();\n\n    // Set up auto-refresh every 30 seconds\n    const interval = setInterval(fetchData, 30000);\n    setRefreshInterval(interval);\n\n    // Cleanup interval on unmount\n    return () => {\n      if (refreshInterval) {\n        clearInterval(refreshInterval);\n      }\n      clearInterval(interval);\n    };\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { variant: 'secondary', icon: 'clock' },\n      'UnderReview': { variant: 'info', icon: 'eye' },\n      'Quoted': { variant: 'warning', icon: 'dollar-sign' },\n      'WaitingConfirmation': { variant: 'warning', icon: 'hourglass-half' },\n      'Confirmed': { variant: 'primary', icon: 'check' },\n      'InProgress': { variant: 'info', icon: 'cog' },\n      'Completed': { variant: 'success', icon: 'check-circle' },\n      'Delivered': { variant: 'success', icon: 'truck' },\n      'Rejected': { variant: 'danger', icon: 'times' },\n      'Cancelled': { variant: 'dark', icon: 'ban' }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary', icon: 'question' };\n\n    return (\n      <Badge bg={config.variant}>\n        <i className={`fas fa-${config.icon} me-1`}></i>\n        {status}\n      </Badge>\n    );\n  };\n\n  const getPriorityBadge = (priority: string = 'Normal') => {\n    const priorityConfig = {\n      'Low': { variant: 'light', icon: 'arrow-down', color: 'text-muted' },\n      'Normal': { variant: 'secondary', icon: 'minus', color: 'text-dark' },\n      'High': { variant: 'warning', icon: 'arrow-up', color: 'text-warning' },\n      'Urgent': { variant: 'danger', icon: 'exclamation', color: 'text-danger' }\n    };\n\n    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig['Normal'];\n\n    return (\n      <Badge bg={config.variant} className={config.color}>\n        <i className={`fas fa-${config.icon} me-1`}></i>\n        {priority}\n      </Badge>\n    );\n  };\n\n  const getTimeAgo = (dateString: string) => {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n    return `${Math.floor(diffInMinutes / 1440)}d ago`;\n  };\n\n  const formatFileSize = (bytes: number = 0) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const handleQuoteSubmit = async () => {\n    if (selectedJob) {\n      try {\n        const estimatedCompletion = new Date();\n        estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));\n\n        await printJobApi.setJobQuote(\n          selectedJob.id,\n          parseFloat(quoteData.cost),\n          estimatedCompletion.toISOString(),\n          quoteData.notes\n        );\n\n        // Refresh print jobs after update\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error submitting quote:', error);\n      }\n    }\n\n    setShowQuoteModal(false);\n    setSelectedJob(null);\n    setQuoteData({ cost: '', estimatedHours: '', notes: '' });\n  };\n\n  const handleStatusUpdate = async (jobId: number, newStatus: string) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n\n      // Refresh print jobs after update\n      const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error updating status:', error);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  const handleOpenChat = async (job: PrintJob) => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      // Load messages for this job\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!newMessage.trim() || !selectedJob) return;\n\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, newMessage.trim());\n\n      // Add the new message to the list\n      setMessages(prev => [...prev, response.data]);\n      setNewMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  const getSortedAndFilteredJobs = () => {\n    let filtered = printJobs;\n\n    // Apply status filter\n    if (filterStatus !== 'all') {\n      filtered = filtered.filter(job => job.status === filterStatus);\n    }\n\n    // Apply search filter\n    if (searchTerm) {\n      const term = searchTerm.toLowerCase();\n      filtered = filtered.filter(job =>\n        job.jobNumber.toLowerCase().includes(term) ||\n        job.fileName.toLowerCase().includes(term) ||\n        job.studentName.toLowerCase().includes(term) ||\n        job.studentEmail.toLowerCase().includes(term) ||\n        job.printType.toLowerCase().includes(term)\n      );\n    }\n\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let aValue: any, bValue: any;\n\n      switch (sortBy) {\n        case 'created':\n          aValue = new Date(a.created).getTime();\n          bValue = new Date(b.created).getTime();\n          break;\n        case 'priority':\n          const priorityOrder = { 'Urgent': 4, 'High': 3, 'Normal': 2, 'Low': 1 };\n          aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 2;\n          bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 2;\n          break;\n        case 'status':\n          aValue = a.status;\n          bValue = b.status;\n          break;\n        case 'cost':\n          aValue = a.cost || 0;\n          bValue = b.cost || 0;\n          break;\n        default:\n          aValue = a.created;\n          bValue = b.created;\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    return filtered;\n  };\n\n  const filteredJobs = getSortedAndFilteredJobs();\n\n  const stats = {\n    total: printJobs.length,\n    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,\n    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,\n    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)\n  };\n\n  return (\n    <div className=\"job-queue-container\">\n      <Container fluid>\n        <Row className=\"mb-4\">\n          <Col>\n            <h2>\n              <i className=\"fas fa-store me-2\"></i>\n              Xerox Center Dashboard\n            </h2>\n            <p className=\"text-muted\">Welcome back, {user?.username}!</p>\n          </Col>\n        </Row>\n\n        {/* Statistics Cards */}\n        <Row className=\"mb-4\">\n          <Col md={3}>\n            <Card className=\"stats-card text-center border-left-primary\">\n              <Card.Body>\n                <i className=\"fas fa-file-alt fa-2x text-primary mb-2\"></i>\n                <h5>Total Jobs</h5>\n                <h3 className=\"text-primary\">{stats.total}</h3>\n              </Card.Body>\n            </Card>\n          </Col>\n          <Col md={3}>\n            <Card className=\"stats-card text-center border-left-warning\">\n              <Card.Body>\n                <i className=\"fas fa-clock fa-2x text-warning mb-2\"></i>\n                <h5>Pending</h5>\n                <h3 className=\"text-warning\">{stats.pending}</h3>\n              </Card.Body>\n            </Card>\n          </Col>\n          <Col md={3}>\n            <Card className=\"stats-card text-center border-left-info\">\n              <Card.Body>\n                <i className=\"fas fa-cog fa-2x text-info mb-2\"></i>\n                <h5>In Progress</h5>\n                <h3 className=\"text-info\">{stats.inProgress}</h3>\n              </Card.Body>\n            </Card>\n          </Col>\n          <Col md={3}>\n            <Card className=\"stats-card text-center border-left-success\">\n              <Card.Body>\n                <i className=\"fas fa-dollar-sign fa-2x text-success mb-2\"></i>\n                <h5>Revenue</h5>\n                <h3 className=\"text-success\">${stats.revenue.toFixed(2)}</h3>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n\n      {/* Job Queue */}\n      <Card>\n        <Card.Header>\n          <Row className=\"align-items-center\">\n            <Col md={4}>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-tasks me-2\"></i>\n                Job Queue ({filteredJobs.length})\n              </h5>\n            </Col>\n            <Col md={8}>\n              <Row className=\"g-2\">\n                <Col md={3}>\n                  <Form.Control\n                    type=\"text\"\n                    placeholder=\"Search jobs...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    size=\"sm\"\n                  />\n                </Col>\n                <Col md={3}>\n                  <Form.Select\n                    size=\"sm\"\n                    value={filterStatus}\n                    onChange={(e) => setFilterStatus(e.target.value)}\n                  >\n                    <option value=\"all\">All Status</option>\n                    <option value=\"Requested\">Requested</option>\n                    <option value=\"UnderReview\">Under Review</option>\n                    <option value=\"Quoted\">Quoted</option>\n                    <option value=\"WaitingConfirmation\">Waiting Confirmation</option>\n                    <option value=\"Confirmed\">Confirmed</option>\n                    <option value=\"InProgress\">In Progress</option>\n                    <option value=\"Completed\">Completed</option>\n                  </Form.Select>\n                </Col>\n                <Col md={3}>\n                  <Form.Select\n                    size=\"sm\"\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value as any)}\n                  >\n                    <option value=\"created\">Sort by Date</option>\n                    <option value=\"priority\">Sort by Priority</option>\n                    <option value=\"status\">Sort by Status</option>\n                    <option value=\"cost\">Sort by Cost</option>\n                  </Form.Select>\n                </Col>\n                <Col md={3}>\n                  <ButtonGroup size=\"sm\">\n                    <Button\n                      variant={sortOrder === 'desc' ? 'primary' : 'outline-primary'}\n                      onClick={() => setSortOrder('desc')}\n                    >\n                      <i className=\"fas fa-sort-amount-down\"></i>\n                    </Button>\n                    <Button\n                      variant={sortOrder === 'asc' ? 'primary' : 'outline-primary'}\n                      onClick={() => setSortOrder('asc')}\n                    >\n                      <i className=\"fas fa-sort-amount-up\"></i>\n                    </Button>\n                    <Button\n                      variant={viewMode === 'table' ? 'primary' : 'outline-primary'}\n                      onClick={() => setViewMode('table')}\n                    >\n                      <i className=\"fas fa-table\"></i>\n                    </Button>\n                    <Button\n                      variant={viewMode === 'cards' ? 'primary' : 'outline-primary'}\n                      onClick={() => setViewMode('cards')}\n                    >\n                      <i className=\"fas fa-th-large\"></i>\n                    </Button>\n                  </ButtonGroup>\n                </Col>\n              </Row>\n            </Col>\n          </Row>\n        </Card.Header>\n        <Card.Body>\n          {filteredJobs.length > 0 ? (\n            viewMode === 'table' ? (\n              <Table responsive hover className=\"job-table\">\n                <thead>\n                  <tr>\n                    <th>Job #</th>\n                    <th>Priority</th>\n                    <th>Student</th>\n                    <th>File Details</th>\n                    <th>Print Specs</th>\n                    <th>Status</th>\n                    <th>Cost</th>\n                    <th>Time</th>\n                    <th>Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {filteredJobs.map(job => (\n                    <tr key={job.id}>\n                      <td>\n                        <strong>{job.jobNumber}</strong>\n                        <br />\n                        <small className=\"text-muted\">\n                          {getTimeAgo(job.created)}\n                        </small>\n                      </td>\n                      <td>\n                        {getPriorityBadge(job.priority)}\n                      </td>\n                      <td>\n                        <div>\n                          <strong>{job.studentName}</strong>\n                          <br />\n                          <small className=\"text-muted\">{job.studentEmail}</small>\n                        </div>\n                      </td>\n                      <td>\n                        <div>\n                          <i className=\"fas fa-file-pdf me-2 text-danger\"></i>\n                          <strong>{job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName}</strong>\n                          <br />\n                          <small className=\"text-muted\">\n                            {job.fileSize ? formatFileSize(job.fileSize) : 'Unknown size'}\n                          </small>\n                        </div>\n                      </td>\n                      <td>\n                        <div className=\"small\">\n                          <div><strong>Type:</strong> {job.printType}</div>\n                          <div><strong>Copies:</strong> {job.copies}</div>\n                          <div><strong>Color:</strong> {job.colorType}</div>\n                          <div><strong>Size:</strong> {job.paperSize}</div>\n                        </div>\n                      </td>\n                      <td>{getStatusBadge(job.status)}</td>\n                      <td>\n                        {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                      </td>\n                      <td>\n                        <div className=\"small\">\n                          <div><strong>Created:</strong></div>\n                          <div>{new Date(job.created).toLocaleDateString()}</div>\n                          {job.estimatedCompletionTime && (\n                            <>\n                              <div><strong>ETA:</strong></div>\n                              <div>{new Date(job.estimatedCompletionTime).toLocaleDateString()}</div>\n                            </>\n                          )}\n                        </div>\n                      </td>\n                    <td>\n                      <div className=\"btn-group-vertical\" role=\"group\">\n                        {/* Download button - always available */}\n                        <Button\n                          variant=\"outline-info\"\n                          size=\"sm\"\n                          onClick={() => handleDownloadFile(job.id, job.fileName)}\n                          title=\"Download File\"\n                        >\n                          <i className=\"fas fa-download me-1\"></i>\n                          Download\n                        </Button>\n\n                        {job.status === 'Requested' && (\n                          <>\n                            <Button\n                              variant=\"outline-primary\"\n                              size=\"sm\"\n                              onClick={() => {\n                                setSelectedJob(job);\n                                setShowQuoteModal(true);\n                              }}\n                            >\n                              <i className=\"fas fa-dollar-sign me-1\"></i>\n                              Quote\n                            </Button>\n                            <Button\n                              variant=\"outline-danger\"\n                              size=\"sm\"\n                              onClick={() => handleStatusUpdate(job.id, 'Rejected')}\n                            >\n                              <i className=\"fas fa-times me-1\"></i>\n                              Reject\n                            </Button>\n                          </>\n                        )}\n\n                        {job.status === 'Confirmed' && (\n                          <Button\n                            variant=\"outline-info\"\n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'InProgress')}\n                          >\n                            <i className=\"fas fa-play me-1\"></i>\n                            Start\n                          </Button>\n                        )}\n\n                        {job.status === 'InProgress' && (\n                          <Button\n                            variant=\"outline-success\"\n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'Completed')}\n                          >\n                            <i className=\"fas fa-check me-1\"></i>\n                            Complete\n                          </Button>\n                        )}\n\n                        {job.status === 'Completed' && (\n                          <Button\n                            variant=\"outline-success\"\n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'Delivered')}\n                          >\n                            <i className=\"fas fa-truck me-1\"></i>\n                            Deliver\n                          </Button>\n                        )}\n\n                        <Button\n                          variant=\"outline-secondary\"\n                          size=\"sm\"\n                          onClick={() => handleOpenChat(job)}\n                          title=\"Chat\"\n                        >\n                          <i className=\"fas fa-comment\"></i>\n                        </Button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n              </Table>\n            ) : (\n              // Card View\n              <Row>\n                {filteredJobs.map(job => (\n                  <Col key={job.id} md={6} lg={4} className=\"mb-3\">\n                    <Card className={`job-card h-100 shadow-sm ${job.priority === 'Urgent' ? 'priority-urgent' : ''} priority-${job.priority?.toLowerCase() || 'normal'}`}>\n                      <Card.Header className=\"d-flex justify-content-between align-items-center\">\n                        <div>\n                          <strong>{job.jobNumber}</strong>\n                          <br />\n                          {getPriorityBadge(job.priority)}\n                        </div>\n                        {getStatusBadge(job.status)}\n                      </Card.Header>\n                      <Card.Body>\n                        <div className=\"mb-2\">\n                          <strong>{job.studentName}</strong>\n                          <br />\n                          <small className=\"text-muted\">{job.studentEmail}</small>\n                        </div>\n                        <div className=\"mb-2\">\n                          <i className=\"fas fa-file-pdf me-2 text-danger\"></i>\n                          <small>{job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName}</small>\n                        </div>\n                        <div className=\"mb-2 small\">\n                          <div><strong>Type:</strong> {job.printType}</div>\n                          <div><strong>Copies:</strong> {job.copies}</div>\n                          <div><strong>Color:</strong> {job.colorType}</div>\n                          <div><strong>Size:</strong> {job.paperSize}</div>\n                        </div>\n                        {job.cost && (\n                          <div className=\"mb-2\">\n                            <strong className=\"text-success\">${job.cost.toFixed(2)}</strong>\n                          </div>\n                        )}\n                        <div className=\"small text-muted\">\n                          {getTimeAgo(job.created)}\n                        </div>\n                      </Card.Body>\n                      <Card.Footer>\n                        <div className=\"action-buttons d-flex flex-wrap gap-1\">\n                          <Button\n                            variant=\"outline-info\"\n                            size=\"sm\"\n                            onClick={() => handleDownloadFile(job.id, job.fileName)}\n                            title=\"Download File\"\n                          >\n                            <i className=\"fas fa-download\"></i>\n                          </Button>\n\n                          {job.status === 'Requested' && (\n                            <>\n                              <Button\n                                variant=\"outline-primary\"\n                                size=\"sm\"\n                                onClick={() => {\n                                  setSelectedJob(job);\n                                  setShowQuoteModal(true);\n                                }}\n                                title=\"Quote\"\n                              >\n                                <i className=\"fas fa-dollar-sign\"></i>\n                              </Button>\n                              <Button\n                                variant=\"outline-danger\"\n                                size=\"sm\"\n                                onClick={() => handleStatusUpdate(job.id, 'Rejected')}\n                                title=\"Reject\"\n                              >\n                                <i className=\"fas fa-times\"></i>\n                              </Button>\n                            </>\n                          )}\n\n                          {job.status === 'Confirmed' && (\n                            <Button\n                              variant=\"outline-info\"\n                              size=\"sm\"\n                              onClick={() => handleStatusUpdate(job.id, 'InProgress')}\n                              title=\"Start\"\n                            >\n                              <i className=\"fas fa-play\"></i>\n                            </Button>\n                          )}\n\n                          {job.status === 'InProgress' && (\n                            <Button\n                              variant=\"outline-success\"\n                              size=\"sm\"\n                              onClick={() => handleStatusUpdate(job.id, 'Completed')}\n                              title=\"Complete\"\n                            >\n                              <i className=\"fas fa-check\"></i>\n                            </Button>\n                          )}\n\n                          {job.status === 'Completed' && (\n                            <Button\n                              variant=\"outline-success\"\n                              size=\"sm\"\n                              onClick={() => handleStatusUpdate(job.id, 'Delivered')}\n                              title=\"Deliver\"\n                            >\n                              <i className=\"fas fa-truck\"></i>\n                            </Button>\n                          )}\n\n                          <Button\n                            variant=\"outline-secondary\"\n                            size=\"sm\"\n                            onClick={() => handleOpenChat(job)}\n                            title=\"Chat\"\n                          >\n                            <i className=\"fas fa-comment\"></i>\n                          </Button>\n\n                          <Button\n                            variant=\"outline-dark\"\n                            size=\"sm\"\n                            onClick={() => {\n                              setSelectedJob(job);\n                              setShowJobDetailsModal(true);\n                            }}\n                            title=\"View Details\"\n                          >\n                            <i className=\"fas fa-eye\"></i>\n                          </Button>\n                        </div>\n                      </Card.Footer>\n                    </Card>\n                  </Col>\n                ))}\n              </Row>\n            )\n          ) : (\n            <Alert variant=\"info\">\n              <i className=\"fas fa-info-circle me-2\"></i>\n              No jobs found for the selected filter.\n            </Alert>\n          )}\n        </Card.Body>\n      </Card>\n\n      {/* Quote Modal */}\n      <Modal show={showQuoteModal} onHide={() => setShowQuoteModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-dollar-sign me-2\"></i>\n            Provide Quote - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedJob && (\n            <>\n              <div className=\"mb-3 p-3 bg-light rounded\">\n                <h6>Job Details:</h6>\n                <div className=\"row\">\n                  <div className=\"col-6\">\n                    <strong>File:</strong> {selectedJob.fileName}<br />\n                    <strong>Type:</strong> {selectedJob.printType}<br />\n                    <strong>Copies:</strong> {selectedJob.copies}\n                  </div>\n                  <div className=\"col-6\">\n                    <strong>Color:</strong> {selectedJob.colorType}<br />\n                    <strong>Size:</strong> {selectedJob.paperSize}<br />\n                    <strong>Student:</strong> {selectedJob.studentName}\n                  </div>\n                </div>\n                {selectedJob.remarks && (\n                  <div className=\"mt-2\">\n                    <strong>Remarks:</strong> {selectedJob.remarks}\n                  </div>\n                )}\n              </div>\n\n              <Form>\n                <Row>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Cost ($)</Form.Label>\n                      <InputGroup>\n                        <InputGroup.Text>$</InputGroup.Text>\n                        <Form.Control\n                          type=\"number\"\n                          step=\"0.01\"\n                          placeholder=\"0.00\"\n                          value={quoteData.cost}\n                          onChange={(e) => setQuoteData(prev => ({ ...prev, cost: e.target.value }))}\n                          required\n                        />\n                      </InputGroup>\n                    </Form.Group>\n                  </Col>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Estimated Hours</Form.Label>\n                      <Form.Control\n                        type=\"number\"\n                        min=\"1\"\n                        placeholder=\"Hours to complete\"\n                        value={quoteData.estimatedHours}\n                        onChange={(e) => setQuoteData(prev => ({ ...prev, estimatedHours: e.target.value }))}\n                        required\n                      />\n                    </Form.Group>\n                  </Col>\n                </Row>\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Notes (Optional)</Form.Label>\n                  <Form.Control\n                    as=\"textarea\"\n                    rows={3}\n                    placeholder=\"Any additional notes for the student...\"\n                    value={quoteData.notes}\n                    onChange={(e) => setQuoteData(prev => ({ ...prev, notes: e.target.value }))}\n                  />\n                </Form.Group>\n              </Form>\n            </>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowQuoteModal(false)}>\n            Cancel\n          </Button>\n          <Button \n            variant=\"primary\" \n            onClick={handleQuoteSubmit}\n            disabled={!quoteData.cost || !quoteData.estimatedHours}\n          >\n            <i className=\"fas fa-paper-plane me-2\"></i>\n            Send Quote\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* Chat Modal */}\n      <Modal show={showChatModal} onHide={() => setShowChatModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-comment me-2\"></i>\n            Chat - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <div style={{ height: '400px', overflowY: 'auto', border: '1px solid #dee2e6', borderRadius: '0.375rem', padding: '1rem', marginBottom: '1rem' }}>\n            {messages.length > 0 ? (\n              messages.map((message) => (\n                <div key={message.id} className={`mb-3 ${message.isFromCurrentUser ? 'text-end' : 'text-start'}`}>\n                  <div className={`d-inline-block p-2 rounded ${message.isFromCurrentUser ? 'bg-primary text-white' : 'bg-light'}`} style={{ maxWidth: '70%' }}>\n                    <div>{message.content}</div>\n                    <small className={`d-block mt-1 ${message.isFromCurrentUser ? 'text-light' : 'text-muted'}`}>\n                      {message.senderName} - {new Date(message.sentAt).toLocaleString()}\n                    </small>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"text-center text-muted\">\n                <i className=\"fas fa-comments fa-3x mb-3\"></i>\n                <p>No messages yet. Start a conversation!</p>\n              </div>\n            )}\n          </div>\n          <div className=\"d-flex\">\n            <Form.Control\n              type=\"text\"\n              placeholder=\"Type your message...\"\n              value={newMessage}\n              onChange={(e) => setNewMessage(e.target.value)}\n              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}\n              className=\"me-2\"\n            />\n            <Button variant=\"primary\" onClick={handleSendMessage} disabled={!newMessage.trim()}>\n              <i className=\"fas fa-paper-plane\"></i>\n            </Button>\n          </div>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowChatModal(false)}>\n            Close\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* Job Details Modal */}\n      <Modal show={showJobDetailsModal} onHide={() => setShowJobDetailsModal(false)} size=\"lg\" className=\"job-details-modal\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-info-circle me-2\"></i>\n            Job Details - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedJob && (\n            <Row>\n              <Col md={6}>\n                <Card className=\"mb-3\">\n                  <Card.Header>\n                    <h6 className=\"mb-0\">\n                      <i className=\"fas fa-user me-2\"></i>\n                      Student Information\n                    </h6>\n                  </Card.Header>\n                  <Card.Body>\n                    <div><strong>Name:</strong> {selectedJob.studentName}</div>\n                    <div><strong>Email:</strong> {selectedJob.studentEmail}</div>\n                  </Card.Body>\n                </Card>\n\n                <Card className=\"mb-3\">\n                  <Card.Header>\n                    <h6 className=\"mb-0\">\n                      <i className=\"fas fa-file me-2\"></i>\n                      File Information\n                    </h6>\n                  </Card.Header>\n                  <Card.Body>\n                    <div><strong>File Name:</strong> {selectedJob.fileName}</div>\n                    {selectedJob.fileSize && (\n                      <div><strong>File Size:</strong> {formatFileSize(selectedJob.fileSize)}</div>\n                    )}\n                    <div className=\"mt-2\">\n                      <Button\n                        variant=\"outline-primary\"\n                        size=\"sm\"\n                        onClick={() => handleDownloadFile(selectedJob.id, selectedJob.fileName)}\n                      >\n                        <i className=\"fas fa-download me-2\"></i>\n                        Download File\n                      </Button>\n                    </div>\n                  </Card.Body>\n                </Card>\n              </Col>\n\n              <Col md={6}>\n                <Card className=\"mb-3\">\n                  <Card.Header>\n                    <h6 className=\"mb-0\">\n                      <i className=\"fas fa-print me-2\"></i>\n                      Print Specifications\n                    </h6>\n                  </Card.Header>\n                  <Card.Body>\n                    <div><strong>Print Type:</strong> {selectedJob.printType}</div>\n                    <div><strong>Copies:</strong> {selectedJob.copies}</div>\n                    <div><strong>Color Type:</strong> {selectedJob.colorType}</div>\n                    <div><strong>Paper Size:</strong> {selectedJob.paperSize}</div>\n                    {selectedJob.remarks && (\n                      <div className=\"mt-2\">\n                        <strong>Remarks:</strong>\n                        <div className=\"p-2 bg-light rounded mt-1\">\n                          {selectedJob.remarks}\n                        </div>\n                      </div>\n                    )}\n                  </Card.Body>\n                </Card>\n\n                <Card className=\"mb-3\">\n                  <Card.Header>\n                    <h6 className=\"mb-0\">\n                      <i className=\"fas fa-info me-2\"></i>\n                      Job Status\n                    </h6>\n                  </Card.Header>\n                  <Card.Body>\n                    <div className=\"mb-2\">\n                      <strong>Status:</strong> {getStatusBadge(selectedJob.status)}\n                    </div>\n                    <div className=\"mb-2\">\n                      <strong>Priority:</strong> {getPriorityBadge(selectedJob.priority)}\n                    </div>\n                    {selectedJob.cost && (\n                      <div className=\"mb-2\">\n                        <strong>Cost:</strong> <span className=\"text-success\">${selectedJob.cost.toFixed(2)}</span>\n                      </div>\n                    )}\n                    <div className=\"mb-2\">\n                      <strong>Created:</strong> {new Date(selectedJob.created).toLocaleString()}\n                    </div>\n                    {selectedJob.estimatedCompletionTime && (\n                      <div>\n                        <strong>Estimated Completion:</strong> {new Date(selectedJob.estimatedCompletionTime).toLocaleString()}\n                      </div>\n                    )}\n                  </Card.Body>\n                </Card>\n              </Col>\n            </Row>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowJobDetailsModal(false)}>\n            Close\n          </Button>\n          {selectedJob && (\n            <Button\n              variant=\"primary\"\n              onClick={() => {\n                setShowJobDetailsModal(false);\n                handleOpenChat(selectedJob);\n              }}\n            >\n              <i className=\"fas fa-comment me-2\"></i>\n              Start Chat\n            </Button>\n          )}\n        </Modal.Footer>\n      </Modal>\n      </Container>\n    </div>\n  );\n};\n\nexport default XeroxCenterDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAqClD,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,SAASC,WAAW,EAAkBC,aAAa,EAAEC,UAAU,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAuBzF,MAAMC,oBAA8B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3C,MAAM;IAAEC;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAkB,IAAI,CAAC;EACrE,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC;IACzCqB,IAAI,EAAE,EAAE;IACRC,cAAc,EAAE,EAAE;IAClBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgC,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAA6C,SAAS,CAAC;EAC3F,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAiB,MAAM,CAAC;EAClE,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAoB,OAAO,CAAC;EACpE,MAAM,CAACwC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC0C,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAwB,IAAI,CAAC;EAEnFC,SAAS,CAAC,MAAM;IACd,MAAM2C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,iBAAiB,GAAG,MAAM1C,WAAW,CAAC2C,kBAAkB,CAAC,CAAC;QAChEhC,YAAY,CAAC+B,iBAAiB,CAACE,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDlC,YAAY,CAAC,EAAE,CAAC;MAClB;IACF,CAAC;IAED8B,SAAS,CAAC,CAAC;;IAEX;IACA,MAAMM,QAAQ,GAAGC,WAAW,CAACP,SAAS,EAAE,KAAK,CAAC;IAC9CD,kBAAkB,CAACO,QAAQ,CAAC;;IAE5B;IACA,OAAO,MAAM;MACX,IAAIR,eAAe,EAAE;QACnBU,aAAa,CAACV,eAAe,CAAC;MAChC;MACAU,aAAa,CAACF,QAAQ,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACpD,aAAa,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC/C,QAAQ,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAc,CAAC;MACrD,qBAAqB,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAiB,CAAC;MACrE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,YAAY,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC9C,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAe,CAAC;MACzD,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,UAAU,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAChD,WAAW,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC9C,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAAEE,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAW,CAAC;IAE9G,oBACElD,OAAA,CAACoD,KAAK;MAACC,EAAE,EAAEF,MAAM,CAACF,OAAQ;MAAAK,QAAA,gBACxBtD,OAAA;QAAGuD,SAAS,EAAE,UAAUJ,MAAM,CAACD,IAAI;MAAQ;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC/CZ,MAAM;IAAA;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACC,QAAgB,GAAG,QAAQ,KAAK;IACxD,MAAMC,cAAc,GAAG;MACrB,KAAK,EAAE;QAAEb,OAAO,EAAE,OAAO;QAAEC,IAAI,EAAE,YAAY;QAAEa,KAAK,EAAE;MAAa,CAAC;MACpE,QAAQ,EAAE;QAAEd,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE,OAAO;QAAEa,KAAK,EAAE;MAAY,CAAC;MACrE,MAAM,EAAE;QAAEd,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE,UAAU;QAAEa,KAAK,EAAE;MAAe,CAAC;MACvE,QAAQ,EAAE;QAAEd,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE,aAAa;QAAEa,KAAK,EAAE;MAAc;IAC3E,CAAC;IAED,MAAMZ,MAAM,GAAGW,cAAc,CAACD,QAAQ,CAAgC,IAAIC,cAAc,CAAC,QAAQ,CAAC;IAElG,oBACE9D,OAAA,CAACoD,KAAK;MAACC,EAAE,EAAEF,MAAM,CAACF,OAAQ;MAACM,SAAS,EAAEJ,MAAM,CAACY,KAAM;MAAAT,QAAA,gBACjDtD,OAAA;QAAGuD,SAAS,EAAE,UAAUJ,MAAM,CAACD,IAAI;MAAQ;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC/CE,QAAQ;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEZ,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC,GAAGJ,IAAI,CAACI,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,UAAU;IACxC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,OAAO;IACtD,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC,OAAO;IACzE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC,OAAO;EACnD,CAAC;EAED,MAAMI,cAAc,GAAGA,CAACC,KAAa,GAAG,CAAC,KAAK;IAC5C,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGP,IAAI,CAACC,KAAK,CAACD,IAAI,CAACQ,GAAG,CAACJ,KAAK,CAAC,GAAGJ,IAAI,CAACQ,GAAG,CAACH,CAAC,CAAC,CAAC;IACnD,OAAOI,UAAU,CAAC,CAACL,KAAK,GAAGJ,IAAI,CAACU,GAAG,CAACL,CAAC,EAAEE,CAAC,CAAC,EAAEI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGL,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI1E,WAAW,EAAE;MACf,IAAI;QACF,MAAM2E,mBAAmB,GAAG,IAAIhB,IAAI,CAAC,CAAC;QACtCgB,mBAAmB,CAACC,QAAQ,CAACD,mBAAmB,CAACE,QAAQ,CAAC,CAAC,GAAGC,QAAQ,CAAC1E,SAAS,CAACG,cAAc,CAAC,CAAC;QAEjG,MAAMnB,WAAW,CAAC2F,WAAW,CAC3B/E,WAAW,CAACgF,EAAE,EACdT,UAAU,CAACnE,SAAS,CAACE,IAAI,CAAC,EAC1BqE,mBAAmB,CAACM,WAAW,CAAC,CAAC,EACjC7E,SAAS,CAACI,KACZ,CAAC;;QAED;QACA,MAAMsB,iBAAiB,GAAG,MAAM1C,WAAW,CAAC2C,kBAAkB,CAAC,CAAC;QAChEhC,YAAY,CAAC+B,iBAAiB,CAACE,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IACF;IAEA9B,iBAAiB,CAAC,KAAK,CAAC;IACxBF,cAAc,CAAC,IAAI,CAAC;IACpBI,YAAY,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,cAAc,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;EAC3D,CAAC;EAED,MAAM0E,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAEC,SAAiB,KAAK;IACrE,IAAI;MACF,MAAMhG,WAAW,CAACiG,eAAe,CAACF,KAAK,EAAEC,SAAS,CAAC;;MAEnD;MACA,MAAMtD,iBAAiB,GAAG,MAAM1C,WAAW,CAAC2C,kBAAkB,CAAC,CAAC;MAChEhC,YAAY,CAAC+B,iBAAiB,CAACE,IAAI,CAAC;IACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMqD,kBAAkB,GAAG,MAAAA,CAAOH,KAAa,EAAEnC,QAAgB,KAAK;IACpE,IAAI;MACF,MAAMuC,QAAQ,GAAG,MAAMlG,aAAa,CAACmG,YAAY,CAACL,KAAK,CAAC;;MAExD;MACA,MAAMM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,QAAQ,CAACvD,IAAI,CAAC,CAAC;MACtC,MAAM2D,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAGnD,QAAQ;MACxBgD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;IACF;EACF,CAAC;EAED,MAAMwE,cAAc,GAAG,MAAOC,GAAa,IAAK;IAC9C,IAAI;MACFzG,cAAc,CAACyG,GAAG,CAAC;MACnB9F,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAM2E,QAAQ,GAAG,MAAMjG,UAAU,CAACqH,cAAc,CAACD,GAAG,CAAC1B,EAAE,CAAC;MACxDlE,WAAW,CAACyE,QAAQ,CAACvD,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CnB,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;EAED,MAAM8F,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC7F,UAAU,CAAC8F,IAAI,CAAC,CAAC,IAAI,CAAC7G,WAAW,EAAE;IAExC,IAAI;MACF,MAAMuF,QAAQ,GAAG,MAAMjG,UAAU,CAACwH,WAAW,CAAC9G,WAAW,CAACgF,EAAE,EAAEjE,UAAU,CAAC8F,IAAI,CAAC,CAAC,CAAC;;MAEhF;MACA/F,WAAW,CAACiG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAExB,QAAQ,CAACvD,IAAI,CAAC,CAAC;MAC7ChB,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAM+E,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIC,QAAQ,GAAGnH,SAAS;;IAExB;IACA,IAAIW,YAAY,KAAK,KAAK,EAAE;MAC1BwG,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACR,GAAG,IAAIA,GAAG,CAACnE,MAAM,KAAK9B,YAAY,CAAC;IAChE;;IAEA;IACA,IAAIY,UAAU,EAAE;MACd,MAAM8F,IAAI,GAAG9F,UAAU,CAAC+F,WAAW,CAAC,CAAC;MACrCH,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACR,GAAG,IAC5BA,GAAG,CAACW,SAAS,CAACD,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAAC,IAC1CT,GAAG,CAAC1D,QAAQ,CAACoE,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAAC,IACzCT,GAAG,CAACa,WAAW,CAACH,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAAC,IAC5CT,GAAG,CAACc,YAAY,CAACJ,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAAC,IAC7CT,GAAG,CAACe,SAAS,CAACL,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAC3C,CAAC;IACH;;IAEA;IACAF,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAW,EAAEC,MAAW;MAE5B,QAAQ7G,MAAM;QACZ,KAAK,SAAS;UACZ4G,MAAM,GAAG,IAAIlE,IAAI,CAACgE,CAAC,CAACI,OAAO,CAAC,CAAC/D,OAAO,CAAC,CAAC;UACtC8D,MAAM,GAAG,IAAInE,IAAI,CAACiE,CAAC,CAACG,OAAO,CAAC,CAAC/D,OAAO,CAAC,CAAC;UACtC;QACF,KAAK,UAAU;UACb,MAAMgE,aAAa,GAAG;YAAE,QAAQ,EAAE,CAAC;YAAE,MAAM,EAAE,CAAC;YAAE,QAAQ,EAAE,CAAC;YAAE,KAAK,EAAE;UAAE,CAAC;UACvEH,MAAM,GAAGG,aAAa,CAACL,CAAC,CAACtE,QAAQ,CAA+B,IAAI,CAAC;UACrEyE,MAAM,GAAGE,aAAa,CAACJ,CAAC,CAACvE,QAAQ,CAA+B,IAAI,CAAC;UACrE;QACF,KAAK,QAAQ;UACXwE,MAAM,GAAGF,CAAC,CAACpF,MAAM;UACjBuF,MAAM,GAAGF,CAAC,CAACrF,MAAM;UACjB;QACF,KAAK,MAAM;UACTsF,MAAM,GAAGF,CAAC,CAACrH,IAAI,IAAI,CAAC;UACpBwH,MAAM,GAAGF,CAAC,CAACtH,IAAI,IAAI,CAAC;UACpB;QACF;UACEuH,MAAM,GAAGF,CAAC,CAACI,OAAO;UAClBD,MAAM,GAAGF,CAAC,CAACG,OAAO;MACtB;MAEA,IAAI5G,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO0G,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEF,OAAOb,QAAQ;EACjB,CAAC;EAED,MAAMgB,YAAY,GAAGjB,wBAAwB,CAAC,CAAC;EAE/C,MAAMkB,KAAK,GAAG;IACZC,KAAK,EAAErI,SAAS,CAACsI,MAAM;IACvBC,OAAO,EAAEvI,SAAS,CAACoH,MAAM,CAACR,GAAG,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAACY,QAAQ,CAACZ,GAAG,CAACnE,MAAM,CAAC,CAAC,CAAC6F,MAAM;IAC3HE,UAAU,EAAExI,SAAS,CAACoH,MAAM,CAACR,GAAG,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAACY,QAAQ,CAACZ,GAAG,CAACnE,MAAM,CAAC,CAAC,CAAC6F,MAAM;IAC5FG,SAAS,EAAEzI,SAAS,CAACoH,MAAM,CAACR,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACY,QAAQ,CAACZ,GAAG,CAACnE,MAAM,CAAC,CAAC,CAAC6F,MAAM;IAC1FI,OAAO,EAAE1I,SAAS,CAAC2I,MAAM,CAAC,CAACC,GAAG,EAAEhC,GAAG,KAAKgC,GAAG,IAAIhC,GAAG,CAACpG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;EAClE,CAAC;EAED,oBACEd,OAAA;IAAKuD,SAAS,EAAC,qBAAqB;IAAAD,QAAA,eAClCtD,OAAA,CAACmJ,SAAS;MAACC,KAAK;MAAA9F,QAAA,gBACdtD,OAAA,CAACqJ,GAAG;QAAC9F,SAAS,EAAC,MAAM;QAAAD,QAAA,eACnBtD,OAAA,CAACsJ,GAAG;UAAAhG,QAAA,gBACFtD,OAAA;YAAAsD,QAAA,gBACEtD,OAAA;cAAGuD,SAAS,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,0BAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3D,OAAA;YAAGuD,SAAS,EAAC,YAAY;YAAAD,QAAA,GAAC,gBAAc,EAACjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkJ,QAAQ,EAAC,GAAC;UAAA;YAAA/F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3D,OAAA,CAACqJ,GAAG;QAAC9F,SAAS,EAAC,MAAM;QAAAD,QAAA,gBACnBtD,OAAA,CAACsJ,GAAG;UAACE,EAAE,EAAE,CAAE;UAAAlG,QAAA,eACTtD,OAAA,CAACyJ,IAAI;YAAClG,SAAS,EAAC,4CAA4C;YAAAD,QAAA,eAC1DtD,OAAA,CAACyJ,IAAI,CAACC,IAAI;cAAApG,QAAA,gBACRtD,OAAA;gBAAGuD,SAAS,EAAC;cAAyC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3D3D,OAAA;gBAAAsD,QAAA,EAAI;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB3D,OAAA;gBAAIuD,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAEoF,KAAK,CAACC;cAAK;gBAAAnF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3D,OAAA,CAACsJ,GAAG;UAACE,EAAE,EAAE,CAAE;UAAAlG,QAAA,eACTtD,OAAA,CAACyJ,IAAI;YAAClG,SAAS,EAAC,4CAA4C;YAAAD,QAAA,eAC1DtD,OAAA,CAACyJ,IAAI,CAACC,IAAI;cAAApG,QAAA,gBACRtD,OAAA;gBAAGuD,SAAS,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxD3D,OAAA;gBAAAsD,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB3D,OAAA;gBAAIuD,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAEoF,KAAK,CAACG;cAAO;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3D,OAAA,CAACsJ,GAAG;UAACE,EAAE,EAAE,CAAE;UAAAlG,QAAA,eACTtD,OAAA,CAACyJ,IAAI;YAAClG,SAAS,EAAC,yCAAyC;YAAAD,QAAA,eACvDtD,OAAA,CAACyJ,IAAI,CAACC,IAAI;cAAApG,QAAA,gBACRtD,OAAA;gBAAGuD,SAAS,EAAC;cAAiC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnD3D,OAAA;gBAAAsD,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB3D,OAAA;gBAAIuD,SAAS,EAAC,WAAW;gBAAAD,QAAA,EAAEoF,KAAK,CAACI;cAAU;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3D,OAAA,CAACsJ,GAAG;UAACE,EAAE,EAAE,CAAE;UAAAlG,QAAA,eACTtD,OAAA,CAACyJ,IAAI;YAAClG,SAAS,EAAC,4CAA4C;YAAAD,QAAA,eAC1DtD,OAAA,CAACyJ,IAAI,CAACC,IAAI;cAAApG,QAAA,gBACRtD,OAAA;gBAAGuD,SAAS,EAAC;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9D3D,OAAA;gBAAAsD,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB3D,OAAA;gBAAIuD,SAAS,EAAC,cAAc;gBAAAD,QAAA,GAAC,GAAC,EAACoF,KAAK,CAACM,OAAO,CAAC/D,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGR3D,OAAA,CAACyJ,IAAI;QAAAnG,QAAA,gBACHtD,OAAA,CAACyJ,IAAI,CAACE,MAAM;UAAArG,QAAA,eACVtD,OAAA,CAACqJ,GAAG;YAAC9F,SAAS,EAAC,oBAAoB;YAAAD,QAAA,gBACjCtD,OAAA,CAACsJ,GAAG;cAACE,EAAE,EAAE,CAAE;cAAAlG,QAAA,eACTtD,OAAA;gBAAIuD,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAClBtD,OAAA;kBAAGuD,SAAS,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1B,EAAC8E,YAAY,CAACG,MAAM,EAAC,GAClC;cAAA;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACN3D,OAAA,CAACsJ,GAAG;cAACE,EAAE,EAAE,CAAE;cAAAlG,QAAA,eACTtD,OAAA,CAACqJ,GAAG;gBAAC9F,SAAS,EAAC,KAAK;gBAAAD,QAAA,gBAClBtD,OAAA,CAACsJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAlG,QAAA,eACTtD,OAAA,CAAC4J,IAAI,CAACC,OAAO;oBACXC,IAAI,EAAC,MAAM;oBACXC,WAAW,EAAC,gBAAgB;oBAC5BC,KAAK,EAAEnI,UAAW;oBAClBoI,QAAQ,EAAGC,CAAC,IAAKpI,aAAa,CAACoI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC/CI,IAAI,EAAC;kBAAI;oBAAA5G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN3D,OAAA,CAACsJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAlG,QAAA,eACTtD,OAAA,CAAC4J,IAAI,CAACS,MAAM;oBACVD,IAAI,EAAC,IAAI;oBACTJ,KAAK,EAAE/I,YAAa;oBACpBgJ,QAAQ,EAAGC,CAAC,IAAKhJ,eAAe,CAACgJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAAA1G,QAAA,gBAEjDtD,OAAA;sBAAQgK,KAAK,EAAC,KAAK;sBAAA1G,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC3D,OAAA;sBAAQgK,KAAK,EAAC,WAAW;sBAAA1G,QAAA,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5C3D,OAAA;sBAAQgK,KAAK,EAAC,aAAa;sBAAA1G,QAAA,EAAC;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjD3D,OAAA;sBAAQgK,KAAK,EAAC,QAAQ;sBAAA1G,QAAA,EAAC;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC3D,OAAA;sBAAQgK,KAAK,EAAC,qBAAqB;sBAAA1G,QAAA,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjE3D,OAAA;sBAAQgK,KAAK,EAAC,WAAW;sBAAA1G,QAAA,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5C3D,OAAA;sBAAQgK,KAAK,EAAC,YAAY;sBAAA1G,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC/C3D,OAAA;sBAAQgK,KAAK,EAAC,WAAW;sBAAA1G,QAAA,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACN3D,OAAA,CAACsJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAlG,QAAA,eACTtD,OAAA,CAAC4J,IAAI,CAACS,MAAM;oBACVD,IAAI,EAAC,IAAI;oBACTJ,KAAK,EAAEvI,MAAO;oBACdwI,QAAQ,EAAGC,CAAC,IAAKxI,SAAS,CAACwI,CAAC,CAACC,MAAM,CAACH,KAAY,CAAE;oBAAA1G,QAAA,gBAElDtD,OAAA;sBAAQgK,KAAK,EAAC,SAAS;sBAAA1G,QAAA,EAAC;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC7C3D,OAAA;sBAAQgK,KAAK,EAAC,UAAU;sBAAA1G,QAAA,EAAC;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClD3D,OAAA;sBAAQgK,KAAK,EAAC,QAAQ;sBAAA1G,QAAA,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9C3D,OAAA;sBAAQgK,KAAK,EAAC,MAAM;sBAAA1G,QAAA,EAAC;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACN3D,OAAA,CAACsJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAlG,QAAA,eACTtD,OAAA,CAACsK,WAAW;oBAACF,IAAI,EAAC,IAAI;oBAAA9G,QAAA,gBACpBtD,OAAA,CAACuK,MAAM;sBACLtH,OAAO,EAAEtB,SAAS,KAAK,MAAM,GAAG,SAAS,GAAG,iBAAkB;sBAC9D6I,OAAO,EAAEA,CAAA,KAAM5I,YAAY,CAAC,MAAM,CAAE;sBAAA0B,QAAA,eAEpCtD,OAAA;wBAAGuD,SAAS,EAAC;sBAAyB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC,eACT3D,OAAA,CAACuK,MAAM;sBACLtH,OAAO,EAAEtB,SAAS,KAAK,KAAK,GAAG,SAAS,GAAG,iBAAkB;sBAC7D6I,OAAO,EAAEA,CAAA,KAAM5I,YAAY,CAAC,KAAK,CAAE;sBAAA0B,QAAA,eAEnCtD,OAAA;wBAAGuD,SAAS,EAAC;sBAAuB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACT3D,OAAA,CAACuK,MAAM;sBACLtH,OAAO,EAAElB,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,iBAAkB;sBAC9DyI,OAAO,EAAEA,CAAA,KAAMxI,WAAW,CAAC,OAAO,CAAE;sBAAAsB,QAAA,eAEpCtD,OAAA;wBAAGuD,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC,eACT3D,OAAA,CAACuK,MAAM;sBACLtH,OAAO,EAAElB,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,iBAAkB;sBAC9DyI,OAAO,EAAEA,CAAA,KAAMxI,WAAW,CAAC,OAAO,CAAE;sBAAAsB,QAAA,eAEpCtD,OAAA;wBAAGuD,SAAS,EAAC;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACd3D,OAAA,CAACyJ,IAAI,CAACC,IAAI;UAAApG,QAAA,EACPmF,YAAY,CAACG,MAAM,GAAG,CAAC,GACtB7G,QAAQ,KAAK,OAAO,gBAClB/B,OAAA,CAACyK,KAAK;YAACC,UAAU;YAACC,KAAK;YAACpH,SAAS,EAAC,WAAW;YAAAD,QAAA,gBAC3CtD,OAAA;cAAAsD,QAAA,eACEtD,OAAA;gBAAAsD,QAAA,gBACEtD,OAAA;kBAAAsD,QAAA,EAAI;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd3D,OAAA;kBAAAsD,QAAA,EAAI;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjB3D,OAAA;kBAAAsD,QAAA,EAAI;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChB3D,OAAA;kBAAAsD,QAAA,EAAI;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrB3D,OAAA;kBAAAsD,QAAA,EAAI;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpB3D,OAAA;kBAAAsD,QAAA,EAAI;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACf3D,OAAA;kBAAAsD,QAAA,EAAI;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb3D,OAAA;kBAAAsD,QAAA,EAAI;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb3D,OAAA;kBAAAsD,QAAA,EAAI;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR3D,OAAA;cAAAsD,QAAA,EACGmF,YAAY,CAACmC,GAAG,CAAC1D,GAAG,iBACnBlH,OAAA;gBAAAsD,QAAA,gBACEtD,OAAA;kBAAAsD,QAAA,gBACEtD,OAAA;oBAAAsD,QAAA,EAAS4D,GAAG,CAACW;kBAAS;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eAChC3D,OAAA;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN3D,OAAA;oBAAOuD,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAC1BU,UAAU,CAACkD,GAAG,CAACqB,OAAO;kBAAC;oBAAA/E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACL3D,OAAA;kBAAAsD,QAAA,EACGM,gBAAgB,CAACsD,GAAG,CAACrD,QAAQ;gBAAC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACL3D,OAAA;kBAAAsD,QAAA,eACEtD,OAAA;oBAAAsD,QAAA,gBACEtD,OAAA;sBAAAsD,QAAA,EAAS4D,GAAG,CAACa;oBAAW;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eAClC3D,OAAA;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN3D,OAAA;sBAAOuD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAE4D,GAAG,CAACc;oBAAY;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACL3D,OAAA;kBAAAsD,QAAA,eACEtD,OAAA;oBAAAsD,QAAA,gBACEtD,OAAA;sBAAGuD,SAAS,EAAC;oBAAkC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpD3D,OAAA;sBAAAsD,QAAA,EAAS4D,GAAG,CAAC1D,QAAQ,CAACoF,MAAM,GAAG,EAAE,GAAG1B,GAAG,CAAC1D,QAAQ,CAACqH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG3D,GAAG,CAAC1D;oBAAQ;sBAAAA,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eAC9F3D,OAAA;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN3D,OAAA;sBAAOuD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAC1B4D,GAAG,CAAC4D,QAAQ,GAAGrG,cAAc,CAACyC,GAAG,CAAC4D,QAAQ,CAAC,GAAG;oBAAc;sBAAAtH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACL3D,OAAA;kBAAAsD,QAAA,eACEtD,OAAA;oBAAKuD,SAAS,EAAC,OAAO;oBAAAD,QAAA,gBACpBtD,OAAA;sBAAAsD,QAAA,gBAAKtD,OAAA;wBAAAsD,QAAA,EAAQ;sBAAK;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACuD,GAAG,CAACe,SAAS;oBAAA;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjD3D,OAAA;sBAAAsD,QAAA,gBAAKtD,OAAA;wBAAAsD,QAAA,EAAQ;sBAAO;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACuD,GAAG,CAAC6D,MAAM;oBAAA;sBAAAvH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChD3D,OAAA;sBAAAsD,QAAA,gBAAKtD,OAAA;wBAAAsD,QAAA,EAAQ;sBAAM;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACuD,GAAG,CAAC8D,SAAS;oBAAA;sBAAAxH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClD3D,OAAA;sBAAAsD,QAAA,gBAAKtD,OAAA;wBAAAsD,QAAA,EAAQ;sBAAK;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACuD,GAAG,CAAC+D,SAAS;oBAAA;sBAAAzH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACL3D,OAAA;kBAAAsD,QAAA,EAAKR,cAAc,CAACoE,GAAG,CAACnE,MAAM;gBAAC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrC3D,OAAA;kBAAAsD,QAAA,EACG4D,GAAG,CAACpG,IAAI,GAAG,IAAIoG,GAAG,CAACpG,IAAI,CAACmE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;gBAAG;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACL3D,OAAA;kBAAAsD,QAAA,eACEtD,OAAA;oBAAKuD,SAAS,EAAC,OAAO;oBAAAD,QAAA,gBACpBtD,OAAA;sBAAAsD,QAAA,eAAKtD,OAAA;wBAAAsD,QAAA,EAAQ;sBAAQ;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpC3D,OAAA;sBAAAsD,QAAA,EAAM,IAAIa,IAAI,CAAC+C,GAAG,CAACqB,OAAO,CAAC,CAAC2C,kBAAkB,CAAC;oBAAC;sBAAA1H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EACtDuD,GAAG,CAACiE,uBAAuB,iBAC1BnL,OAAA,CAAAE,SAAA;sBAAAoD,QAAA,gBACEtD,OAAA;wBAAAsD,QAAA,eAAKtD,OAAA;0BAAAsD,QAAA,EAAQ;wBAAI;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChC3D,OAAA;wBAAAsD,QAAA,EAAM,IAAIa,IAAI,CAAC+C,GAAG,CAACiE,uBAAuB,CAAC,CAACD,kBAAkB,CAAC;sBAAC;wBAAA1H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,eACvE,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACP3D,OAAA;kBAAAsD,QAAA,eACEtD,OAAA;oBAAKuD,SAAS,EAAC,oBAAoB;oBAAC6H,IAAI,EAAC,OAAO;oBAAA9H,QAAA,gBAE9CtD,OAAA,CAACuK,MAAM;sBACLtH,OAAO,EAAC,cAAc;sBACtBmH,IAAI,EAAC,IAAI;sBACTI,OAAO,EAAEA,CAAA,KAAM1E,kBAAkB,CAACoB,GAAG,CAAC1B,EAAE,EAAE0B,GAAG,CAAC1D,QAAQ,CAAE;sBACxD6H,KAAK,EAAC,eAAe;sBAAA/H,QAAA,gBAErBtD,OAAA;wBAAGuD,SAAS,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,YAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAERuD,GAAG,CAACnE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAAAE,SAAA;sBAAAoD,QAAA,gBACEtD,OAAA,CAACuK,MAAM;wBACLtH,OAAO,EAAC,iBAAiB;wBACzBmH,IAAI,EAAC,IAAI;wBACTI,OAAO,EAAEA,CAAA,KAAM;0BACb/J,cAAc,CAACyG,GAAG,CAAC;0BACnBvG,iBAAiB,CAAC,IAAI,CAAC;wBACzB,CAAE;wBAAA2C,QAAA,gBAEFtD,OAAA;0BAAGuD,SAAS,EAAC;wBAAyB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,SAE7C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT3D,OAAA,CAACuK,MAAM;wBACLtH,OAAO,EAAC,gBAAgB;wBACxBmH,IAAI,EAAC,IAAI;wBACTI,OAAO,EAAEA,CAAA,KAAM9E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,UAAU,CAAE;wBAAAlC,QAAA,gBAEtDtD,OAAA;0BAAGuD,SAAS,EAAC;wBAAmB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,UAEvC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH,EAEAuD,GAAG,CAACnE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAACuK,MAAM;sBACLtH,OAAO,EAAC,cAAc;sBACtBmH,IAAI,EAAC,IAAI;sBACTI,OAAO,EAAEA,CAAA,KAAM9E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,YAAY,CAAE;sBAAAlC,QAAA,gBAExDtD,OAAA;wBAAGuD,SAAS,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,SAEtC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT,EAEAuD,GAAG,CAACnE,MAAM,KAAK,YAAY,iBAC1B/C,OAAA,CAACuK,MAAM;sBACLtH,OAAO,EAAC,iBAAiB;sBACzBmH,IAAI,EAAC,IAAI;sBACTI,OAAO,EAAEA,CAAA,KAAM9E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,WAAW,CAAE;sBAAAlC,QAAA,gBAEvDtD,OAAA;wBAAGuD,SAAS,EAAC;sBAAmB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,YAEvC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT,EAEAuD,GAAG,CAACnE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAACuK,MAAM;sBACLtH,OAAO,EAAC,iBAAiB;sBACzBmH,IAAI,EAAC,IAAI;sBACTI,OAAO,EAAEA,CAAA,KAAM9E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,WAAW,CAAE;sBAAAlC,QAAA,gBAEvDtD,OAAA;wBAAGuD,SAAS,EAAC;sBAAmB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,WAEvC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT,eAED3D,OAAA,CAACuK,MAAM;sBACLtH,OAAO,EAAC,mBAAmB;sBAC3BmH,IAAI,EAAC,IAAI;sBACTI,OAAO,EAAEA,CAAA,KAAMvD,cAAc,CAACC,GAAG,CAAE;sBACnCmE,KAAK,EAAC,MAAM;sBAAA/H,QAAA,eAEZtD,OAAA;wBAAGuD,SAAS,EAAC;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAnIIuD,GAAG,CAAC1B,EAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoIb,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;UAAA;UAER;UACA3D,OAAA,CAACqJ,GAAG;YAAA/F,QAAA,EACDmF,YAAY,CAACmC,GAAG,CAAC1D,GAAG;cAAA,IAAAoE,aAAA;cAAA,oBACnBtL,OAAA,CAACsJ,GAAG;gBAAcE,EAAE,EAAE,CAAE;gBAAC+B,EAAE,EAAE,CAAE;gBAAChI,SAAS,EAAC,MAAM;gBAAAD,QAAA,eAC9CtD,OAAA,CAACyJ,IAAI;kBAAClG,SAAS,EAAE,4BAA4B2D,GAAG,CAACrD,QAAQ,KAAK,QAAQ,GAAG,iBAAiB,GAAG,EAAE,aAAa,EAAAyH,aAAA,GAAApE,GAAG,CAACrD,QAAQ,cAAAyH,aAAA,uBAAZA,aAAA,CAAc1D,WAAW,CAAC,CAAC,KAAI,QAAQ,EAAG;kBAAAtE,QAAA,gBACpJtD,OAAA,CAACyJ,IAAI,CAACE,MAAM;oBAACpG,SAAS,EAAC,mDAAmD;oBAAAD,QAAA,gBACxEtD,OAAA;sBAAAsD,QAAA,gBACEtD,OAAA;wBAAAsD,QAAA,EAAS4D,GAAG,CAACW;sBAAS;wBAAArE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eAChC3D,OAAA;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,EACLC,gBAAgB,CAACsD,GAAG,CAACrD,QAAQ,CAAC;oBAAA;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,EACLb,cAAc,CAACoE,GAAG,CAACnE,MAAM,CAAC;kBAAA;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,eACd3D,OAAA,CAACyJ,IAAI,CAACC,IAAI;oBAAApG,QAAA,gBACRtD,OAAA;sBAAKuD,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBACnBtD,OAAA;wBAAAsD,QAAA,EAAS4D,GAAG,CAACa;sBAAW;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eAClC3D,OAAA;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN3D,OAAA;wBAAOuD,SAAS,EAAC,YAAY;wBAAAD,QAAA,EAAE4D,GAAG,CAACc;sBAAY;wBAAAxE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC,eACN3D,OAAA;sBAAKuD,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBACnBtD,OAAA;wBAAGuD,SAAS,EAAC;sBAAkC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpD3D,OAAA;wBAAAsD,QAAA,EAAQ4D,GAAG,CAAC1D,QAAQ,CAACoF,MAAM,GAAG,EAAE,GAAG1B,GAAG,CAAC1D,QAAQ,CAACqH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG3D,GAAG,CAAC1D;sBAAQ;wBAAAA,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC,eACN3D,OAAA;sBAAKuD,SAAS,EAAC,YAAY;sBAAAD,QAAA,gBACzBtD,OAAA;wBAAAsD,QAAA,gBAAKtD,OAAA;0BAAAsD,QAAA,EAAQ;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACuD,GAAG,CAACe,SAAS;sBAAA;wBAAAzE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjD3D,OAAA;wBAAAsD,QAAA,gBAAKtD,OAAA;0BAAAsD,QAAA,EAAQ;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACuD,GAAG,CAAC6D,MAAM;sBAAA;wBAAAvH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAChD3D,OAAA;wBAAAsD,QAAA,gBAAKtD,OAAA;0BAAAsD,QAAA,EAAQ;wBAAM;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACuD,GAAG,CAAC8D,SAAS;sBAAA;wBAAAxH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAClD3D,OAAA;wBAAAsD,QAAA,gBAAKtD,OAAA;0BAAAsD,QAAA,EAAQ;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACuD,GAAG,CAAC+D,SAAS;sBAAA;wBAAAzH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,EACLuD,GAAG,CAACpG,IAAI,iBACPd,OAAA;sBAAKuD,SAAS,EAAC,MAAM;sBAAAD,QAAA,eACnBtD,OAAA;wBAAQuD,SAAS,EAAC,cAAc;wBAAAD,QAAA,GAAC,GAAC,EAAC4D,GAAG,CAACpG,IAAI,CAACmE,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAzB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CACN,eACD3D,OAAA;sBAAKuD,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAC9BU,UAAU,CAACkD,GAAG,CAACqB,OAAO;oBAAC;sBAAA/E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ3D,OAAA,CAACyJ,IAAI,CAAC+B,MAAM;oBAAAlI,QAAA,eACVtD,OAAA;sBAAKuD,SAAS,EAAC,uCAAuC;sBAAAD,QAAA,gBACpDtD,OAAA,CAACuK,MAAM;wBACLtH,OAAO,EAAC,cAAc;wBACtBmH,IAAI,EAAC,IAAI;wBACTI,OAAO,EAAEA,CAAA,KAAM1E,kBAAkB,CAACoB,GAAG,CAAC1B,EAAE,EAAE0B,GAAG,CAAC1D,QAAQ,CAAE;wBACxD6H,KAAK,EAAC,eAAe;wBAAA/H,QAAA,eAErBtD,OAAA;0BAAGuD,SAAS,EAAC;wBAAiB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,EAERuD,GAAG,CAACnE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAAAE,SAAA;wBAAAoD,QAAA,gBACEtD,OAAA,CAACuK,MAAM;0BACLtH,OAAO,EAAC,iBAAiB;0BACzBmH,IAAI,EAAC,IAAI;0BACTI,OAAO,EAAEA,CAAA,KAAM;4BACb/J,cAAc,CAACyG,GAAG,CAAC;4BACnBvG,iBAAiB,CAAC,IAAI,CAAC;0BACzB,CAAE;0BACF0K,KAAK,EAAC,OAAO;0BAAA/H,QAAA,eAEbtD,OAAA;4BAAGuD,SAAS,EAAC;0BAAoB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC,eACT3D,OAAA,CAACuK,MAAM;0BACLtH,OAAO,EAAC,gBAAgB;0BACxBmH,IAAI,EAAC,IAAI;0BACTI,OAAO,EAAEA,CAAA,KAAM9E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,UAAU,CAAE;0BACtD6F,KAAK,EAAC,QAAQ;0BAAA/H,QAAA,eAEdtD,OAAA;4BAAGuD,SAAS,EAAC;0BAAc;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1B,CAAC;sBAAA,eACT,CACH,EAEAuD,GAAG,CAACnE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAACuK,MAAM;wBACLtH,OAAO,EAAC,cAAc;wBACtBmH,IAAI,EAAC,IAAI;wBACTI,OAAO,EAAEA,CAAA,KAAM9E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,YAAY,CAAE;wBACxD6F,KAAK,EAAC,OAAO;wBAAA/H,QAAA,eAEbtD,OAAA;0BAAGuD,SAAS,EAAC;wBAAa;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CACT,EAEAuD,GAAG,CAACnE,MAAM,KAAK,YAAY,iBAC1B/C,OAAA,CAACuK,MAAM;wBACLtH,OAAO,EAAC,iBAAiB;wBACzBmH,IAAI,EAAC,IAAI;wBACTI,OAAO,EAAEA,CAAA,KAAM9E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,WAAW,CAAE;wBACvD6F,KAAK,EAAC,UAAU;wBAAA/H,QAAA,eAEhBtD,OAAA;0BAAGuD,SAAS,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CACT,EAEAuD,GAAG,CAACnE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAACuK,MAAM;wBACLtH,OAAO,EAAC,iBAAiB;wBACzBmH,IAAI,EAAC,IAAI;wBACTI,OAAO,EAAEA,CAAA,KAAM9E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,WAAW,CAAE;wBACvD6F,KAAK,EAAC,SAAS;wBAAA/H,QAAA,eAEftD,OAAA;0BAAGuD,SAAS,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CACT,eAED3D,OAAA,CAACuK,MAAM;wBACLtH,OAAO,EAAC,mBAAmB;wBAC3BmH,IAAI,EAAC,IAAI;wBACTI,OAAO,EAAEA,CAAA,KAAMvD,cAAc,CAACC,GAAG,CAAE;wBACnCmE,KAAK,EAAC,MAAM;wBAAA/H,QAAA,eAEZtD,OAAA;0BAAGuD,SAAS,EAAC;wBAAgB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,eAET3D,OAAA,CAACuK,MAAM;wBACLtH,OAAO,EAAC,cAAc;wBACtBmH,IAAI,EAAC,IAAI;wBACTI,OAAO,EAAEA,CAAA,KAAM;0BACb/J,cAAc,CAACyG,GAAG,CAAC;0BACnBhF,sBAAsB,CAAC,IAAI,CAAC;wBAC9B,CAAE;wBACFmJ,KAAK,EAAC,cAAc;wBAAA/H,QAAA,eAEpBtD,OAAA;0BAAGuD,SAAS,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GA7HCuD,GAAG,CAAC1B,EAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8HX,CAAC;YAAA,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,gBAED3D,OAAA,CAACyL,KAAK;YAACxI,OAAO,EAAC,MAAM;YAAAK,QAAA,gBACnBtD,OAAA;cAAGuD,SAAS,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,0CAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGP3D,OAAA,CAAC0L,KAAK;QAACC,IAAI,EAAEjL,cAAe;QAACkL,MAAM,EAAEA,CAAA,KAAMjL,iBAAiB,CAAC,KAAK,CAAE;QAAA2C,QAAA,gBAClEtD,OAAA,CAAC0L,KAAK,CAAC/B,MAAM;UAACkC,WAAW;UAAAvI,QAAA,eACvBtD,OAAA,CAAC0L,KAAK,CAACI,KAAK;YAAAxI,QAAA,gBACVtD,OAAA;cAAGuD,SAAS,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,oBAC3B,EAACnD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqH,SAAS;UAAA;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACf3D,OAAA,CAAC0L,KAAK,CAAChC,IAAI;UAAApG,QAAA,EACR9C,WAAW,iBACVR,OAAA,CAAAE,SAAA;YAAAoD,QAAA,gBACEtD,OAAA;cAAKuD,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCtD,OAAA;gBAAAsD,QAAA,EAAI;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB3D,OAAA;gBAAKuD,SAAS,EAAC,KAAK;gBAAAD,QAAA,gBAClBtD,OAAA;kBAAKuD,SAAS,EAAC,OAAO;kBAAAD,QAAA,gBACpBtD,OAAA;oBAAAsD,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACgD,QAAQ,eAACxD,OAAA;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnD3D,OAAA;oBAAAsD,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACyH,SAAS,eAACjI,OAAA;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpD3D,OAAA;oBAAAsD,QAAA,EAAQ;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACuK,MAAM;gBAAA;kBAAAvH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACN3D,OAAA;kBAAKuD,SAAS,EAAC,OAAO;kBAAAD,QAAA,gBACpBtD,OAAA;oBAAAsD,QAAA,EAAQ;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACwK,SAAS,eAAChL,OAAA;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrD3D,OAAA;oBAAAsD,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACyK,SAAS,eAACjL,OAAA;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpD3D,OAAA;oBAAAsD,QAAA,EAAQ;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACuH,WAAW;gBAAA;kBAAAvE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLnD,WAAW,CAACuL,OAAO,iBAClB/L,OAAA;gBAAKuD,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBtD,OAAA;kBAAAsD,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACuL,OAAO;cAAA;gBAAAvI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN3D,OAAA,CAAC4J,IAAI;cAAAtG,QAAA,gBACHtD,OAAA,CAACqJ,GAAG;gBAAA/F,QAAA,gBACFtD,OAAA,CAACsJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAlG,QAAA,eACTtD,OAAA,CAAC4J,IAAI,CAACoC,KAAK;oBAACzI,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1BtD,OAAA,CAAC4J,IAAI,CAACqC,KAAK;sBAAA3I,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjC3D,OAAA,CAACkM,UAAU;sBAAA5I,QAAA,gBACTtD,OAAA,CAACkM,UAAU,CAACC,IAAI;wBAAA7I,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAiB,CAAC,eACpC3D,OAAA,CAAC4J,IAAI,CAACC,OAAO;wBACXC,IAAI,EAAC,QAAQ;wBACbsC,IAAI,EAAC,MAAM;wBACXrC,WAAW,EAAC,MAAM;wBAClBC,KAAK,EAAEpJ,SAAS,CAACE,IAAK;wBACtBmJ,QAAQ,EAAGC,CAAC,IAAKrJ,YAAY,CAAC0G,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAEzG,IAAI,EAAEoJ,CAAC,CAACC,MAAM,CAACH;wBAAM,CAAC,CAAC,CAAE;wBAC3EqC,QAAQ;sBAAA;wBAAA7I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN3D,OAAA,CAACsJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAlG,QAAA,eACTtD,OAAA,CAAC4J,IAAI,CAACoC,KAAK;oBAACzI,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1BtD,OAAA,CAAC4J,IAAI,CAACqC,KAAK;sBAAA3I,QAAA,EAAC;oBAAe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACxC3D,OAAA,CAAC4J,IAAI,CAACC,OAAO;sBACXC,IAAI,EAAC,QAAQ;sBACbwC,GAAG,EAAC,GAAG;sBACPvC,WAAW,EAAC,mBAAmB;sBAC/BC,KAAK,EAAEpJ,SAAS,CAACG,cAAe;sBAChCkJ,QAAQ,EAAGC,CAAC,IAAKrJ,YAAY,CAAC0G,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAExG,cAAc,EAAEmJ,CAAC,CAACC,MAAM,CAACH;sBAAM,CAAC,CAAC,CAAE;sBACrFqC,QAAQ;oBAAA;sBAAA7I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN3D,OAAA,CAAC4J,IAAI,CAACoC,KAAK;gBAACzI,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BtD,OAAA,CAAC4J,IAAI,CAACqC,KAAK;kBAAA3I,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzC3D,OAAA,CAAC4J,IAAI,CAACC,OAAO;kBACX0C,EAAE,EAAC,UAAU;kBACbC,IAAI,EAAE,CAAE;kBACRzC,WAAW,EAAC,yCAAyC;kBACrDC,KAAK,EAAEpJ,SAAS,CAACI,KAAM;kBACvBiJ,QAAQ,EAAGC,CAAC,IAAKrJ,YAAY,CAAC0G,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEvG,KAAK,EAAEkJ,CAAC,CAACC,MAAM,CAACH;kBAAM,CAAC,CAAC;gBAAE;kBAAAxG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA,eACP;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eACb3D,OAAA,CAAC0L,KAAK,CAACF,MAAM;UAAAlI,QAAA,gBACXtD,OAAA,CAACuK,MAAM;YAACtH,OAAO,EAAC,WAAW;YAACuH,OAAO,EAAEA,CAAA,KAAM7J,iBAAiB,CAAC,KAAK,CAAE;YAAA2C,QAAA,EAAC;UAErE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3D,OAAA,CAACuK,MAAM;YACLtH,OAAO,EAAC,SAAS;YACjBuH,OAAO,EAAEtF,iBAAkB;YAC3BuH,QAAQ,EAAE,CAAC7L,SAAS,CAACE,IAAI,IAAI,CAACF,SAAS,CAACG,cAAe;YAAAuC,QAAA,gBAEvDtD,OAAA;cAAGuD,SAAS,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,cAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGR3D,OAAA,CAAC0L,KAAK;QAACC,IAAI,EAAExK,aAAc;QAACyK,MAAM,EAAEA,CAAA,KAAMxK,gBAAgB,CAAC,KAAK,CAAE;QAACgJ,IAAI,EAAC,IAAI;QAAA9G,QAAA,gBAC1EtD,OAAA,CAAC0L,KAAK,CAAC/B,MAAM;UAACkC,WAAW;UAAAvI,QAAA,eACvBtD,OAAA,CAAC0L,KAAK,CAACI,KAAK;YAAAxI,QAAA,gBACVtD,OAAA;cAAGuD,SAAS,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,WAChC,EAACnD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqH,SAAS;UAAA;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACf3D,OAAA,CAAC0L,KAAK,CAAChC,IAAI;UAAApG,QAAA,gBACTtD,OAAA;YAAK0M,KAAK,EAAE;cAAEC,MAAM,EAAE,OAAO;cAAEC,SAAS,EAAE,MAAM;cAAEC,MAAM,EAAE,mBAAmB;cAAEC,YAAY,EAAE,UAAU;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAA1J,QAAA,EAC9IjC,QAAQ,CAACuH,MAAM,GAAG,CAAC,GAClBvH,QAAQ,CAACuJ,GAAG,CAAEqC,OAAO,iBACnBjN,OAAA;cAAsBuD,SAAS,EAAE,QAAQ0J,OAAO,CAACC,iBAAiB,GAAG,UAAU,GAAG,YAAY,EAAG;cAAA5J,QAAA,eAC/FtD,OAAA;gBAAKuD,SAAS,EAAE,8BAA8B0J,OAAO,CAACC,iBAAiB,GAAG,uBAAuB,GAAG,UAAU,EAAG;gBAACR,KAAK,EAAE;kBAAES,QAAQ,EAAE;gBAAM,CAAE;gBAAA7J,QAAA,gBAC3ItD,OAAA;kBAAAsD,QAAA,EAAM2J,OAAO,CAACG;gBAAO;kBAAA5J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5B3D,OAAA;kBAAOuD,SAAS,EAAE,gBAAgB0J,OAAO,CAACC,iBAAiB,GAAG,YAAY,GAAG,YAAY,EAAG;kBAAA5J,QAAA,GACzF2J,OAAO,CAACI,UAAU,EAAC,KAAG,EAAC,IAAIlJ,IAAI,CAAC8I,OAAO,CAACK,MAAM,CAAC,CAACC,cAAc,CAAC,CAAC;gBAAA;kBAAA/J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC,GANEsJ,OAAO,CAACzH,EAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOf,CACN,CAAC,gBAEF3D,OAAA;cAAKuD,SAAS,EAAC,wBAAwB;cAAAD,QAAA,gBACrCtD,OAAA;gBAAGuD,SAAS,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9C3D,OAAA;gBAAAsD,QAAA,EAAG;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN3D,OAAA;YAAKuD,SAAS,EAAC,QAAQ;YAAAD,QAAA,gBACrBtD,OAAA,CAAC4J,IAAI,CAACC,OAAO;cACXC,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,sBAAsB;cAClCC,KAAK,EAAEzI,UAAW;cAClB0I,QAAQ,EAAGC,CAAC,IAAK1I,aAAa,CAAC0I,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CwD,SAAS,EAAGtD,CAAC,IAAKA,CAAC,CAACuD,GAAG,KAAK,OAAO,IAAIrG,iBAAiB,CAAC,CAAE;cAC3D7D,SAAS,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACF3D,OAAA,CAACuK,MAAM;cAACtH,OAAO,EAAC,SAAS;cAACuH,OAAO,EAAEpD,iBAAkB;cAACqF,QAAQ,EAAE,CAAClL,UAAU,CAAC8F,IAAI,CAAC,CAAE;cAAA/D,QAAA,eACjFtD,OAAA;gBAAGuD,SAAS,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACb3D,OAAA,CAAC0L,KAAK,CAACF,MAAM;UAAAlI,QAAA,eACXtD,OAAA,CAACuK,MAAM;YAACtH,OAAO,EAAC,WAAW;YAACuH,OAAO,EAAEA,CAAA,KAAMpJ,gBAAgB,CAAC,KAAK,CAAE;YAAAkC,QAAA,EAAC;UAEpE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGR3D,OAAA,CAAC0L,KAAK;QAACC,IAAI,EAAE1J,mBAAoB;QAAC2J,MAAM,EAAEA,CAAA,KAAM1J,sBAAsB,CAAC,KAAK,CAAE;QAACkI,IAAI,EAAC,IAAI;QAAC7G,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBACpHtD,OAAA,CAAC0L,KAAK,CAAC/B,MAAM;UAACkC,WAAW;UAAAvI,QAAA,eACvBtD,OAAA,CAAC0L,KAAK,CAACI,KAAK;YAAAxI,QAAA,gBACVtD,OAAA;cAAGuD,SAAS,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,kBAC7B,EAACnD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqH,SAAS;UAAA;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACf3D,OAAA,CAAC0L,KAAK,CAAChC,IAAI;UAAApG,QAAA,EACR9C,WAAW,iBACVR,OAAA,CAACqJ,GAAG;YAAA/F,QAAA,gBACFtD,OAAA,CAACsJ,GAAG;cAACE,EAAE,EAAE,CAAE;cAAAlG,QAAA,gBACTtD,OAAA,CAACyJ,IAAI;gBAAClG,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACpBtD,OAAA,CAACyJ,IAAI,CAACE,MAAM;kBAAArG,QAAA,eACVtD,OAAA;oBAAIuD,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAClBtD,OAAA;sBAAGuD,SAAS,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,uBAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eACd3D,OAAA,CAACyJ,IAAI,CAACC,IAAI;kBAAApG,QAAA,gBACRtD,OAAA;oBAAAsD,QAAA,gBAAKtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACuH,WAAW;kBAAA;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3D3D,OAAA;oBAAAsD,QAAA,gBAAKtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACwH,YAAY;kBAAA;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eAEP3D,OAAA,CAACyJ,IAAI;gBAAClG,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACpBtD,OAAA,CAACyJ,IAAI,CAACE,MAAM;kBAAArG,QAAA,eACVtD,OAAA;oBAAIuD,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAClBtD,OAAA;sBAAGuD,SAAS,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,oBAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eACd3D,OAAA,CAACyJ,IAAI,CAACC,IAAI;kBAAApG,QAAA,gBACRtD,OAAA;oBAAAsD,QAAA,gBAAKtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACgD,QAAQ;kBAAA;oBAAAA,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC5DnD,WAAW,CAACsK,QAAQ,iBACnB9K,OAAA;oBAAAsD,QAAA,gBAAKtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACc,cAAc,CAACjE,WAAW,CAACsK,QAAQ,CAAC;kBAAA;oBAAAtH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAC7E,eACD3D,OAAA;oBAAKuD,SAAS,EAAC,MAAM;oBAAAD,QAAA,eACnBtD,OAAA,CAACuK,MAAM;sBACLtH,OAAO,EAAC,iBAAiB;sBACzBmH,IAAI,EAAC,IAAI;sBACTI,OAAO,EAAEA,CAAA,KAAM1E,kBAAkB,CAACtF,WAAW,CAACgF,EAAE,EAAEhF,WAAW,CAACgD,QAAQ,CAAE;sBAAAF,QAAA,gBAExEtD,OAAA;wBAAGuD,SAAS,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,iBAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN3D,OAAA,CAACsJ,GAAG;cAACE,EAAE,EAAE,CAAE;cAAAlG,QAAA,gBACTtD,OAAA,CAACyJ,IAAI;gBAAClG,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACpBtD,OAAA,CAACyJ,IAAI,CAACE,MAAM;kBAAArG,QAAA,eACVtD,OAAA;oBAAIuD,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAClBtD,OAAA;sBAAGuD,SAAS,EAAC;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,wBAEvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eACd3D,OAAA,CAACyJ,IAAI,CAACC,IAAI;kBAAApG,QAAA,gBACRtD,OAAA;oBAAAsD,QAAA,gBAAKtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACyH,SAAS;kBAAA;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/D3D,OAAA;oBAAAsD,QAAA,gBAAKtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACuK,MAAM;kBAAA;oBAAAvH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxD3D,OAAA;oBAAAsD,QAAA,gBAAKtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACwK,SAAS;kBAAA;oBAAAxH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/D3D,OAAA;oBAAAsD,QAAA,gBAAKtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACnD,WAAW,CAACyK,SAAS;kBAAA;oBAAAzH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC9DnD,WAAW,CAACuL,OAAO,iBAClB/L,OAAA;oBAAKuD,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzB3D,OAAA;sBAAKuD,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACvC9C,WAAW,CAACuL;oBAAO;sBAAAvI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eAEP3D,OAAA,CAACyJ,IAAI;gBAAClG,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACpBtD,OAAA,CAACyJ,IAAI,CAACE,MAAM;kBAAArG,QAAA,eACVtD,OAAA;oBAAIuD,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAClBtD,OAAA;sBAAGuD,SAAS,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,cAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eACd3D,OAAA,CAACyJ,IAAI,CAACC,IAAI;kBAAApG,QAAA,gBACRtD,OAAA;oBAAKuD,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACb,cAAc,CAACtC,WAAW,CAACuC,MAAM,CAAC;kBAAA;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACN3D,OAAA;oBAAKuD,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACC,gBAAgB,CAACpD,WAAW,CAACqD,QAAQ,CAAC;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,EACLnD,WAAW,CAACM,IAAI,iBACfd,OAAA;oBAAKuD,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAA3D,OAAA;sBAAMuD,SAAS,EAAC,cAAc;sBAAAD,QAAA,GAAC,GAAC,EAAC9C,WAAW,CAACM,IAAI,CAACmE,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF,CACN,eACD3D,OAAA;oBAAKuD,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIQ,IAAI,CAAC3D,WAAW,CAAC+H,OAAO,CAAC,CAACgF,cAAc,CAAC,CAAC;kBAAA;oBAAA/J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,EACLnD,WAAW,CAAC2K,uBAAuB,iBAClCnL,OAAA;oBAAAsD,QAAA,gBACEtD,OAAA;sBAAAsD,QAAA,EAAQ;oBAAqB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIQ,IAAI,CAAC3D,WAAW,CAAC2K,uBAAuB,CAAC,CAACoC,cAAc,CAAC,CAAC;kBAAA;oBAAA/J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnG,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eACb3D,OAAA,CAAC0L,KAAK,CAACF,MAAM;UAAAlI,QAAA,gBACXtD,OAAA,CAACuK,MAAM;YAACtH,OAAO,EAAC,WAAW;YAACuH,OAAO,EAAEA,CAAA,KAAMtI,sBAAsB,CAAC,KAAK,CAAE;YAAAoB,QAAA,EAAC;UAE1E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRnD,WAAW,iBACVR,OAAA,CAACuK,MAAM;YACLtH,OAAO,EAAC,SAAS;YACjBuH,OAAO,EAAEA,CAAA,KAAM;cACbtI,sBAAsB,CAAC,KAAK,CAAC;cAC7B+E,cAAc,CAACzG,WAAW,CAAC;YAC7B,CAAE;YAAA8C,QAAA,gBAEFtD,OAAA;cAAGuD,SAAS,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,cAEzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACvD,EAAA,CAt8BID,oBAA8B;EAAA,QACjBR,OAAO;AAAA;AAAA+N,EAAA,GADpBvN,oBAA8B;AAw8BpC,eAAeA,oBAAoB;AAAC,IAAAuN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}