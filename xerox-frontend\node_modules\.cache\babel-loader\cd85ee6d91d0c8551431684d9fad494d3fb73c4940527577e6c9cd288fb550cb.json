{"ast": null, "code": "import { createRendererMotionComponent } from '../../motion/index.mjs';\nimport { createUseRender } from '../dom/use-render.mjs';\nimport { isSVGComponent } from '../dom/utils/is-svg-component.mjs';\nimport { htmlMotionConfig } from '../html/config-motion.mjs';\nimport { svgMotionConfig } from '../svg/config-motion.mjs';\nfunction createMotionComponentFactory(preloadedFeatures, createVisualElement) {\n  return function createMotionComponent(Component, {\n    forwardMotionProps\n  } = {\n    forwardMotionProps: false\n  }) {\n    const baseConfig = isSVGComponent(Component) ? svgMotionConfig : htmlMotionConfig;\n    const config = {\n      ...baseConfig,\n      preloadedFeatures,\n      useRender: createUseRender(forwardMotionProps),\n      createVisualElement,\n      Component\n    };\n    return createRendererMotionComponent(config);\n  };\n}\nexport { createMotionComponentFactory };", "map": {"version": 3, "names": ["createRendererMotionComponent", "createUseRender", "isSVGComponent", "htmlMotionConfig", "svgMotionConfig", "createMotionComponentFactory", "preloadedFeatures", "createVisualElement", "createMotionComponent", "Component", "forwardMotionProps", "baseConfig", "config", "useRender"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/render/components/create-factory.mjs"], "sourcesContent": ["import { createRendererMotionComponent } from '../../motion/index.mjs';\nimport { createUseRender } from '../dom/use-render.mjs';\nimport { isSVGComponent } from '../dom/utils/is-svg-component.mjs';\nimport { htmlMotionConfig } from '../html/config-motion.mjs';\nimport { svgMotionConfig } from '../svg/config-motion.mjs';\n\nfunction createMotionComponentFactory(preloadedFeatures, createVisualElement) {\n    return function createMotionComponent(Component, { forwardMotionProps } = { forwardMotionProps: false }) {\n        const baseConfig = isSVGComponent(Component)\n            ? svgMotionConfig\n            : htmlMotionConfig;\n        const config = {\n            ...baseConfig,\n            preloadedFeatures,\n            useRender: createUseRender(forwardMotionProps),\n            createVisualElement,\n            Component,\n        };\n        return createRendererMotionComponent(config);\n    };\n}\n\nexport { createMotionComponentFactory };\n"], "mappings": "AAAA,SAASA,6BAA6B,QAAQ,wBAAwB;AACtE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,SAASC,4BAA4BA,CAACC,iBAAiB,EAAEC,mBAAmB,EAAE;EAC1E,OAAO,SAASC,qBAAqBA,CAACC,SAAS,EAAE;IAAEC;EAAmB,CAAC,GAAG;IAAEA,kBAAkB,EAAE;EAAM,CAAC,EAAE;IACrG,MAAMC,UAAU,GAAGT,cAAc,CAACO,SAAS,CAAC,GACtCL,eAAe,GACfD,gBAAgB;IACtB,MAAMS,MAAM,GAAG;MACX,GAAGD,UAAU;MACbL,iBAAiB;MACjBO,SAAS,EAAEZ,eAAe,CAACS,kBAAkB,CAAC;MAC9CH,mBAAmB;MACnBE;IACJ,CAAC;IACD,OAAOT,6BAA6B,CAACY,MAAM,CAAC;EAChD,CAAC;AACL;AAEA,SAASP,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}