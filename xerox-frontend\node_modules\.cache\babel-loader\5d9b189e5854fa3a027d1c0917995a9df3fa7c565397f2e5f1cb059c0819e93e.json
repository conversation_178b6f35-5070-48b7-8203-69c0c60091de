{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\StudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Upload, FileText, Clock, CheckCircle, DollarSign, Download, MessageCircle, Info, Check, Star, MapPin, TrendingUp, Activity, Printer, Eye, Send, X, Filter, Search, Calendar } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../services/api';\nimport { cn } from '../lib/utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [xeroxCenters, setXeroxCenters] = useState([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        // Fetch xerox centers\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        // Set empty arrays on error\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n    fetchData();\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        color: 'bg-gray-100 text-gray-800 border-gray-200',\n        icon: Clock\n      },\n      'UnderReview': {\n        color: 'bg-blue-100 text-blue-800 border-blue-200',\n        icon: Eye\n      },\n      'Quoted': {\n        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n        icon: DollarSign\n      },\n      'WaitingConfirmation': {\n        color: 'bg-orange-100 text-orange-800 border-orange-200',\n        icon: Clock\n      },\n      'Confirmed': {\n        color: 'bg-indigo-100 text-indigo-800 border-indigo-200',\n        icon: Check\n      },\n      'InProgress': {\n        color: 'bg-purple-100 text-purple-800 border-purple-200',\n        icon: Activity\n      },\n      'Completed': {\n        color: 'bg-green-100 text-green-800 border-green-200',\n        icon: CheckCircle\n      },\n      'Delivered': {\n        color: 'bg-emerald-100 text-emerald-800 border-emerald-200',\n        icon: CheckCircle\n      },\n      'Rejected': {\n        color: 'bg-red-100 text-red-800 border-red-200',\n        icon: X\n      },\n      'Cancelled': {\n        color: 'bg-gray-100 text-gray-800 border-gray-200',\n        icon: X\n      }\n    };\n    const config = statusConfig[status] || {\n      color: 'bg-gray-100 text-gray-800 border-gray-200',\n      icon: Info\n    };\n    const IconComponent = config.icon;\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        scale: 0.8,\n        opacity: 0\n      },\n      animate: {\n        scale: 1,\n        opacity: 1\n      },\n      className: cn(\"inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border\", config.color),\n      children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n        className: \"w-3 h-3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this);\n  };\n  const getWorkloadColor = pendingJobs => {\n    if (pendingJobs <= 5) return 'success';\n    if (pendingJobs <= 10) return 'warning';\n    return 'danger';\n  };\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n      await fileUploadApi.uploadFile(formData);\n\n      // Refresh print jobs after upload\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n  const handleViewJob = job => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n  const handleOpenChat = async job => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      // Load messages for this job\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n  const handleDownloadFile = async (jobId, fileName) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n  const handleConfirmJob = async jobId => {\n    try {\n      await printJobApi.confirmJob(jobId);\n\n      // Refresh print jobs after confirmation\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n\n      // Add the new message to the list\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n  const {\n    isDarkMode\n  } = useTheme();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 p-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n            className: \"text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent\",\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            children: \"Student Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            className: \"text-slate-600 dark:text-slate-300 text-lg\",\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-blue-600 dark:text-blue-400\",\n              children: user === null || user === void 0 ? void 0 : user.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 29\n            }, this), \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: () => setShowUploadModal(true),\n          className: cn(\"group relative px-6 py-3 rounded-xl font-medium transition-all duration-300\", \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\", \"text-white shadow-lg hover:shadow-xl transform hover:scale-105\", \"border border-white/20 backdrop-blur-sm\"),\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            delay: 0.4\n          },\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Upload Files\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-blue-600/20 to-purple-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.5,\n          staggerChildren: 0.1\n        },\n        children: [{\n          title: \"Total Jobs\",\n          value: printJobs.length,\n          icon: FileText,\n          color: \"from-blue-500 to-blue-600\",\n          bgColor: \"bg-blue-50 dark:bg-blue-900/20\",\n          textColor: \"text-blue-600 dark:text-blue-400\",\n          change: \"+12%\",\n          changeType: \"positive\"\n        }, {\n          title: \"In Progress\",\n          value: printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length,\n          icon: Clock,\n          color: \"from-orange-500 to-orange-600\",\n          bgColor: \"bg-orange-50 dark:bg-orange-900/20\",\n          textColor: \"text-orange-600 dark:text-orange-400\",\n          change: \"+5%\",\n          changeType: \"positive\"\n        }, {\n          title: \"Completed\",\n          value: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n          icon: CheckCircle,\n          color: \"from-green-500 to-green-600\",\n          bgColor: \"bg-green-50 dark:bg-green-900/20\",\n          textColor: \"text-green-600 dark:text-green-400\",\n          change: \"+8%\",\n          changeType: \"positive\"\n        }, {\n          title: \"Total Spent\",\n          value: `$${printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)}`,\n          icon: DollarSign,\n          color: \"from-purple-500 to-purple-600\",\n          bgColor: \"bg-purple-50 dark:bg-purple-900/20\",\n          textColor: \"text-purple-600 dark:text-purple-400\",\n          change: \"+15%\",\n          changeType: \"positive\"\n        }].map((stat, index) => {\n          const IconComponent = stat.icon;\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              y: 0,\n              scale: 1\n            },\n            transition: {\n              delay: 0.6 + index * 0.1\n            },\n            whileHover: {\n              y: -5,\n              scale: 1.02\n            },\n            className: cn(\"relative p-6 rounded-2xl border border-white/20 backdrop-blur-sm\", \"bg-white/70 dark:bg-slate-800/70 shadow-lg hover:shadow-xl\", \"transition-all duration-300 group cursor-pointer\"),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: cn(\"p-3 rounded-xl\", stat.bgColor),\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                  className: cn(\"w-6 h-6\", stat.textColor)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-1 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600 font-medium\",\n                  children: stat.change\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-600 dark:text-slate-300 text-sm font-medium\",\n                children: stat.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-3xl font-bold text-slate-900 dark:text-white\",\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: cn(\"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-10\", \"bg-gradient-to-br transition-opacity duration-300\", stat.color)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this)]\n          }, stat.title, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"lg:col-span-2\",\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: 0.8\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl border border-white/20 shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 border-b border-slate-200 dark:border-slate-700\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(FileText, {\n                      className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-slate-900 dark:text-white\",\n                    children: \"Recent Print Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\",\n                    children: /*#__PURE__*/_jsxDEV(Filter, {\n                      className: \"w-4 h-4 text-slate-600 dark:text-slate-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\",\n                    children: /*#__PURE__*/_jsxDEV(Search, {\n                      className: \"w-4 h-4 text-slate-600 dark:text-slate-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: printJobs.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: printJobs.map((job, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.9 + index * 0.1\n                  },\n                  className: \"group p-4 rounded-xl border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300 hover:shadow-md\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-4 flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"p-2 bg-red-100 dark:bg-red-900/30 rounded-lg\",\n                        children: /*#__PURE__*/_jsxDEV(FileText, {\n                          className: \"w-5 h-5 text-red-600 dark:text-red-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 427,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 426,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-2 mb-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-semibold text-slate-900 dark:text-white\",\n                            children: job.jobNumber\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 432,\n                            columnNumber: 33\n                          }, this), getStatusBadge(job.status)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 431,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-slate-600 dark:text-slate-300 truncate\",\n                          children: job.fileName\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 437,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-4 mt-2 text-xs text-slate-500 dark:text-slate-400\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-1\",\n                            children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                              className: \"w-3 h-3\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 442,\n                              columnNumber: 35\n                            }, this), job.xeroxCenterName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 441,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-1\",\n                            children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                              className: \"w-3 h-3\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 446,\n                              columnNumber: 35\n                            }, this), job.cost ? `$${job.cost.toFixed(2)}` : 'Not quoted']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 445,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"flex items-center gap-1\",\n                            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                              className: \"w-3 h-3\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 450,\n                              columnNumber: 35\n                            }, this), new Date(job.created).toLocaleDateString()]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 449,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 440,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 430,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                      children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => handleDownloadFile(job.id, job.fileName),\n                        className: \"p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors\",\n                        title: \"Download File\",\n                        children: /*#__PURE__*/_jsxDEV(Download, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 465,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 458,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => handleViewJob(job),\n                        className: \"p-2 text-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\",\n                        title: \"View Details\",\n                        children: /*#__PURE__*/_jsxDEV(Info, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 475,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 468,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => handleOpenChat(job),\n                        className: \"p-2 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-colors\",\n                        title: \"Chat\",\n                        children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 485,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 478,\n                        columnNumber: 29\n                      }, this), job.status === 'Quoted' && /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => handleConfirmJob(job.id),\n                        className: \"p-2 text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors\",\n                        title: \"Confirm Quote\",\n                        children: /*#__PURE__*/_jsxDEV(Check, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 496,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 489,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 457,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 25\n                  }, this)\n                }, job.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                className: \"text-center py-12\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(FileText, {\n                    className: \"w-8 h-8 text-blue-600 dark:text-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-slate-900 dark:text-white mb-2\",\n                  children: \"No print jobs yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-slate-600 dark:text-slate-300 mb-4\",\n                  children: \"Upload your first file to get started with printing!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: () => setShowUploadModal(true),\n                  className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  children: \"Upload File\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: 1.0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl border border-white/20 shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 border-b border-slate-200 dark:border-slate-700\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(Printer, {\n                    className: \"w-5 h-5 text-purple-600 dark:text-purple-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-semibold text-slate-900 dark:text-white\",\n                  children: \"Available Centers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 space-y-4\",\n              children: xeroxCenters.map((center, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 1.1 + index * 0.1\n                },\n                className: \"group p-4 rounded-xl border border-slate-200 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-300 hover:shadow-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-slate-900 dark:text-white mb-1\",\n                      children: center.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 562,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-1 text-sm text-slate-600 dark:text-slate-300 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                        className: \"w-3 h-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 566,\n                        columnNumber: 27\n                      }, this), center.location]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: cn(\"px-2.5 py-1 rounded-full text-xs font-medium\", center.pendingJobs <= 5 ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\" : center.pendingJobs <= 10 ? \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400\" : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"),\n                    children: [center.pendingJobs, \" jobs\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 570,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Star, {\n                      className: \"w-4 h-4 text-yellow-500 fill-current\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-slate-700 dark:text-slate-300\",\n                      children: center.averageRating.toFixed(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 585,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    className: \"px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors opacity-0 group-hover:opacity-100\",\n                    children: \"Select Center\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 21\n                }, this)]\n              }, center.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: showUploadModal && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          exit: {\n            opacity: 0\n          },\n          className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\",\n          onClick: () => setShowUploadModal(false),\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0.9,\n              opacity: 0\n            },\n            animate: {\n              scale: 1,\n              opacity: 1\n            },\n            exit: {\n              scale: 0.9,\n              opacity: 0\n            },\n            onClick: e => e.stopPropagation(),\n            className: \"w-full max-w-2xl bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-white/20\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(Upload, {\n                    className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-slate-900 dark:text-white\",\n                  children: \"Upload Files for Printing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowUploadModal(false),\n                className: \"p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(X, {\n                  className: \"w-5 h-5 text-slate-600 dark:text-slate-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                  children: \"Select File\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    accept: \".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\",\n                    onChange: e => {\n                      const files = e.target.files;\n                      setSelectedFile(files ? files[0] : null);\n                    },\n                    className: \"block w-full text-sm text-slate-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900/30 dark:file:text-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-slate-500 dark:text-slate-400\",\n                  children: \"Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                    children: \"Print Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: uploadData.printType,\n                    onChange: e => setUploadData(prev => ({\n                      ...prev,\n                      printType: e.target.value\n                    })),\n                    className: \"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Print\",\n                      children: \"Print\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 674,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Xerox\",\n                      children: \"Xerox\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 675,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Binding\",\n                      children: \"Binding\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Lamination\",\n                      children: \"Lamination\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 677,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                    children: \"Number of Copies\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    min: \"1\",\n                    value: uploadData.copies,\n                    onChange: e => setUploadData(prev => ({\n                      ...prev,\n                      copies: parseInt(e.target.value)\n                    })),\n                    className: \"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                    children: \"Color Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: uploadData.colorType,\n                    onChange: e => setUploadData(prev => ({\n                      ...prev,\n                      colorType: e.target.value\n                    })),\n                    className: \"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"BlackWhite\",\n                      children: \"Black & White\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 703,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Color\",\n                      children: \"Color\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 704,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 698,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                    children: \"Paper Size\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 709,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: uploadData.paperSize,\n                    onChange: e => setUploadData(prev => ({\n                      ...prev,\n                      paperSize: e.target.value\n                    })),\n                    className: \"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"A4\",\n                      children: \"A4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 717,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"A3\",\n                      children: \"A3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 718,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Letter\",\n                      children: \"Letter\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Legal\",\n                      children: \"Legal\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 720,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 708,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                  children: \"Preferred Xerox Center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: uploadData.preferredXeroxCenterId,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    preferredXeroxCenterId: e.target.value\n                  })),\n                  className: \"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select a center (optional)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 23\n                  }, this), xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: center.id,\n                    children: [center.name, \" - \", center.pendingJobs, \" pending jobs\"]\n                  }, center.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                  children: \"Remarks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  rows: 3,\n                  placeholder: \"Any special instructions or remarks...\",\n                  value: uploadData.remarks,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    remarks: e.target.value\n                  })),\n                  className: \"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-end gap-3 p-6 border-t border-slate-200 dark:border-slate-700\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                onClick: () => setShowUploadModal(false),\n                className: \"px-4 py-2 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\",\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                onClick: handleFileUpload,\n                disabled: !selectedFile,\n                className: cn(\"px-6 py-2 rounded-lg font-medium transition-all duration-300\", selectedFile ? \"bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl\" : \"bg-slate-300 dark:bg-slate-600 text-slate-500 dark:text-slate-400 cursor-not-allowed\"),\n                whileHover: selectedFile ? {\n                  scale: 1.02\n                } : {},\n                whileTap: selectedFile ? {\n                  scale: 0.98\n                } : {},\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Upload, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 23\n                  }, this), \"Upload File\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: showViewModal && selectedJob && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          exit: {\n            opacity: 0\n          },\n          className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\",\n          onClick: () => setShowViewModal(false),\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0.9,\n              opacity: 0\n            },\n            animate: {\n              scale: 1,\n              opacity: 1\n            },\n            exit: {\n              scale: 0.9,\n              opacity: 0\n            },\n            onClick: e => e.stopPropagation(),\n            className: \"w-full max-w-3xl bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-white/20\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(Eye, {\n                    className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-slate-900 dark:text-white\",\n                  children: [\"Job Details - \", selectedJob.jobNumber]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowViewModal(false),\n                className: \"p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(X, {\n                  className: \"w-5 h-5 text-slate-600 dark:text-slate-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 823,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-slate-900 dark:text-white border-b border-slate-200 dark:border-slate-700 pb-2\",\n                    children: \"File Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-slate-600 dark:text-slate-400\",\n                        children: \"File Name:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 837,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: selectedJob.fileName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 838,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 836,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-slate-600 dark:text-slate-400\",\n                        children: \"Print Type:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 841,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: selectedJob.printType\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 842,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 840,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-slate-600 dark:text-slate-400\",\n                        children: \"Copies:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 845,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: selectedJob.copies\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 846,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 844,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-slate-600 dark:text-slate-400\",\n                        children: \"Color Type:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 849,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: selectedJob.colorType\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 850,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 848,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-slate-600 dark:text-slate-400\",\n                        children: \"Paper Size:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 853,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: selectedJob.paperSize\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 854,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 852,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 835,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-slate-900 dark:text-white border-b border-slate-200 dark:border-slate-700 pb-2\",\n                    children: \"Job Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-slate-600 dark:text-slate-400\",\n                        children: \"Status:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 866,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-1\",\n                        children: getStatusBadge(selectedJob.status)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 867,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-slate-600 dark:text-slate-400\",\n                        children: \"Cost:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 870,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: selectedJob.cost ? `$${selectedJob.cost.toFixed(2)}` : 'Not quoted yet'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 871,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 869,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-slate-600 dark:text-slate-400\",\n                        children: \"Xerox Center:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 876,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: selectedJob.xeroxCenterName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 877,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 875,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-slate-600 dark:text-slate-400\",\n                        children: \"Created:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 880,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: new Date(selectedJob.created).toLocaleString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 881,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 879,\n                      columnNumber: 25\n                    }, this), selectedJob.estimatedCompletionTime && /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-slate-600 dark:text-slate-400\",\n                        children: \"Estimated Completion:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 885,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: new Date(selectedJob.estimatedCompletionTime).toLocaleString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 886,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 884,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 19\n              }, this), selectedJob.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6 p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-slate-600 dark:text-slate-400 mb-2\",\n                  children: \"Remarks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 898,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-slate-900 dark:text-white\",\n                  children: selectedJob.remarks\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 21\n              }, this), selectedJob.status === 'Quoted' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 text-yellow-800 dark:text-yellow-400\",\n                  children: [/*#__PURE__*/_jsxDEV(Info, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 907,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"Quote Available\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 908,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 906,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-yellow-700 dark:text-yellow-300 mt-1\",\n                  children: \"This job has been quoted. Please confirm to proceed with printing.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-end gap-3 p-6 border-t border-slate-200 dark:border-slate-700\",\n              children: [selectedJob.status === 'Quoted' && /*#__PURE__*/_jsxDEV(motion.button, {\n                onClick: () => {\n                  handleConfirmJob(selectedJob.id);\n                  setShowViewModal(false);\n                },\n                className: \"px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors\",\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Check, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 25\n                  }, this), \"Confirm Quote\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 929,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                onClick: () => setShowViewModal(false),\n                className: \"px-4 py-2 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\",\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                children: \"Close\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 918,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 802,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 793,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: showChatModal && selectedJob && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          exit: {\n            opacity: 0\n          },\n          className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\",\n          onClick: () => setShowChatModal(false),\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0.9,\n              opacity: 0\n            },\n            animate: {\n              scale: 1,\n              opacity: 1\n            },\n            exit: {\n              scale: 0.9,\n              opacity: 0\n            },\n            onClick: e => e.stopPropagation(),\n            className: \"w-full max-w-2xl bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-white/20 flex flex-col h-[600px]\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-2 bg-green-100 dark:bg-green-900/30 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                    className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 970,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 969,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-slate-900 dark:text-white\",\n                  children: [\"Chat - \", selectedJob.jobNumber]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 968,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowChatModal(false),\n                className: \"p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(X, {\n                  className: \"w-5 h-5 text-slate-600 dark:text-slate-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 980,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 967,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 p-6 overflow-y-auto\",\n              children: messages.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: messages.map(message => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 10\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: `flex ${message.isFromCurrentUser ? 'justify-end' : 'justify-start'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: cn(\"max-w-[70%] p-3 rounded-2xl\", message.isFromCurrentUser ? \"bg-blue-600 text-white rounded-br-md\" : \"bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white rounded-bl-md\"),\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm\",\n                      children: message.content\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1001,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: cn(\"text-xs mt-1\", message.isFromCurrentUser ? \"text-blue-100\" : \"text-slate-500 dark:text-slate-400\"),\n                      children: [message.senderName, \" - \", new Date(message.sentAt).toLocaleString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1002,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 27\n                  }, this)\n                }, message.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 989,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center justify-center h-full text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-16 h-16 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                    className: \"w-8 h-8 text-slate-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1017,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1016,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-slate-900 dark:text-white mb-2\",\n                  children: \"No messages yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-slate-600 dark:text-slate-300\",\n                  children: \"Start a conversation with the xerox center!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1022,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 985,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 border-t border-slate-200 dark:border-slate-700\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Type your message...\",\n                  value: chatMessage,\n                  onChange: e => setChatMessage(e.target.value),\n                  onKeyDown: e => e.key === 'Enter' && handleSendMessage(),\n                  className: \"flex-1 px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1032,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: handleSendMessage,\n                  disabled: !chatMessage.trim(),\n                  className: cn(\"px-4 py-2 rounded-lg transition-all duration-300\", chatMessage.trim() ? \"bg-blue-600 hover:bg-blue-700 text-white\" : \"bg-slate-300 dark:bg-slate-600 text-slate-500 dark:text-slate-400 cursor-not-allowed\"),\n                  whileHover: chatMessage.trim() ? {\n                    scale: 1.02\n                  } : {},\n                  whileTap: chatMessage.trim() ? {\n                    scale: 0.98\n                  } : {},\n                  children: /*#__PURE__*/_jsxDEV(Send, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1052,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1040,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1031,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1030,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 959,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 952,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 950,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 239,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentDashboard, \"iyxrshFhtGBBq300n8D2Z23lQFI=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = StudentDashboard;\nexport default StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "Upload", "FileText", "Clock", "CheckCircle", "DollarSign", "Download", "MessageCircle", "Info", "Check", "Star", "MapPin", "TrendingUp", "Activity", "Printer", "Eye", "Send", "X", "Filter", "Search", "Calendar", "useAuth", "useTheme", "printJobApi", "xeroxCenterApi", "fileUploadApi", "messageApi", "cn", "jsxDEV", "_jsxDEV", "StudentDashboard", "_s", "user", "printJobs", "setPrintJobs", "xeroxCenters", "setXeroxCenters", "showUploadModal", "setShowUploadModal", "showViewModal", "setShowViewModal", "showChatModal", "setShowChatModal", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "selectedFile", "setSelectedFile", "chatMessage", "setChatMessage", "messages", "setMessages", "uploadData", "setUploadData", "remarks", "preferredXeroxCenterId", "printType", "copies", "colorType", "paperSize", "fetchData", "printJobsResponse", "getStudentJobs", "data", "xeroxCentersResponse", "getAll", "error", "console", "getStatusBadge", "status", "statusConfig", "color", "icon", "config", "IconComponent", "div", "initial", "scale", "opacity", "animate", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getWorkloadColor", "pendingJobs", "handleFileUpload", "formData", "FormData", "append", "toString", "uploadFile", "handleViewJob", "job", "handleOpenChat", "response", "getJobMessages", "id", "handleDownloadFile", "jobId", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleConfirmJob", "<PERSON><PERSON>ob", "handleSendMessage", "trim", "sendMessage", "prev", "isDarkMode", "y", "transition", "duration", "h1", "x", "delay", "p", "username", "button", "onClick", "whileHover", "whileTap", "stagger<PERSON><PERSON><PERSON><PERSON>", "title", "value", "length", "bgColor", "textColor", "change", "changeType", "filter", "includes", "reduce", "sum", "cost", "toFixed", "map", "stat", "index", "jobNumber", "xeroxCenterName", "Date", "created", "toLocaleDateString", "center", "name", "location", "averageRating", "exit", "e", "stopPropagation", "type", "accept", "onChange", "files", "target", "min", "parseInt", "rows", "placeholder", "disabled", "toLocaleString", "estimatedCompletionTime", "message", "isFromCurrentUser", "content", "sender<PERSON>ame", "sentAt", "onKeyDown", "key", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/StudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Upload,\n  FileText,\n  Clock,\n  CheckCircle,\n  DollarSign,\n  Download,\n  MessageCircle,\n  Info,\n  Check,\n  Star,\n  MapPin,\n  TrendingUp,\n  Activity,\n  Users,\n  Printer,\n  Eye,\n  Send,\n  X,\n  Plus,\n  Filter,\n  Search,\n  Calendar,\n  BarChart3\n} from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../services/api';\nimport { cn } from '../lib/utils';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  xeroxCenterName: string;\n  created: string;\n}\n\ninterface XeroxCenter {\n  id: number;\n  name: string;\n  location: string;\n  pendingJobs: number;\n  averageRating: number;\n  isActive: boolean;\n}\n\nconst StudentDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState<any>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState<any[]>([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        // Fetch xerox centers\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        // Set empty arrays on error\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: Clock },\n      'UnderReview': { color: 'bg-blue-100 text-blue-800 border-blue-200', icon: Eye },\n      'Quoted': { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: DollarSign },\n      'WaitingConfirmation': { color: 'bg-orange-100 text-orange-800 border-orange-200', icon: Clock },\n      'Confirmed': { color: 'bg-indigo-100 text-indigo-800 border-indigo-200', icon: Check },\n      'InProgress': { color: 'bg-purple-100 text-purple-800 border-purple-200', icon: Activity },\n      'Completed': { color: 'bg-green-100 text-green-800 border-green-200', icon: CheckCircle },\n      'Delivered': { color: 'bg-emerald-100 text-emerald-800 border-emerald-200', icon: CheckCircle },\n      'Rejected': { color: 'bg-red-100 text-red-800 border-red-200', icon: X },\n      'Cancelled': { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: X }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || {\n      color: 'bg-gray-100 text-gray-800 border-gray-200',\n      icon: Info\n    };\n\n    const IconComponent = config.icon;\n\n    return (\n      <motion.div\n        initial={{ scale: 0.8, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        className={cn(\n          \"inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border\",\n          config.color\n        )}\n      >\n        <IconComponent className=\"w-3 h-3\" />\n        {status}\n      </motion.div>\n    );\n  };\n\n  const getWorkloadColor = (pendingJobs: number) => {\n    if (pendingJobs <= 5) return 'success';\n    if (pendingJobs <= 10) return 'warning';\n    return 'danger';\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n\n      await fileUploadApi.uploadFile(formData);\n\n      // Refresh print jobs after upload\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  const handleViewJob = (job: any) => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n\n  const handleOpenChat = async (job: any) => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      // Load messages for this job\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  const handleConfirmJob = async (jobId: number) => {\n    try {\n      await printJobApi.confirmJob(jobId);\n\n      // Refresh print jobs after confirmation\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n\n      // Add the new message to the list\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  const { isDarkMode } = useTheme();\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-8\">\n        {/* Header Section */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\"\n        >\n          <div className=\"space-y-2\">\n            <motion.h1\n              className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.2 }}\n            >\n              Student Dashboard\n            </motion.h1>\n            <motion.p\n              className=\"text-slate-600 dark:text-slate-300 text-lg\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.3 }}\n            >\n              Welcome back, <span className=\"font-semibold text-blue-600 dark:text-blue-400\">{user?.username}</span>!\n            </motion.p>\n          </div>\n\n          <motion.button\n            onClick={() => setShowUploadModal(true)}\n            className={cn(\n              \"group relative px-6 py-3 rounded-xl font-medium transition-all duration-300\",\n              \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\",\n              \"text-white shadow-lg hover:shadow-xl transform hover:scale-105\",\n              \"border border-white/20 backdrop-blur-sm\"\n            )}\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.4 }}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <div className=\"flex items-center gap-2\">\n              <Upload className=\"w-5 h-5\" />\n              <span>Upload Files</span>\n            </div>\n            <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-blue-600/20 to-purple-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n          </motion.button>\n        </motion.div>\n\n        {/* Statistics Cards */}\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5, staggerChildren: 0.1 }}\n        >\n          {[\n            {\n              title: \"Total Jobs\",\n              value: printJobs.length,\n              icon: FileText,\n              color: \"from-blue-500 to-blue-600\",\n              bgColor: \"bg-blue-50 dark:bg-blue-900/20\",\n              textColor: \"text-blue-600 dark:text-blue-400\",\n              change: \"+12%\",\n              changeType: \"positive\"\n            },\n            {\n              title: \"In Progress\",\n              value: printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length,\n              icon: Clock,\n              color: \"from-orange-500 to-orange-600\",\n              bgColor: \"bg-orange-50 dark:bg-orange-900/20\",\n              textColor: \"text-orange-600 dark:text-orange-400\",\n              change: \"+5%\",\n              changeType: \"positive\"\n            },\n            {\n              title: \"Completed\",\n              value: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n              icon: CheckCircle,\n              color: \"from-green-500 to-green-600\",\n              bgColor: \"bg-green-50 dark:bg-green-900/20\",\n              textColor: \"text-green-600 dark:text-green-400\",\n              change: \"+8%\",\n              changeType: \"positive\"\n            },\n            {\n              title: \"Total Spent\",\n              value: `$${printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)}`,\n              icon: DollarSign,\n              color: \"from-purple-500 to-purple-600\",\n              bgColor: \"bg-purple-50 dark:bg-purple-900/20\",\n              textColor: \"text-purple-600 dark:text-purple-400\",\n              change: \"+15%\",\n              changeType: \"positive\"\n            }\n          ].map((stat, index) => {\n            const IconComponent = stat.icon;\n            return (\n              <motion.div\n                key={stat.title}\n                initial={{ opacity: 0, y: 20, scale: 0.9 }}\n                animate={{ opacity: 1, y: 0, scale: 1 }}\n                transition={{ delay: 0.6 + index * 0.1 }}\n                whileHover={{ y: -5, scale: 1.02 }}\n                className={cn(\n                  \"relative p-6 rounded-2xl border border-white/20 backdrop-blur-sm\",\n                  \"bg-white/70 dark:bg-slate-800/70 shadow-lg hover:shadow-xl\",\n                  \"transition-all duration-300 group cursor-pointer\"\n                )}\n              >\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className={cn(\"p-3 rounded-xl\", stat.bgColor)}>\n                    <IconComponent className={cn(\"w-6 h-6\", stat.textColor)} />\n                  </div>\n                  <div className=\"flex items-center gap-1 text-sm\">\n                    <TrendingUp className=\"w-4 h-4 text-green-500\" />\n                    <span className=\"text-green-600 font-medium\">{stat.change}</span>\n                  </div>\n                </div>\n\n                <div className=\"space-y-1\">\n                  <p className=\"text-slate-600 dark:text-slate-300 text-sm font-medium\">\n                    {stat.title}\n                  </p>\n                  <p className=\"text-3xl font-bold text-slate-900 dark:text-white\">\n                    {stat.value}\n                  </p>\n                </div>\n\n                {/* Gradient overlay on hover */}\n                <div className={cn(\n                  \"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-10\",\n                  \"bg-gradient-to-br transition-opacity duration-300\",\n                  stat.color\n                )} />\n              </motion.div>\n            );\n          })}\n        </motion.div>\n\n        {/* Main Content Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Recent Jobs */}\n          <motion.div\n            className=\"lg:col-span-2\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 0.8 }}\n          >\n            <div className=\"bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl border border-white/20 shadow-lg\">\n              <div className=\"p-6 border-b border-slate-200 dark:border-slate-700\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n                      <FileText className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\n                    </div>\n                    <h3 className=\"text-xl font-semibold text-slate-900 dark:text-white\">\n                      Recent Print Jobs\n                    </h3>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <button className=\"p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\">\n                      <Filter className=\"w-4 h-4 text-slate-600 dark:text-slate-400\" />\n                    </button>\n                    <button className=\"p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\">\n                      <Search className=\"w-4 h-4 text-slate-600 dark:text-slate-400\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"p-6\">\n                {printJobs.length > 0 ? (\n                  <div className=\"space-y-4\">\n                    {printJobs.map((job, index) => (\n                      <motion.div\n                        key={job.id}\n                        initial={{ opacity: 0, y: 20 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        transition={{ delay: 0.9 + index * 0.1 }}\n                        className=\"group p-4 rounded-xl border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300 hover:shadow-md\"\n                      >\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center gap-4 flex-1\">\n                            <div className=\"p-2 bg-red-100 dark:bg-red-900/30 rounded-lg\">\n                              <FileText className=\"w-5 h-5 text-red-600 dark:text-red-400\" />\n                            </div>\n\n                            <div className=\"flex-1 min-w-0\">\n                              <div className=\"flex items-center gap-2 mb-1\">\n                                <span className=\"font-semibold text-slate-900 dark:text-white\">\n                                  {job.jobNumber}\n                                </span>\n                                {getStatusBadge(job.status)}\n                              </div>\n                              <p className=\"text-sm text-slate-600 dark:text-slate-300 truncate\">\n                                {job.fileName}\n                              </p>\n                              <div className=\"flex items-center gap-4 mt-2 text-xs text-slate-500 dark:text-slate-400\">\n                                <span className=\"flex items-center gap-1\">\n                                  <MapPin className=\"w-3 h-3\" />\n                                  {job.xeroxCenterName}\n                                </span>\n                                <span className=\"flex items-center gap-1\">\n                                  <DollarSign className=\"w-3 h-3\" />\n                                  {job.cost ? `$${job.cost.toFixed(2)}` : 'Not quoted'}\n                                </span>\n                                <span className=\"flex items-center gap-1\">\n                                  <Calendar className=\"w-3 h-3\" />\n                                  {new Date(job.created).toLocaleDateString()}\n                                </span>\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                            <motion.button\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                              onClick={() => handleDownloadFile(job.id, job.fileName)}\n                              className=\"p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors\"\n                              title=\"Download File\"\n                            >\n                              <Download className=\"w-4 h-4\" />\n                            </motion.button>\n\n                            <motion.button\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                              onClick={() => handleViewJob(job)}\n                              className=\"p-2 text-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\"\n                              title=\"View Details\"\n                            >\n                              <Info className=\"w-4 h-4\" />\n                            </motion.button>\n\n                            <motion.button\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                              onClick={() => handleOpenChat(job)}\n                              className=\"p-2 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-colors\"\n                              title=\"Chat\"\n                            >\n                              <MessageCircle className=\"w-4 h-4\" />\n                            </motion.button>\n\n                            {job.status === 'Quoted' && (\n                              <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={() => handleConfirmJob(job.id)}\n                                className=\"p-2 text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors\"\n                                title=\"Confirm Quote\"\n                              >\n                                <Check className=\"w-4 h-4\" />\n                              </motion.button>\n                            )}\n                          </div>\n                        </div>\n                      </motion.div>\n                    ))}\n                  </div>\n                ) : (\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    className=\"text-center py-12\"\n                  >\n                    <div className=\"w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                      <FileText className=\"w-8 h-8 text-blue-600 dark:text-blue-400\" />\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n                      No print jobs yet\n                    </h3>\n                    <p className=\"text-slate-600 dark:text-slate-300 mb-4\">\n                      Upload your first file to get started with printing!\n                    </p>\n                    <motion.button\n                      onClick={() => setShowUploadModal(true)}\n                      className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\"\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                    >\n                      Upload File\n                    </motion.button>\n                  </motion.div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Xerox Centers */}\n          <motion.div\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 1.0 }}\n          >\n            <div className=\"bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl border border-white/20 shadow-lg\">\n              <div className=\"p-6 border-b border-slate-200 dark:border-slate-700\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg\">\n                    <Printer className=\"w-5 h-5 text-purple-600 dark:text-purple-400\" />\n                  </div>\n                  <h3 className=\"text-xl font-semibold text-slate-900 dark:text-white\">\n                    Available Centers\n                  </h3>\n                </div>\n              </div>\n\n              <div className=\"p-6 space-y-4\">\n                {xeroxCenters.map((center, index) => (\n                  <motion.div\n                    key={center.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1.1 + index * 0.1 }}\n                    className=\"group p-4 rounded-xl border border-slate-200 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-300 hover:shadow-md\"\n                  >\n                    <div className=\"flex items-start justify-between mb-3\">\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-semibold text-slate-900 dark:text-white mb-1\">\n                          {center.name}\n                        </h4>\n                        <div className=\"flex items-center gap-1 text-sm text-slate-600 dark:text-slate-300 mb-2\">\n                          <MapPin className=\"w-3 h-3\" />\n                          {center.location}\n                        </div>\n                      </div>\n                      <div className={cn(\n                        \"px-2.5 py-1 rounded-full text-xs font-medium\",\n                        center.pendingJobs <= 5\n                          ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\n                          : center.pendingJobs <= 10\n                          ? \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400\"\n                          : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\n                      )}>\n                        {center.pendingJobs} jobs\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-1\">\n                        <Star className=\"w-4 h-4 text-yellow-500 fill-current\" />\n                        <span className=\"text-sm font-medium text-slate-700 dark:text-slate-300\">\n                          {center.averageRating.toFixed(1)}\n                        </span>\n                      </div>\n\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        className=\"px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors opacity-0 group-hover:opacity-100\"\n                      >\n                        Select Center\n                      </motion.button>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Upload Modal */}\n        <AnimatePresence>\n          {showUploadModal && (\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n              onClick={() => setShowUploadModal(false)}\n            >\n              <motion.div\n                initial={{ scale: 0.9, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                exit={{ scale: 0.9, opacity: 0 }}\n                onClick={(e) => e.stopPropagation()}\n                className=\"w-full max-w-2xl bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-white/20\"\n              >\n                {/* Header */}\n                <div className=\"flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n                      <Upload className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\n                    </div>\n                    <h2 className=\"text-xl font-semibold text-slate-900 dark:text-white\">\n                      Upload Files for Printing\n                    </h2>\n                  </div>\n                  <button\n                    onClick={() => setShowUploadModal(false)}\n                    className=\"p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\"\n                  >\n                    <X className=\"w-5 h-5 text-slate-600 dark:text-slate-400\" />\n                  </button>\n                </div>\n\n                {/* Body */}\n                <div className=\"p-6 space-y-6\">\n                  {/* File Upload */}\n                  <div className=\"space-y-2\">\n                    <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300\">\n                      Select File\n                    </label>\n                    <div className=\"relative\">\n                      <input\n                        type=\"file\"\n                        accept=\".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\"\n                        onChange={(e) => {\n                          const files = (e.target as HTMLInputElement).files;\n                          setSelectedFile(files ? files[0] : null);\n                        }}\n                        className=\"block w-full text-sm text-slate-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900/30 dark:file:text-blue-400\"\n                      />\n                    </div>\n                    <p className=\"text-xs text-slate-500 dark:text-slate-400\">\n                      Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\n                    </p>\n                  </div>\n\n                  {/* Form Grid */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div className=\"space-y-2\">\n                      <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300\">\n                        Print Type\n                      </label>\n                      <select\n                        value={uploadData.printType}\n                        onChange={(e) => setUploadData(prev => ({ ...prev, printType: e.target.value }))}\n                        className=\"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      >\n                        <option value=\"Print\">Print</option>\n                        <option value=\"Xerox\">Xerox</option>\n                        <option value=\"Binding\">Binding</option>\n                        <option value=\"Lamination\">Lamination</option>\n                      </select>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300\">\n                        Number of Copies\n                      </label>\n                      <input\n                        type=\"number\"\n                        min=\"1\"\n                        value={uploadData.copies}\n                        onChange={(e) => setUploadData(prev => ({ ...prev, copies: parseInt(e.target.value) }))}\n                        className=\"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      />\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300\">\n                        Color Type\n                      </label>\n                      <select\n                        value={uploadData.colorType}\n                        onChange={(e) => setUploadData(prev => ({ ...prev, colorType: e.target.value }))}\n                        className=\"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      >\n                        <option value=\"BlackWhite\">Black & White</option>\n                        <option value=\"Color\">Color</option>\n                      </select>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300\">\n                        Paper Size\n                      </label>\n                      <select\n                        value={uploadData.paperSize}\n                        onChange={(e) => setUploadData(prev => ({ ...prev, paperSize: e.target.value }))}\n                        className=\"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      >\n                        <option value=\"A4\">A4</option>\n                        <option value=\"A3\">A3</option>\n                        <option value=\"Letter\">Letter</option>\n                        <option value=\"Legal\">Legal</option>\n                      </select>\n                    </div>\n                  </div>\n\n                  {/* Xerox Center Selection */}\n                  <div className=\"space-y-2\">\n                    <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300\">\n                      Preferred Xerox Center\n                    </label>\n                    <select\n                      value={uploadData.preferredXeroxCenterId}\n                      onChange={(e) => setUploadData(prev => ({ ...prev, preferredXeroxCenterId: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    >\n                      <option value=\"\">Select a center (optional)</option>\n                      {xeroxCenters.map(center => (\n                        <option key={center.id} value={center.id}>\n                          {center.name} - {center.pendingJobs} pending jobs\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {/* Remarks */}\n                  <div className=\"space-y-2\">\n                    <label className=\"block text-sm font-medium text-slate-700 dark:text-slate-300\">\n                      Remarks\n                    </label>\n                    <textarea\n                      rows={3}\n                      placeholder=\"Any special instructions or remarks...\"\n                      value={uploadData.remarks}\n                      onChange={(e) => setUploadData(prev => ({ ...prev, remarks: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n                    />\n                  </div>\n                </div>\n\n                {/* Footer */}\n                <div className=\"flex items-center justify-end gap-3 p-6 border-t border-slate-200 dark:border-slate-700\">\n                  <motion.button\n                    onClick={() => setShowUploadModal(false)}\n                    className=\"px-4 py-2 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    Cancel\n                  </motion.button>\n                  <motion.button\n                    onClick={handleFileUpload}\n                    disabled={!selectedFile}\n                    className={cn(\n                      \"px-6 py-2 rounded-lg font-medium transition-all duration-300\",\n                      selectedFile\n                        ? \"bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl\"\n                        : \"bg-slate-300 dark:bg-slate-600 text-slate-500 dark:text-slate-400 cursor-not-allowed\"\n                    )}\n                    whileHover={selectedFile ? { scale: 1.02 } : {}}\n                    whileTap={selectedFile ? { scale: 0.98 } : {}}\n                  >\n                    <div className=\"flex items-center gap-2\">\n                      <Upload className=\"w-4 h-4\" />\n                      Upload File\n                    </div>\n                  </motion.button>\n                </div>\n              </motion.div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* View Job Details Modal */}\n        <AnimatePresence>\n          {showViewModal && selectedJob && (\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n              onClick={() => setShowViewModal(false)}\n            >\n              <motion.div\n                initial={{ scale: 0.9, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                exit={{ scale: 0.9, opacity: 0 }}\n                onClick={(e) => e.stopPropagation()}\n                className=\"w-full max-w-3xl bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-white/20\"\n              >\n                {/* Header */}\n                <div className=\"flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n                      <Eye className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\n                    </div>\n                    <h2 className=\"text-xl font-semibold text-slate-900 dark:text-white\">\n                      Job Details - {selectedJob.jobNumber}\n                    </h2>\n                  </div>\n                  <button\n                    onClick={() => setShowViewModal(false)}\n                    className=\"p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\"\n                  >\n                    <X className=\"w-5 h-5 text-slate-600 dark:text-slate-400\" />\n                  </button>\n                </div>\n\n                {/* Body */}\n                <div className=\"p-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n                    {/* File Information */}\n                    <div className=\"space-y-4\">\n                      <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white border-b border-slate-200 dark:border-slate-700 pb-2\">\n                        File Information\n                      </h3>\n                      <div className=\"space-y-3\">\n                        <div>\n                          <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">File Name:</span>\n                          <p className=\"text-slate-900 dark:text-white\">{selectedJob.fileName}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Print Type:</span>\n                          <p className=\"text-slate-900 dark:text-white\">{selectedJob.printType}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Copies:</span>\n                          <p className=\"text-slate-900 dark:text-white\">{selectedJob.copies}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Color Type:</span>\n                          <p className=\"text-slate-900 dark:text-white\">{selectedJob.colorType}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Paper Size:</span>\n                          <p className=\"text-slate-900 dark:text-white\">{selectedJob.paperSize}</p>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Job Information */}\n                    <div className=\"space-y-4\">\n                      <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white border-b border-slate-200 dark:border-slate-700 pb-2\">\n                        Job Information\n                      </h3>\n                      <div className=\"space-y-3\">\n                        <div>\n                          <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Status:</span>\n                          <div className=\"mt-1\">{getStatusBadge(selectedJob.status)}</div>\n                        </div>\n                        <div>\n                          <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Cost:</span>\n                          <p className=\"text-slate-900 dark:text-white\">\n                            {selectedJob.cost ? `$${selectedJob.cost.toFixed(2)}` : 'Not quoted yet'}\n                          </p>\n                        </div>\n                        <div>\n                          <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Xerox Center:</span>\n                          <p className=\"text-slate-900 dark:text-white\">{selectedJob.xeroxCenterName}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Created:</span>\n                          <p className=\"text-slate-900 dark:text-white\">{new Date(selectedJob.created).toLocaleString()}</p>\n                        </div>\n                        {selectedJob.estimatedCompletionTime && (\n                          <div>\n                            <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Estimated Completion:</span>\n                            <p className=\"text-slate-900 dark:text-white\">\n                              {new Date(selectedJob.estimatedCompletionTime).toLocaleString()}\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Remarks */}\n                  {selectedJob.remarks && (\n                    <div className=\"mt-6 p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg\">\n                      <h4 className=\"text-sm font-medium text-slate-600 dark:text-slate-400 mb-2\">Remarks</h4>\n                      <p className=\"text-slate-900 dark:text-white\">{selectedJob.remarks}</p>\n                    </div>\n                  )}\n\n                  {/* Quote Alert */}\n                  {selectedJob.status === 'Quoted' && (\n                    <div className=\"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\n                      <div className=\"flex items-center gap-2 text-yellow-800 dark:text-yellow-400\">\n                        <Info className=\"w-5 h-5\" />\n                        <span className=\"font-medium\">Quote Available</span>\n                      </div>\n                      <p className=\"text-yellow-700 dark:text-yellow-300 mt-1\">\n                        This job has been quoted. Please confirm to proceed with printing.\n                      </p>\n                    </div>\n                  )}\n                </div>\n\n                {/* Footer */}\n                <div className=\"flex items-center justify-end gap-3 p-6 border-t border-slate-200 dark:border-slate-700\">\n                  {selectedJob.status === 'Quoted' && (\n                    <motion.button\n                      onClick={() => {\n                        handleConfirmJob(selectedJob.id);\n                        setShowViewModal(false);\n                      }}\n                      className=\"px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors\"\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <div className=\"flex items-center gap-2\">\n                        <Check className=\"w-4 h-4\" />\n                        Confirm Quote\n                      </div>\n                    </motion.button>\n                  )}\n                  <motion.button\n                    onClick={() => setShowViewModal(false)}\n                    className=\"px-4 py-2 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    Close\n                  </motion.button>\n                </div>\n              </motion.div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* Chat Modal */}\n        <AnimatePresence>\n          {showChatModal && selectedJob && (\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n              onClick={() => setShowChatModal(false)}\n            >\n              <motion.div\n                initial={{ scale: 0.9, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                exit={{ scale: 0.9, opacity: 0 }}\n                onClick={(e) => e.stopPropagation()}\n                className=\"w-full max-w-2xl bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-white/20 flex flex-col h-[600px]\"\n              >\n                {/* Header */}\n                <div className=\"flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"p-2 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n                      <MessageCircle className=\"w-5 h-5 text-green-600 dark:text-green-400\" />\n                    </div>\n                    <h2 className=\"text-xl font-semibold text-slate-900 dark:text-white\">\n                      Chat - {selectedJob.jobNumber}\n                    </h2>\n                  </div>\n                  <button\n                    onClick={() => setShowChatModal(false)}\n                    className=\"p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors\"\n                  >\n                    <X className=\"w-5 h-5 text-slate-600 dark:text-slate-400\" />\n                  </button>\n                </div>\n\n                {/* Messages */}\n                <div className=\"flex-1 p-6 overflow-y-auto\">\n                  {messages.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {messages.map((message) => (\n                        <motion.div\n                          key={message.id}\n                          initial={{ opacity: 0, y: 10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          className={`flex ${message.isFromCurrentUser ? 'justify-end' : 'justify-start'}`}\n                        >\n                          <div className={cn(\n                            \"max-w-[70%] p-3 rounded-2xl\",\n                            message.isFromCurrentUser\n                              ? \"bg-blue-600 text-white rounded-br-md\"\n                              : \"bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white rounded-bl-md\"\n                          )}>\n                            <p className=\"text-sm\">{message.content}</p>\n                            <p className={cn(\n                              \"text-xs mt-1\",\n                              message.isFromCurrentUser\n                                ? \"text-blue-100\"\n                                : \"text-slate-500 dark:text-slate-400\"\n                            )}>\n                              {message.senderName} - {new Date(message.sentAt).toLocaleString()}\n                            </p>\n                          </div>\n                        </motion.div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"flex flex-col items-center justify-center h-full text-center\">\n                      <div className=\"w-16 h-16 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center mb-4\">\n                        <MessageCircle className=\"w-8 h-8 text-slate-400\" />\n                      </div>\n                      <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n                        No messages yet\n                      </h3>\n                      <p className=\"text-slate-600 dark:text-slate-300\">\n                        Start a conversation with the xerox center!\n                      </p>\n                    </div>\n                  )}\n                </div>\n\n                {/* Message Input */}\n                <div className=\"p-6 border-t border-slate-200 dark:border-slate-700\">\n                  <div className=\"flex gap-3\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"Type your message...\"\n                      value={chatMessage}\n                      onChange={(e) => setChatMessage(e.target.value)}\n                      onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}\n                      className=\"flex-1 px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    />\n                    <motion.button\n                      onClick={handleSendMessage}\n                      disabled={!chatMessage.trim()}\n                      className={cn(\n                        \"px-4 py-2 rounded-lg transition-all duration-300\",\n                        chatMessage.trim()\n                          ? \"bg-blue-600 hover:bg-blue-700 text-white\"\n                          : \"bg-slate-300 dark:bg-slate-600 text-slate-500 dark:text-slate-400 cursor-not-allowed\"\n                      )}\n                      whileHover={chatMessage.trim() ? { scale: 1.02 } : {}}\n                      whileTap={chatMessage.trim() ? { scale: 0.98 } : {}}\n                    >\n                      <Send className=\"w-4 h-4\" />\n                    </motion.button>\n                  </div>\n                </div>\n              </motion.div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </div>\n  );\n};\n\nexport default StudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,QAAQ,EACRC,aAAa,EACbC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,QAAQ,EAERC,OAAO,EACPC,GAAG,EACHC,IAAI,EACJC,CAAC,EAEDC,MAAM,EACNC,MAAM,EACNC,QAAQ,QAEH,cAAc;AACrB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,WAAW,EAAEC,cAAc,EAAEC,aAAa,EAAEC,UAAU,QAAQ,iBAAiB;AACxF,SAASC,EAAE,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsBlC,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC;IAC3CwD,OAAO,EAAE,EAAE;IACXC,sBAAsB,EAAE,EAAE;IAC1BC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF5D,SAAS,CAAC,MAAM;IACd,MAAM6D,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,iBAAiB,GAAG,MAAMrC,WAAW,CAACsC,cAAc,CAAC,CAAC;QAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;;QAEpC;QACA,MAAMC,oBAAoB,GAAG,MAAMvC,cAAc,CAACwC,MAAM,CAAC,CAAC;QAC1D5B,eAAe,CAAC2B,oBAAoB,CAACD,IAAI,CAAC;MAC5C,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QACA/B,YAAY,CAAC,EAAE,CAAC;QAChBE,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC;IAEDuB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,KAAK,EAAE,2CAA2C;QAAEC,IAAI,EAAEpE;MAAM,CAAC;MAChF,aAAa,EAAE;QAAEmE,KAAK,EAAE,2CAA2C;QAAEC,IAAI,EAAExD;MAAI,CAAC;MAChF,QAAQ,EAAE;QAAEuD,KAAK,EAAE,iDAAiD;QAAEC,IAAI,EAAElE;MAAW,CAAC;MACxF,qBAAqB,EAAE;QAAEiE,KAAK,EAAE,iDAAiD;QAAEC,IAAI,EAAEpE;MAAM,CAAC;MAChG,WAAW,EAAE;QAAEmE,KAAK,EAAE,iDAAiD;QAAEC,IAAI,EAAE9D;MAAM,CAAC;MACtF,YAAY,EAAE;QAAE6D,KAAK,EAAE,iDAAiD;QAAEC,IAAI,EAAE1D;MAAS,CAAC;MAC1F,WAAW,EAAE;QAAEyD,KAAK,EAAE,8CAA8C;QAAEC,IAAI,EAAEnE;MAAY,CAAC;MACzF,WAAW,EAAE;QAAEkE,KAAK,EAAE,oDAAoD;QAAEC,IAAI,EAAEnE;MAAY,CAAC;MAC/F,UAAU,EAAE;QAAEkE,KAAK,EAAE,wCAAwC;QAAEC,IAAI,EAAEtD;MAAE,CAAC;MACxE,WAAW,EAAE;QAAEqD,KAAK,EAAE,2CAA2C;QAAEC,IAAI,EAAEtD;MAAE;IAC7E,CAAC;IAED,MAAMuD,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAClEE,KAAK,EAAE,2CAA2C;MAClDC,IAAI,EAAE/D;IACR,CAAC;IAED,MAAMiE,aAAa,GAAGD,MAAM,CAACD,IAAI;IAEjC,oBACE1C,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;MACTC,OAAO,EAAE;QAAEC,KAAK,EAAE,GAAG;QAAEC,OAAO,EAAE;MAAE,CAAE;MACpCC,OAAO,EAAE;QAAEF,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAClCE,SAAS,EAAEpD,EAAE,CACX,sFAAsF,EACtF6C,MAAM,CAACF,KACT,CAAE;MAAAU,QAAA,gBAEFnD,OAAA,CAAC4C,aAAa;QAACM,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACpChB,MAAM;IAAA;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEjB,CAAC;EAED,MAAMC,gBAAgB,GAAIC,WAAmB,IAAK;IAChD,IAAIA,WAAW,IAAI,CAAC,EAAE,OAAO,SAAS;IACtC,IAAIA,WAAW,IAAI,EAAE,EAAE,OAAO,SAAS;IACvC,OAAO,QAAQ;EACjB,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC1C,YAAY,EAAE;IAEnB,IAAI;MACF,MAAM2C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE7C,YAAY,CAAC;MACrC2C,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEvC,UAAU,CAACE,OAAO,CAAC;MAC9CmC,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAEvC,UAAU,CAACG,sBAAsB,CAAC;MAC5EkC,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEvC,UAAU,CAACI,SAAS,CAAC;MAClDiC,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEvC,UAAU,CAACK,MAAM,CAACmC,QAAQ,CAAC,CAAC,CAAC;MACvDH,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEvC,UAAU,CAACM,SAAS,CAAC;MAClD+B,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEvC,UAAU,CAACO,SAAS,CAAC;MAElD,MAAMjC,aAAa,CAACmE,UAAU,CAACJ,QAAQ,CAAC;;MAExC;MACA,MAAM5B,iBAAiB,GAAG,MAAMrC,WAAW,CAACsC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;MAEpCxB,kBAAkB,CAAC,KAAK,CAAC;MACzBQ,eAAe,CAAC,IAAI,CAAC;MACrBM,aAAa,CAAC;QACZC,OAAO,EAAE,EAAE;QACXC,sBAAsB,EAAE,EAAE;QAC1BC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C;IACF;EACF,CAAC;EAED,MAAM4B,aAAa,GAAIC,GAAQ,IAAK;IAClClD,cAAc,CAACkD,GAAG,CAAC;IACnBtD,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMuD,cAAc,GAAG,MAAOD,GAAQ,IAAK;IACzC,IAAI;MACFlD,cAAc,CAACkD,GAAG,CAAC;MACnBpD,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAMsD,QAAQ,GAAG,MAAMtE,UAAU,CAACuE,cAAc,CAACH,GAAG,CAACI,EAAE,CAAC;MACxDhD,WAAW,CAAC8C,QAAQ,CAAClC,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/Cf,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;EAED,MAAMiD,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAEnB,QAAgB,KAAK;IACpE,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAMvE,aAAa,CAAC4E,YAAY,CAACD,KAAK,CAAC;;MAExD;MACA,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACP,QAAQ,CAAClC,IAAI,CAAC,CAAC;MACtC,MAAM0C,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAG/B,QAAQ;MACxB4B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;IACF;EACF,CAAC;EAED,MAAMqD,gBAAgB,GAAG,MAAOlB,KAAa,IAAK;IAChD,IAAI;MACF,MAAM7E,WAAW,CAACgG,UAAU,CAACnB,KAAK,CAAC;;MAEnC;MACA,MAAMxC,iBAAiB,GAAG,MAAMrC,WAAW,CAACsC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMuD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACzE,WAAW,CAAC0E,IAAI,CAAC,CAAC,IAAI,CAAC9E,WAAW,EAAE;IAEzC,IAAI;MACF,MAAMqD,QAAQ,GAAG,MAAMtE,UAAU,CAACgG,WAAW,CAAC/E,WAAW,CAACuD,EAAE,EAAEnD,WAAW,CAAC0E,IAAI,CAAC,CAAC,CAAC;;MAEjF;MACAvE,WAAW,CAACyE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE3B,QAAQ,CAAClC,IAAI,CAAC,CAAC;MAC7Cd,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAM;IAAE2D;EAAW,CAAC,GAAGtG,QAAQ,CAAC,CAAC;EAEjC,oBACEO,OAAA;IAAKkD,SAAS,EAAC,sIAAsI;IAAAC,QAAA,eACnJnD,OAAA;MAAKkD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1CnD,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;QACTC,OAAO,EAAE;UAAEE,OAAO,EAAE,CAAC;UAAEgD,CAAC,EAAE,CAAC;QAAG,CAAE;QAChC/C,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEgD,CAAC,EAAE;QAAE,CAAE;QAC9BC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BhD,SAAS,EAAC,oEAAoE;QAAAC,QAAA,gBAE9EnD,OAAA;UAAKkD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnD,OAAA,CAAC9B,MAAM,CAACiI,EAAE;YACRjD,SAAS,EAAC,8GAA8G;YACxHJ,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEoD,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCnD,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEoD,CAAC,EAAE;YAAE,CAAE;YAC9BH,UAAU,EAAE;cAAEI,KAAK,EAAE;YAAI,CAAE;YAAAlD,QAAA,EAC5B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZvD,OAAA,CAAC9B,MAAM,CAACoI,CAAC;YACPpD,SAAS,EAAC,4CAA4C;YACtDJ,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEoD,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCnD,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEoD,CAAC,EAAE;YAAE,CAAE;YAC9BH,UAAU,EAAE;cAAEI,KAAK,EAAE;YAAI,CAAE;YAAAlD,QAAA,GAC5B,gBACe,eAAAnD,OAAA;cAAMkD,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAAEhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoG;YAAQ;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,KACxG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENvD,OAAA,CAAC9B,MAAM,CAACsI,MAAM;UACZC,OAAO,EAAEA,CAAA,KAAMhG,kBAAkB,CAAC,IAAI,CAAE;UACxCyC,SAAS,EAAEpD,EAAE,CACX,6EAA6E,EAC7E,sFAAsF,EACtF,gEAAgE,EAChE,yCACF,CAAE;UACFgD,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,KAAK,EAAE;UAAI,CAAE;UACpCE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,KAAK,EAAE;UAAE,CAAE;UAClCkD,UAAU,EAAE;YAAEI,KAAK,EAAE;UAAI,CAAE;UAC3BK,UAAU,EAAE;YAAE3D,KAAK,EAAE;UAAK,CAAE;UAC5B4D,QAAQ,EAAE;YAAE5D,KAAK,EAAE;UAAK,CAAE;UAAAI,QAAA,gBAE1BnD,OAAA;YAAKkD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCnD,OAAA,CAAC5B,MAAM;cAAC8E,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BvD,OAAA;cAAAmD,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACNvD,OAAA;YAAKkD,SAAS,EAAC;UAAkJ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGbvD,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;QACTK,SAAS,EAAC,sDAAsD;QAChEJ,OAAO,EAAE;UAAEE,OAAO,EAAE,CAAC;UAAEgD,CAAC,EAAE;QAAG,CAAE;QAC/B/C,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEgD,CAAC,EAAE;QAAE,CAAE;QAC9BC,UAAU,EAAE;UAAEI,KAAK,EAAE,GAAG;UAAEO,eAAe,EAAE;QAAI,CAAE;QAAAzD,QAAA,EAEhD,CACC;UACE0D,KAAK,EAAE,YAAY;UACnBC,KAAK,EAAE1G,SAAS,CAAC2G,MAAM;UACvBrE,IAAI,EAAErE,QAAQ;UACdoE,KAAK,EAAE,2BAA2B;UAClCuE,OAAO,EAAE,gCAAgC;UACzCC,SAAS,EAAE,kCAAkC;UAC7CC,MAAM,EAAE,MAAM;UACdC,UAAU,EAAE;QACd,CAAC,EACD;UACEN,KAAK,EAAE,aAAa;UACpBC,KAAK,EAAE1G,SAAS,CAACgH,MAAM,CAACnD,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAACoD,QAAQ,CAACpD,GAAG,CAAC1B,MAAM,CAAC,CAAC,CAACwE,MAAM;UACtGrE,IAAI,EAAEpE,KAAK;UACXmE,KAAK,EAAE,+BAA+B;UACtCuE,OAAO,EAAE,oCAAoC;UAC7CC,SAAS,EAAE,sCAAsC;UACjDC,MAAM,EAAE,KAAK;UACbC,UAAU,EAAE;QACd,CAAC,EACD;UACEN,KAAK,EAAE,WAAW;UAClBC,KAAK,EAAE1G,SAAS,CAACgH,MAAM,CAACnD,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACoD,QAAQ,CAACpD,GAAG,CAAC1B,MAAM,CAAC,CAAC,CAACwE,MAAM;UACtFrE,IAAI,EAAEnE,WAAW;UACjBkE,KAAK,EAAE,6BAA6B;UACpCuE,OAAO,EAAE,kCAAkC;UAC3CC,SAAS,EAAE,oCAAoC;UAC/CC,MAAM,EAAE,KAAK;UACbC,UAAU,EAAE;QACd,CAAC,EACD;UACEN,KAAK,EAAE,aAAa;UACpBC,KAAK,EAAE,IAAI1G,SAAS,CAACkH,MAAM,CAAC,CAACC,GAAG,EAAEtD,GAAG,KAAKsD,GAAG,IAAItD,GAAG,CAACuD,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE;UAChF/E,IAAI,EAAElE,UAAU;UAChBiE,KAAK,EAAE,+BAA+B;UACtCuE,OAAO,EAAE,oCAAoC;UAC7CC,SAAS,EAAE,sCAAsC;UACjDC,MAAM,EAAE,MAAM;UACdC,UAAU,EAAE;QACd,CAAC,CACF,CAACO,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;UACrB,MAAMhF,aAAa,GAAG+E,IAAI,CAACjF,IAAI;UAC/B,oBACE1C,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;YAETC,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEgD,CAAC,EAAE,EAAE;cAAEjD,KAAK,EAAE;YAAI,CAAE;YAC3CE,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEgD,CAAC,EAAE,CAAC;cAAEjD,KAAK,EAAE;YAAE,CAAE;YACxCkD,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG,GAAGuB,KAAK,GAAG;YAAI,CAAE;YACzClB,UAAU,EAAE;cAAEV,CAAC,EAAE,CAAC,CAAC;cAAEjD,KAAK,EAAE;YAAK,CAAE;YACnCG,SAAS,EAAEpD,EAAE,CACX,kEAAkE,EAClE,4DAA4D,EAC5D,kDACF,CAAE;YAAAqD,QAAA,gBAEFnD,OAAA;cAAKkD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDnD,OAAA;gBAAKkD,SAAS,EAAEpD,EAAE,CAAC,gBAAgB,EAAE6H,IAAI,CAACX,OAAO,CAAE;gBAAA7D,QAAA,eACjDnD,OAAA,CAAC4C,aAAa;kBAACM,SAAS,EAAEpD,EAAE,CAAC,SAAS,EAAE6H,IAAI,CAACV,SAAS;gBAAE;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACNvD,OAAA;gBAAKkD,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CnD,OAAA,CAACjB,UAAU;kBAACmE,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDvD,OAAA;kBAAMkD,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAEwE,IAAI,CAACT;gBAAM;kBAAA9D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvD,OAAA;cAAKkD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBnD,OAAA;gBAAGkD,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,EAClEwE,IAAI,CAACd;cAAK;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACJvD,OAAA;gBAAGkD,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC7DwE,IAAI,CAACb;cAAK;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNvD,OAAA;cAAKkD,SAAS,EAAEpD,EAAE,CAChB,+DAA+D,EAC/D,mDAAmD,EACnD6H,IAAI,CAAClF,KACP;YAAE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAnCAoE,IAAI,CAACd,KAAK;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoCL,CAAC;QAEjB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGbvD,OAAA;QAAKkD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDnD,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;UACTK,SAAS,EAAC,eAAe;UACzBJ,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAEoD,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCnD,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEoD,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEI,KAAK,EAAE;UAAI,CAAE;UAAAlD,QAAA,eAE3BnD,OAAA;YAAKkD,SAAS,EAAC,gGAAgG;YAAAC,QAAA,gBAC7GnD,OAAA;cAAKkD,SAAS,EAAC,qDAAqD;cAAAC,QAAA,eAClEnD,OAAA;gBAAKkD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDnD,OAAA;kBAAKkD,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCnD,OAAA;oBAAKkD,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,eAC7DnD,OAAA,CAAC3B,QAAQ;sBAAC6E,SAAS,EAAC;oBAA0C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACNvD,OAAA;oBAAIkD,SAAS,EAAC,sDAAsD;oBAAAC,QAAA,EAAC;kBAErE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNvD,OAAA;kBAAKkD,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCnD,OAAA;oBAAQkD,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,eAC7FnD,OAAA,CAACX,MAAM;sBAAC6D,SAAS,EAAC;oBAA4C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACTvD,OAAA;oBAAQkD,SAAS,EAAC,6EAA6E;oBAAAC,QAAA,eAC7FnD,OAAA,CAACV,MAAM;sBAAC4D,SAAS,EAAC;oBAA4C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvD,OAAA;cAAKkD,SAAS,EAAC,KAAK;cAAAC,QAAA,EACjB/C,SAAS,CAAC2G,MAAM,GAAG,CAAC,gBACnB/G,OAAA;gBAAKkD,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvB/C,SAAS,CAACsH,GAAG,CAAC,CAACzD,GAAG,EAAE2D,KAAK,kBACxB5H,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;kBAETC,OAAO,EAAE;oBAAEE,OAAO,EAAE,CAAC;oBAAEgD,CAAC,EAAE;kBAAG,CAAE;kBAC/B/C,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEgD,CAAC,EAAE;kBAAE,CAAE;kBAC9BC,UAAU,EAAE;oBAAEI,KAAK,EAAE,GAAG,GAAGuB,KAAK,GAAG;kBAAI,CAAE;kBACzC1E,SAAS,EAAC,iKAAiK;kBAAAC,QAAA,eAE3KnD,OAAA;oBAAKkD,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDnD,OAAA;sBAAKkD,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBAC7CnD,OAAA;wBAAKkD,SAAS,EAAC,8CAA8C;wBAAAC,QAAA,eAC3DnD,OAAA,CAAC3B,QAAQ;0BAAC6E,SAAS,EAAC;wBAAwC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5D,CAAC,eAENvD,OAAA;wBAAKkD,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,gBAC7BnD,OAAA;0BAAKkD,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,gBAC3CnD,OAAA;4BAAMkD,SAAS,EAAC,8CAA8C;4BAAAC,QAAA,EAC3Dc,GAAG,CAAC4D;0BAAS;4BAAAzE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,EACNjB,cAAc,CAAC2B,GAAG,CAAC1B,MAAM,CAAC;wBAAA;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC,eACNvD,OAAA;0BAAGkD,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,EAC/Dc,GAAG,CAACb;wBAAQ;0BAAAA,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACJvD,OAAA;0BAAKkD,SAAS,EAAC,yEAAyE;0BAAAC,QAAA,gBACtFnD,OAAA;4BAAMkD,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,gBACvCnD,OAAA,CAAClB,MAAM;8BAACoE,SAAS,EAAC;4BAAS;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC7BU,GAAG,CAAC6D,eAAe;0BAAA;4BAAA1E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChB,CAAC,eACPvD,OAAA;4BAAMkD,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,gBACvCnD,OAAA,CAACxB,UAAU;8BAAC0E,SAAS,EAAC;4BAAS;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACjCU,GAAG,CAACuD,IAAI,GAAG,IAAIvD,GAAG,CAACuD,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,YAAY;0BAAA;4BAAArE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChD,CAAC,eACPvD,OAAA;4BAAMkD,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,gBACvCnD,OAAA,CAACT,QAAQ;8BAAC2D,SAAS,EAAC;4BAAS;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC/B,IAAIwE,IAAI,CAAC9D,GAAG,CAAC+D,OAAO,CAAC,CAACC,kBAAkB,CAAC,CAAC;0BAAA;4BAAA7E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENvD,OAAA;sBAAKkD,SAAS,EAAC,2FAA2F;sBAAAC,QAAA,gBACxGnD,OAAA,CAAC9B,MAAM,CAACsI,MAAM;wBACZE,UAAU,EAAE;0BAAE3D,KAAK,EAAE;wBAAK,CAAE;wBAC5B4D,QAAQ,EAAE;0BAAE5D,KAAK,EAAE;wBAAK,CAAE;wBAC1B0D,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAACL,GAAG,CAACI,EAAE,EAAEJ,GAAG,CAACb,QAAQ,CAAE;wBACxDF,SAAS,EAAC,4FAA4F;wBACtG2D,KAAK,EAAC,eAAe;wBAAA1D,QAAA,eAErBnD,OAAA,CAACvB,QAAQ;0BAACyE,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eAEhBvD,OAAA,CAAC9B,MAAM,CAACsI,MAAM;wBACZE,UAAU,EAAE;0BAAE3D,KAAK,EAAE;wBAAK,CAAE;wBAC5B4D,QAAQ,EAAE;0BAAE5D,KAAK,EAAE;wBAAK,CAAE;wBAC1B0D,OAAO,EAAEA,CAAA,KAAMzC,aAAa,CAACC,GAAG,CAAE;wBAClCf,SAAS,EAAC,4FAA4F;wBACtG2D,KAAK,EAAC,cAAc;wBAAA1D,QAAA,eAEpBnD,OAAA,CAACrB,IAAI;0BAACuE,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf,CAAC,eAEhBvD,OAAA,CAAC9B,MAAM,CAACsI,MAAM;wBACZE,UAAU,EAAE;0BAAE3D,KAAK,EAAE;wBAAK,CAAE;wBAC5B4D,QAAQ,EAAE;0BAAE5D,KAAK,EAAE;wBAAK,CAAE;wBAC1B0D,OAAO,EAAEA,CAAA,KAAMvC,cAAc,CAACD,GAAG,CAAE;wBACnCf,SAAS,EAAC,+FAA+F;wBACzG2D,KAAK,EAAC,MAAM;wBAAA1D,QAAA,eAEZnD,OAAA,CAACtB,aAAa;0BAACwE,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,EAEfU,GAAG,CAAC1B,MAAM,KAAK,QAAQ,iBACtBvC,OAAA,CAAC9B,MAAM,CAACsI,MAAM;wBACZE,UAAU,EAAE;0BAAE3D,KAAK,EAAE;wBAAK,CAAE;wBAC5B4D,QAAQ,EAAE;0BAAE5D,KAAK,EAAE;wBAAK,CAAE;wBAC1B0D,OAAO,EAAEA,CAAA,KAAMhB,gBAAgB,CAACxB,GAAG,CAACI,EAAE,CAAE;wBACxCnB,SAAS,EAAC,6EAA6E;wBACvF2D,KAAK,EAAC,eAAe;wBAAA1D,QAAA,eAErBnD,OAAA,CAACpB,KAAK;0BAACsE,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChB,CAChB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAlFDU,GAAG,CAACI,EAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmFD,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENvD,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;gBACTC,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAED,KAAK,EAAE;gBAAI,CAAE;gBACpCE,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAED,KAAK,EAAE;gBAAE,CAAE;gBAClCG,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAE7BnD,OAAA;kBAAKkD,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,eACnHnD,OAAA,CAAC3B,QAAQ;oBAAC6E,SAAS,EAAC;kBAA0C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACNvD,OAAA;kBAAIkD,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLvD,OAAA;kBAAGkD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAEvD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJvD,OAAA,CAAC9B,MAAM,CAACsI,MAAM;kBACZC,OAAO,EAAEA,CAAA,KAAMhG,kBAAkB,CAAC,IAAI,CAAE;kBACxCyC,SAAS,EAAC,iFAAiF;kBAC3FwD,UAAU,EAAE;oBAAE3D,KAAK,EAAE;kBAAK,CAAE;kBAC5B4D,QAAQ,EAAE;oBAAE5D,KAAK,EAAE;kBAAK,CAAE;kBAAAI,QAAA,EAC3B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbvD,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;UACTC,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAEoD,CAAC,EAAE;UAAG,CAAE;UAC/BnD,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEoD,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEI,KAAK,EAAE;UAAI,CAAE;UAAAlD,QAAA,eAE3BnD,OAAA;YAAKkD,SAAS,EAAC,gGAAgG;YAAAC,QAAA,gBAC7GnD,OAAA;cAAKkD,SAAS,EAAC,qDAAqD;cAAAC,QAAA,eAClEnD,OAAA;gBAAKkD,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCnD,OAAA;kBAAKkD,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,eACjEnD,OAAA,CAACf,OAAO;oBAACiE,SAAS,EAAC;kBAA8C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC,eACNvD,OAAA;kBAAIkD,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,EAAC;gBAErE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvD,OAAA;cAAKkD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3B7C,YAAY,CAACoH,GAAG,CAAC,CAACQ,MAAM,EAAEN,KAAK,kBAC9B5H,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;gBAETC,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAEgD,CAAC,EAAE;gBAAG,CAAE;gBAC/B/C,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEgD,CAAC,EAAE;gBAAE,CAAE;gBAC9BC,UAAU,EAAE;kBAAEI,KAAK,EAAE,GAAG,GAAGuB,KAAK,GAAG;gBAAI,CAAE;gBACzC1E,SAAS,EAAC,qKAAqK;gBAAAC,QAAA,gBAE/KnD,OAAA;kBAAKkD,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDnD,OAAA;oBAAKkD,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBnD,OAAA;sBAAIkD,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,EAC9D+E,MAAM,CAACC;oBAAI;sBAAA/E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACLvD,OAAA;sBAAKkD,SAAS,EAAC,yEAAyE;sBAAAC,QAAA,gBACtFnD,OAAA,CAAClB,MAAM;wBAACoE,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EAC7B2E,MAAM,CAACE,QAAQ;oBAAA;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA;oBAAKkD,SAAS,EAAEpD,EAAE,CAChB,8CAA8C,EAC9CoI,MAAM,CAACzE,WAAW,IAAI,CAAC,GACnB,sEAAsE,GACtEyE,MAAM,CAACzE,WAAW,IAAI,EAAE,GACxB,0EAA0E,GAC1E,8DACN,CAAE;oBAAAN,QAAA,GACC+E,MAAM,CAACzE,WAAW,EAAC,OACtB;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENvD,OAAA;kBAAKkD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDnD,OAAA;oBAAKkD,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCnD,OAAA,CAACnB,IAAI;sBAACqE,SAAS,EAAC;oBAAsC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACzDvD,OAAA;sBAAMkD,SAAS,EAAC,wDAAwD;sBAAAC,QAAA,EACrE+E,MAAM,CAACG,aAAa,CAACZ,OAAO,CAAC,CAAC;oBAAC;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAENvD,OAAA,CAAC9B,MAAM,CAACsI,MAAM;oBACZE,UAAU,EAAE;sBAAE3D,KAAK,EAAE;oBAAK,CAAE;oBAC5B4D,QAAQ,EAAE;sBAAE5D,KAAK,EAAE;oBAAK,CAAE;oBAC1BG,SAAS,EAAC,2IAA2I;oBAAAC,QAAA,EACtJ;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA,GA3CD2E,MAAM,CAAC7D,EAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4CJ,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNvD,OAAA,CAAC7B,eAAe;QAAAgF,QAAA,EACb3C,eAAe,iBACdR,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;UACTC,OAAO,EAAE;YAAEE,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBsF,IAAI,EAAE;YAAEtF,OAAO,EAAE;UAAE,CAAE;UACrBE,SAAS,EAAC,sFAAsF;UAChGuD,OAAO,EAAEA,CAAA,KAAMhG,kBAAkB,CAAC,KAAK,CAAE;UAAA0C,QAAA,eAEzCnD,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;YACTC,OAAO,EAAE;cAAEC,KAAK,EAAE,GAAG;cAAEC,OAAO,EAAE;YAAE,CAAE;YACpCC,OAAO,EAAE;cAAEF,KAAK,EAAE,CAAC;cAAEC,OAAO,EAAE;YAAE,CAAE;YAClCsF,IAAI,EAAE;cAAEvF,KAAK,EAAE,GAAG;cAAEC,OAAO,EAAE;YAAE,CAAE;YACjCyD,OAAO,EAAG8B,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;YACpCtF,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBAGrGnD,OAAA;cAAKkD,SAAS,EAAC,uFAAuF;cAAAC,QAAA,gBACpGnD,OAAA;gBAAKkD,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCnD,OAAA;kBAAKkD,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,eAC7DnD,OAAA,CAAC5B,MAAM;oBAAC8E,SAAS,EAAC;kBAA0C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eACNvD,OAAA;kBAAIkD,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,EAAC;gBAErE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNvD,OAAA;gBACEyG,OAAO,EAAEA,CAAA,KAAMhG,kBAAkB,CAAC,KAAK,CAAE;gBACzCyC,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,eAEvFnD,OAAA,CAACZ,CAAC;kBAAC8D,SAAS,EAAC;gBAA4C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNvD,OAAA;cAAKkD,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAE5BnD,OAAA;gBAAKkD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBnD,OAAA;kBAAOkD,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,EAAC;gBAEhF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRvD,OAAA;kBAAKkD,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACvBnD,OAAA;oBACEyI,IAAI,EAAC,MAAM;oBACXC,MAAM,EAAC,2CAA2C;oBAClDC,QAAQ,EAAGJ,CAAC,IAAK;sBACf,MAAMK,KAAK,GAAIL,CAAC,CAACM,MAAM,CAAsBD,KAAK;sBAClD3H,eAAe,CAAC2H,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;oBAC1C,CAAE;oBACF1F,SAAS,EAAC;kBAA0O;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNvD,OAAA;kBAAGkD,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAE1D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNvD,OAAA;gBAAKkD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDnD,OAAA;kBAAKkD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBnD,OAAA;oBAAOkD,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EAAC;kBAEhF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRvD,OAAA;oBACE8G,KAAK,EAAExF,UAAU,CAACI,SAAU;oBAC5BiH,QAAQ,EAAGJ,CAAC,IAAKhH,aAAa,CAACuE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEpE,SAAS,EAAE6G,CAAC,CAACM,MAAM,CAAC/B;oBAAM,CAAC,CAAC,CAAE;oBACjF5D,SAAS,EAAC,+LAA+L;oBAAAC,QAAA,gBAEzMnD,OAAA;sBAAQ8G,KAAK,EAAC,OAAO;sBAAA3D,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCvD,OAAA;sBAAQ8G,KAAK,EAAC,OAAO;sBAAA3D,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCvD,OAAA;sBAAQ8G,KAAK,EAAC,SAAS;sBAAA3D,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCvD,OAAA;sBAAQ8G,KAAK,EAAC,YAAY;sBAAA3D,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAENvD,OAAA;kBAAKkD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBnD,OAAA;oBAAOkD,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EAAC;kBAEhF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRvD,OAAA;oBACEyI,IAAI,EAAC,QAAQ;oBACbK,GAAG,EAAC,GAAG;oBACPhC,KAAK,EAAExF,UAAU,CAACK,MAAO;oBACzBgH,QAAQ,EAAGJ,CAAC,IAAKhH,aAAa,CAACuE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEnE,MAAM,EAAEoH,QAAQ,CAACR,CAAC,CAACM,MAAM,CAAC/B,KAAK;oBAAE,CAAC,CAAC,CAAE;oBACxF5D,SAAS,EAAC;kBAA+L;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1M,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENvD,OAAA;kBAAKkD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBnD,OAAA;oBAAOkD,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EAAC;kBAEhF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRvD,OAAA;oBACE8G,KAAK,EAAExF,UAAU,CAACM,SAAU;oBAC5B+G,QAAQ,EAAGJ,CAAC,IAAKhH,aAAa,CAACuE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAElE,SAAS,EAAE2G,CAAC,CAACM,MAAM,CAAC/B;oBAAM,CAAC,CAAC,CAAE;oBACjF5D,SAAS,EAAC,+LAA+L;oBAAAC,QAAA,gBAEzMnD,OAAA;sBAAQ8G,KAAK,EAAC,YAAY;sBAAA3D,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjDvD,OAAA;sBAAQ8G,KAAK,EAAC,OAAO;sBAAA3D,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAENvD,OAAA;kBAAKkD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBnD,OAAA;oBAAOkD,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EAAC;kBAEhF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRvD,OAAA;oBACE8G,KAAK,EAAExF,UAAU,CAACO,SAAU;oBAC5B8G,QAAQ,EAAGJ,CAAC,IAAKhH,aAAa,CAACuE,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEjE,SAAS,EAAE0G,CAAC,CAACM,MAAM,CAAC/B;oBAAM,CAAC,CAAC,CAAE;oBACjF5D,SAAS,EAAC,+LAA+L;oBAAAC,QAAA,gBAEzMnD,OAAA;sBAAQ8G,KAAK,EAAC,IAAI;sBAAA3D,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9BvD,OAAA;sBAAQ8G,KAAK,EAAC,IAAI;sBAAA3D,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9BvD,OAAA;sBAAQ8G,KAAK,EAAC,QAAQ;sBAAA3D,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCvD,OAAA;sBAAQ8G,KAAK,EAAC,OAAO;sBAAA3D,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNvD,OAAA;gBAAKkD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBnD,OAAA;kBAAOkD,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,EAAC;gBAEhF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRvD,OAAA;kBACE8G,KAAK,EAAExF,UAAU,CAACG,sBAAuB;kBACzCkH,QAAQ,EAAGJ,CAAC,IAAKhH,aAAa,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAErE,sBAAsB,EAAE8G,CAAC,CAACM,MAAM,CAAC/B;kBAAM,CAAC,CAAC,CAAE;kBAC9F5D,SAAS,EAAC,+LAA+L;kBAAAC,QAAA,gBAEzMnD,OAAA;oBAAQ8G,KAAK,EAAC,EAAE;oBAAA3D,QAAA,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACnDjD,YAAY,CAACoH,GAAG,CAACQ,MAAM,iBACtBlI,OAAA;oBAAwB8G,KAAK,EAAEoB,MAAM,CAAC7D,EAAG;oBAAAlB,QAAA,GACtC+E,MAAM,CAACC,IAAI,EAAC,KAAG,EAACD,MAAM,CAACzE,WAAW,EAAC,eACtC;kBAAA,GAFayE,MAAM,CAAC7D,EAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEd,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGNvD,OAAA;gBAAKkD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBnD,OAAA;kBAAOkD,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,EAAC;gBAEhF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRvD,OAAA;kBACEgJ,IAAI,EAAE,CAAE;kBACRC,WAAW,EAAC,wCAAwC;kBACpDnC,KAAK,EAAExF,UAAU,CAACE,OAAQ;kBAC1BmH,QAAQ,EAAGJ,CAAC,IAAKhH,aAAa,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEtE,OAAO,EAAE+G,CAAC,CAACM,MAAM,CAAC/B;kBAAM,CAAC,CAAC,CAAE;kBAC/E5D,SAAS,EAAC;gBAA2M;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNvD,OAAA;cAAKkD,SAAS,EAAC,yFAAyF;cAAAC,QAAA,gBACtGnD,OAAA,CAAC9B,MAAM,CAACsI,MAAM;gBACZC,OAAO,EAAEA,CAAA,KAAMhG,kBAAkB,CAAC,KAAK,CAAE;gBACzCyC,SAAS,EAAC,sHAAsH;gBAChIwD,UAAU,EAAE;kBAAE3D,KAAK,EAAE;gBAAK,CAAE;gBAC5B4D,QAAQ,EAAE;kBAAE5D,KAAK,EAAE;gBAAK,CAAE;gBAAAI,QAAA,EAC3B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAChBvD,OAAA,CAAC9B,MAAM,CAACsI,MAAM;gBACZC,OAAO,EAAE/C,gBAAiB;gBAC1BwF,QAAQ,EAAE,CAAClI,YAAa;gBACxBkC,SAAS,EAAEpD,EAAE,CACX,8DAA8D,EAC9DkB,YAAY,GACR,oEAAoE,GACpE,sFACN,CAAE;gBACF0F,UAAU,EAAE1F,YAAY,GAAG;kBAAE+B,KAAK,EAAE;gBAAK,CAAC,GAAG,CAAC,CAAE;gBAChD4D,QAAQ,EAAE3F,YAAY,GAAG;kBAAE+B,KAAK,EAAE;gBAAK,CAAC,GAAG,CAAC,CAAE;gBAAAI,QAAA,eAE9CnD,OAAA;kBAAKkD,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCnD,OAAA,CAAC5B,MAAM;oBAAC8E,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEhC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC,eAGlBvD,OAAA,CAAC7B,eAAe;QAAAgF,QAAA,EACbzC,aAAa,IAAII,WAAW,iBAC3Bd,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;UACTC,OAAO,EAAE;YAAEE,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBsF,IAAI,EAAE;YAAEtF,OAAO,EAAE;UAAE,CAAE;UACrBE,SAAS,EAAC,sFAAsF;UAChGuD,OAAO,EAAEA,CAAA,KAAM9F,gBAAgB,CAAC,KAAK,CAAE;UAAAwC,QAAA,eAEvCnD,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;YACTC,OAAO,EAAE;cAAEC,KAAK,EAAE,GAAG;cAAEC,OAAO,EAAE;YAAE,CAAE;YACpCC,OAAO,EAAE;cAAEF,KAAK,EAAE,CAAC;cAAEC,OAAO,EAAE;YAAE,CAAE;YAClCsF,IAAI,EAAE;cAAEvF,KAAK,EAAE,GAAG;cAAEC,OAAO,EAAE;YAAE,CAAE;YACjCyD,OAAO,EAAG8B,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;YACpCtF,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBAGrGnD,OAAA;cAAKkD,SAAS,EAAC,uFAAuF;cAAAC,QAAA,gBACpGnD,OAAA;gBAAKkD,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCnD,OAAA;kBAAKkD,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,eAC7DnD,OAAA,CAACd,GAAG;oBAACgE,SAAS,EAAC;kBAA0C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACNvD,OAAA;kBAAIkD,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,GAAC,gBACrD,EAACrC,WAAW,CAAC+G,SAAS;gBAAA;kBAAAzE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNvD,OAAA;gBACEyG,OAAO,EAAEA,CAAA,KAAM9F,gBAAgB,CAAC,KAAK,CAAE;gBACvCuC,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,eAEvFnD,OAAA,CAACZ,CAAC;kBAAC8D,SAAS,EAAC;gBAA4C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNvD,OAAA;cAAKkD,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBnD,OAAA;gBAAKkD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBAEpDnD,OAAA;kBAAKkD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBnD,OAAA;oBAAIkD,SAAS,EAAC,2GAA2G;oBAAAC,QAAA,EAAC;kBAE1H;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLvD,OAAA;oBAAKkD,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBnD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAMkD,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,EAAC;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC1FvD,OAAA;wBAAGkD,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,EAAErC,WAAW,CAACsC;sBAAQ;wBAAAA,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrE,CAAC,eACNvD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAMkD,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,EAAC;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3FvD,OAAA;wBAAGkD,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,EAAErC,WAAW,CAACY;sBAAS;wBAAA0B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC,eACNvD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAMkD,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvFvD,OAAA;wBAAGkD,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,EAAErC,WAAW,CAACa;sBAAM;wBAAAyB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE,CAAC,eACNvD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAMkD,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,EAAC;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3FvD,OAAA;wBAAGkD,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,EAAErC,WAAW,CAACc;sBAAS;wBAAAwB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC,eACNvD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAMkD,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,EAAC;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3FvD,OAAA;wBAAGkD,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,EAAErC,WAAW,CAACe;sBAAS;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNvD,OAAA;kBAAKkD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBnD,OAAA;oBAAIkD,SAAS,EAAC,2GAA2G;oBAAAC,QAAA,EAAC;kBAE1H;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLvD,OAAA;oBAAKkD,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBnD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAMkD,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvFvD,OAAA;wBAAKkD,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAAEb,cAAc,CAACxB,WAAW,CAACyB,MAAM;sBAAC;wBAAAa,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC,eACNvD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAMkD,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrFvD,OAAA;wBAAGkD,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,EAC1CrC,WAAW,CAAC0G,IAAI,GAAG,IAAI1G,WAAW,CAAC0G,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;sBAAgB;wBAAArE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACNvD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAMkD,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,EAAC;sBAAa;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7FvD,OAAA;wBAAGkD,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,EAAErC,WAAW,CAACgH;sBAAe;wBAAA1E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC,eACNvD,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAMkD,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxFvD,OAAA;wBAAGkD,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,EAAE,IAAI4E,IAAI,CAACjH,WAAW,CAACkH,OAAO,CAAC,CAACmB,cAAc,CAAC;sBAAC;wBAAA/F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/F,CAAC,EACLzC,WAAW,CAACsI,uBAAuB,iBAClCpJ,OAAA;sBAAAmD,QAAA,gBACEnD,OAAA;wBAAMkD,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,EAAC;sBAAqB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrGvD,OAAA;wBAAGkD,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,EAC1C,IAAI4E,IAAI,CAACjH,WAAW,CAACsI,uBAAuB,CAAC,CAACD,cAAc,CAAC;sBAAC;wBAAA/F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLzC,WAAW,CAACU,OAAO,iBAClBxB,OAAA;gBAAKkD,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnEnD,OAAA;kBAAIkD,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxFvD,OAAA;kBAAGkD,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAErC,WAAW,CAACU;gBAAO;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACN,EAGAzC,WAAW,CAACyB,MAAM,KAAK,QAAQ,iBAC9BvC,OAAA;gBAAKkD,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,gBACrHnD,OAAA;kBAAKkD,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,gBAC3EnD,OAAA,CAACrB,IAAI;oBAACuE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5BvD,OAAA;oBAAMkD,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACNvD,OAAA;kBAAGkD,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAEzD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNvD,OAAA;cAAKkD,SAAS,EAAC,yFAAyF;cAAAC,QAAA,GACrGrC,WAAW,CAACyB,MAAM,KAAK,QAAQ,iBAC9BvC,OAAA,CAAC9B,MAAM,CAACsI,MAAM;gBACZC,OAAO,EAAEA,CAAA,KAAM;kBACbhB,gBAAgB,CAAC3E,WAAW,CAACuD,EAAE,CAAC;kBAChC1D,gBAAgB,CAAC,KAAK,CAAC;gBACzB,CAAE;gBACFuC,SAAS,EAAC,+FAA+F;gBACzGwD,UAAU,EAAE;kBAAE3D,KAAK,EAAE;gBAAK,CAAE;gBAC5B4D,QAAQ,EAAE;kBAAE5D,KAAK,EAAE;gBAAK,CAAE;gBAAAI,QAAA,eAE1BnD,OAAA;kBAAKkD,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCnD,OAAA,CAACpB,KAAK;oBAACsE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAE/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAChB,eACDvD,OAAA,CAAC9B,MAAM,CAACsI,MAAM;gBACZC,OAAO,EAAEA,CAAA,KAAM9F,gBAAgB,CAAC,KAAK,CAAE;gBACvCuC,SAAS,EAAC,sHAAsH;gBAChIwD,UAAU,EAAE;kBAAE3D,KAAK,EAAE;gBAAK,CAAE;gBAC5B4D,QAAQ,EAAE;kBAAE5D,KAAK,EAAE;gBAAK,CAAE;gBAAAI,QAAA,EAC3B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC,eAGlBvD,OAAA,CAAC7B,eAAe;QAAAgF,QAAA,EACbvC,aAAa,IAAIE,WAAW,iBAC3Bd,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;UACTC,OAAO,EAAE;YAAEE,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBsF,IAAI,EAAE;YAAEtF,OAAO,EAAE;UAAE,CAAE;UACrBE,SAAS,EAAC,sFAAsF;UAChGuD,OAAO,EAAEA,CAAA,KAAM5F,gBAAgB,CAAC,KAAK,CAAE;UAAAsC,QAAA,eAEvCnD,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;YACTC,OAAO,EAAE;cAAEC,KAAK,EAAE,GAAG;cAAEC,OAAO,EAAE;YAAE,CAAE;YACpCC,OAAO,EAAE;cAAEF,KAAK,EAAE,CAAC;cAAEC,OAAO,EAAE;YAAE,CAAE;YAClCsF,IAAI,EAAE;cAAEvF,KAAK,EAAE,GAAG;cAAEC,OAAO,EAAE;YAAE,CAAE;YACjCyD,OAAO,EAAG8B,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;YACpCtF,SAAS,EAAC,mHAAmH;YAAAC,QAAA,gBAG7HnD,OAAA;cAAKkD,SAAS,EAAC,uFAAuF;cAAAC,QAAA,gBACpGnD,OAAA;gBAAKkD,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCnD,OAAA;kBAAKkD,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAC/DnD,OAAA,CAACtB,aAAa;oBAACwE,SAAS,EAAC;kBAA4C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACNvD,OAAA;kBAAIkD,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,GAAC,SAC5D,EAACrC,WAAW,CAAC+G,SAAS;gBAAA;kBAAAzE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNvD,OAAA;gBACEyG,OAAO,EAAEA,CAAA,KAAM5F,gBAAgB,CAAC,KAAK,CAAE;gBACvCqC,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,eAEvFnD,OAAA,CAACZ,CAAC;kBAAC8D,SAAS,EAAC;gBAA4C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNvD,OAAA;cAAKkD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EACxC/B,QAAQ,CAAC2F,MAAM,GAAG,CAAC,gBAClB/G,OAAA;gBAAKkD,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvB/B,QAAQ,CAACsG,GAAG,CAAE2B,OAAO,iBACpBrJ,OAAA,CAAC9B,MAAM,CAAC2E,GAAG;kBAETC,OAAO,EAAE;oBAAEE,OAAO,EAAE,CAAC;oBAAEgD,CAAC,EAAE;kBAAG,CAAE;kBAC/B/C,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEgD,CAAC,EAAE;kBAAE,CAAE;kBAC9B9C,SAAS,EAAE,QAAQmG,OAAO,CAACC,iBAAiB,GAAG,aAAa,GAAG,eAAe,EAAG;kBAAAnG,QAAA,eAEjFnD,OAAA;oBAAKkD,SAAS,EAAEpD,EAAE,CAChB,6BAA6B,EAC7BuJ,OAAO,CAACC,iBAAiB,GACrB,sCAAsC,GACtC,6EACN,CAAE;oBAAAnG,QAAA,gBACAnD,OAAA;sBAAGkD,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEkG,OAAO,CAACE;oBAAO;sBAAAnG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5CvD,OAAA;sBAAGkD,SAAS,EAAEpD,EAAE,CACd,cAAc,EACduJ,OAAO,CAACC,iBAAiB,GACrB,eAAe,GACf,oCACN,CAAE;sBAAAnG,QAAA,GACCkG,OAAO,CAACG,UAAU,EAAC,KAAG,EAAC,IAAIzB,IAAI,CAACsB,OAAO,CAACI,MAAM,CAAC,CAACN,cAAc,CAAC,CAAC;oBAAA;sBAAA/F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC,GApBD8F,OAAO,CAAChF,EAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqBL,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENvD,OAAA;gBAAKkD,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3EnD,OAAA;kBAAKkD,SAAS,EAAC,6FAA6F;kBAAAC,QAAA,eAC1GnD,OAAA,CAACtB,aAAa;oBAACwE,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACNvD,OAAA;kBAAIkD,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLvD,OAAA;kBAAGkD,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAElD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNvD,OAAA;cAAKkD,SAAS,EAAC,qDAAqD;cAAAC,QAAA,eAClEnD,OAAA;gBAAKkD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBnD,OAAA;kBACEyI,IAAI,EAAC,MAAM;kBACXQ,WAAW,EAAC,sBAAsB;kBAClCnC,KAAK,EAAE5F,WAAY;kBACnByH,QAAQ,EAAGJ,CAAC,IAAKpH,cAAc,CAACoH,CAAC,CAACM,MAAM,CAAC/B,KAAK,CAAE;kBAChD4C,SAAS,EAAGnB,CAAC,IAAKA,CAAC,CAACoB,GAAG,KAAK,OAAO,IAAIhE,iBAAiB,CAAC,CAAE;kBAC3DzC,SAAS,EAAC;gBAA+L;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1M,CAAC,eACFvD,OAAA,CAAC9B,MAAM,CAACsI,MAAM;kBACZC,OAAO,EAAEd,iBAAkB;kBAC3BuD,QAAQ,EAAE,CAAChI,WAAW,CAAC0E,IAAI,CAAC,CAAE;kBAC9B1C,SAAS,EAAEpD,EAAE,CACX,kDAAkD,EAClDoB,WAAW,CAAC0E,IAAI,CAAC,CAAC,GACd,0CAA0C,GAC1C,sFACN,CAAE;kBACFc,UAAU,EAAExF,WAAW,CAAC0E,IAAI,CAAC,CAAC,GAAG;oBAAE7C,KAAK,EAAE;kBAAK,CAAC,GAAG,CAAC,CAAE;kBACtD4D,QAAQ,EAAEzF,WAAW,CAAC0E,IAAI,CAAC,CAAC,GAAG;oBAAE7C,KAAK,EAAE;kBAAK,CAAC,GAAG,CAAC,CAAE;kBAAAI,QAAA,eAEpDnD,OAAA,CAACb,IAAI;oBAAC+D,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CAl/BID,gBAA0B;EAAA,QACbT,OAAO,EAsLDC,QAAQ;AAAA;AAAAmK,EAAA,GAvL3B3J,gBAA0B;AAo/BhC,eAAeA,gBAAgB;AAAC,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}