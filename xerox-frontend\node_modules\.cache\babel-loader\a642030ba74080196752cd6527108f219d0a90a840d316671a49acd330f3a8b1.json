{"ast": null, "code": "/**\n * @license lucide-react v0.515.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 16v6\",\n  key: \"c8a4gj\"\n}], [\"path\", {\n  d: \"M14 20h-4\",\n  key: \"m8m19d\"\n}], [\"path\", {\n  d: \"M18 2h4v4\",\n  key: \"1341mj\"\n}], [\"path\", {\n  d: \"m2 2 7.17 7.17\",\n  key: \"13q8l2\"\n}], [\"path\", {\n  d: \"M2 5.355V2h3.357\",\n  key: \"18136r\"\n}], [\"path\", {\n  d: \"m22 2-7.17 7.17\",\n  key: \"1epvy4\"\n}], [\"path\", {\n  d: \"M8 5 5 8\",\n  key: \"mgbjhz\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4exip2\"\n}]];\nconst Transgender = createLucideIcon(\"transgender\", __iconNode);\nexport { __iconNode, Transgender as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Transgender", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\node_modules\\lucide-react\\src\\icons\\transgender.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 16v6', key: 'c8a4gj' }],\n  ['path', { d: 'M14 20h-4', key: 'm8m19d' }],\n  ['path', { d: 'M18 2h4v4', key: '1341mj' }],\n  ['path', { d: 'm2 2 7.17 7.17', key: '13q8l2' }],\n  ['path', { d: 'M2 5.355V2h3.357', key: '18136r' }],\n  ['path', { d: 'm22 2-7.17 7.17', key: '1epvy4' }],\n  ['path', { d: 'M8 5 5 8', key: 'mgbjhz' }],\n  ['circle', { cx: '12', cy: '12', r: '4', key: '4exip2' }],\n];\n\n/**\n * @component @name Transgender\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTZ2NiIgLz4KICA8cGF0aCBkPSJNMTQgMjBoLTQiIC8+CiAgPHBhdGggZD0iTTE4IDJoNHY0IiAvPgogIDxwYXRoIGQ9Im0yIDIgNy4xNyA3LjE3IiAvPgogIDxwYXRoIGQ9Ik0yIDUuMzU1VjJoMy4zNTciIC8+CiAgPHBhdGggZD0ibTIyIDItNy4xNyA3LjE3IiAvPgogIDxwYXRoIGQ9Ik04IDUgNSA4IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/transgender\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Transgender = createLucideIcon('transgender', __iconNode);\n\nexport default Transgender;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GAC1D;AAaM,MAAAI,WAAA,GAAcC,gBAAiB,gBAAeP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}