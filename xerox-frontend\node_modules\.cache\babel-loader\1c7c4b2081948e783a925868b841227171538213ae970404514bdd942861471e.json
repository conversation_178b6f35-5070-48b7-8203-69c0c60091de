{"ast": null, "code": "/**\n * Check if the value is a zero value string like \"0px\" or \"0%\"\n */\nconst isZeroValueString = v => /^0[^.\\s]+$/u.test(v);\nexport { isZeroValueString };", "map": {"version": 3, "names": ["isZeroValueString", "v", "test"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-utils/dist/es/is-zero-value-string.mjs"], "sourcesContent": ["/**\n * Check if the value is a zero value string like \"0px\" or \"0%\"\n */\nconst isZeroValueString = (v) => /^0[^.\\s]+$/u.test(v);\n\nexport { isZeroValueString };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,iBAAiB,GAAIC,CAAC,IAAK,aAAa,CAACC,IAAI,CAACD,CAAC,CAAC;AAEtD,SAASD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}