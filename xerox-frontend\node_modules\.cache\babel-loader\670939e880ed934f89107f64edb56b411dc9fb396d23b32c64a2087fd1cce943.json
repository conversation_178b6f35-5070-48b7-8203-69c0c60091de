{"ast": null, "code": "import { warnOnce, SubscriptionManager, velocityPerSecond } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = value => {\n  return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n  current: undefined\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n  /**\n   * @param init - The initiating value\n   * @param config - Optional configuration options\n   *\n   * -  `transformer`: A function to transform incoming values with.\n   */\n  constructor(init, options = {}) {\n    /**\n     * Tracks whether this value can output a velocity. Currently this is only true\n     * if the value is numerical, but we might be able to widen the scope here and support\n     * other value types.\n     *\n     * @internal\n     */\n    this.canTrackVelocity = null;\n    /**\n     * An object containing a SubscriptionManager for each active event.\n     */\n    this.events = {};\n    this.updateAndNotify = (v, render = true) => {\n      const currentTime = time.now();\n      /**\n       * If we're updating the value during another frame or eventloop\n       * than the previous frame, then the we set the previous frame value\n       * to current.\n       */\n      if (this.updatedAt !== currentTime) {\n        this.setPrevFrameValue();\n      }\n      this.prev = this.current;\n      this.setCurrent(v);\n      // Update update subscribers\n      if (this.current !== this.prev) {\n        this.events.change?.notify(this.current);\n        if (this.dependents) {\n          for (const dependent of this.dependents) {\n            dependent.dirty();\n          }\n        }\n      }\n      // Update render subscribers\n      if (render) {\n        this.events.renderRequest?.notify(this.current);\n      }\n    };\n    this.hasAnimated = false;\n    this.setCurrent(init);\n    this.owner = options.owner;\n  }\n  setCurrent(current) {\n    this.current = current;\n    this.updatedAt = time.now();\n    if (this.canTrackVelocity === null && current !== undefined) {\n      this.canTrackVelocity = isFloat(this.current);\n    }\n  }\n  setPrevFrameValue(prevFrameValue = this.current) {\n    this.prevFrameValue = prevFrameValue;\n    this.prevUpdatedAt = this.updatedAt;\n  }\n  /**\n   * Adds a function that will be notified when the `MotionValue` is updated.\n   *\n   * It returns a function that, when called, will cancel the subscription.\n   *\n   * When calling `onChange` inside a React component, it should be wrapped with the\n   * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n   * from the `useEffect` function to ensure you don't add duplicate subscribers..\n   *\n   * ```jsx\n   * export const MyComponent = () => {\n   *   const x = useMotionValue(0)\n   *   const y = useMotionValue(0)\n   *   const opacity = useMotionValue(1)\n   *\n   *   useEffect(() => {\n   *     function updateOpacity() {\n   *       const maxXY = Math.max(x.get(), y.get())\n   *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n   *       opacity.set(newOpacity)\n   *     }\n   *\n   *     const unsubscribeX = x.on(\"change\", updateOpacity)\n   *     const unsubscribeY = y.on(\"change\", updateOpacity)\n   *\n   *     return () => {\n   *       unsubscribeX()\n   *       unsubscribeY()\n   *     }\n   *   }, [])\n   *\n   *   return <motion.div style={{ x }} />\n   * }\n   * ```\n   *\n   * @param subscriber - A function that receives the latest value.\n   * @returns A function that, when called, will cancel this subscription.\n   *\n   * @deprecated\n   */\n  onChange(subscription) {\n    if (process.env.NODE_ENV !== \"production\") {\n      warnOnce(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n    }\n    return this.on(\"change\", subscription);\n  }\n  on(eventName, callback) {\n    if (!this.events[eventName]) {\n      this.events[eventName] = new SubscriptionManager();\n    }\n    const unsubscribe = this.events[eventName].add(callback);\n    if (eventName === \"change\") {\n      return () => {\n        unsubscribe();\n        /**\n         * If we have no more change listeners by the start\n         * of the next frame, stop active animations.\n         */\n        frame.read(() => {\n          if (!this.events.change.getSize()) {\n            this.stop();\n          }\n        });\n      };\n    }\n    return unsubscribe;\n  }\n  clearListeners() {\n    for (const eventManagers in this.events) {\n      this.events[eventManagers].clear();\n    }\n  }\n  /**\n   * Attaches a passive effect to the `MotionValue`.\n   */\n  attach(passiveEffect, stopPassiveEffect) {\n    this.passiveEffect = passiveEffect;\n    this.stopPassiveEffect = stopPassiveEffect;\n  }\n  /**\n   * Sets the state of the `MotionValue`.\n   *\n   * @remarks\n   *\n   * ```jsx\n   * const x = useMotionValue(0)\n   * x.set(10)\n   * ```\n   *\n   * @param latest - Latest value to set.\n   * @param render - Whether to notify render subscribers. Defaults to `true`\n   *\n   * @public\n   */\n  set(v, render = true) {\n    if (!render || !this.passiveEffect) {\n      this.updateAndNotify(v, render);\n    } else {\n      this.passiveEffect(v, this.updateAndNotify);\n    }\n  }\n  setWithVelocity(prev, current, delta) {\n    this.set(current);\n    this.prev = undefined;\n    this.prevFrameValue = prev;\n    this.prevUpdatedAt = this.updatedAt - delta;\n  }\n  /**\n   * Set the state of the `MotionValue`, stopping any active animations,\n   * effects, and resets velocity to `0`.\n   */\n  jump(v, endAnimation = true) {\n    this.updateAndNotify(v);\n    this.prev = v;\n    this.prevUpdatedAt = this.prevFrameValue = undefined;\n    endAnimation && this.stop();\n    if (this.stopPassiveEffect) this.stopPassiveEffect();\n  }\n  dirty() {\n    this.events.change?.notify(this.current);\n  }\n  addDependent(dependent) {\n    if (!this.dependents) {\n      this.dependents = new Set();\n    }\n    this.dependents.add(dependent);\n  }\n  removeDependent(dependent) {\n    if (this.dependents) {\n      this.dependents.delete(dependent);\n    }\n  }\n  /**\n   * Returns the latest state of `MotionValue`\n   *\n   * @returns - The latest state of `MotionValue`\n   *\n   * @public\n   */\n  get() {\n    if (collectMotionValues.current) {\n      collectMotionValues.current.push(this);\n    }\n    return this.current;\n  }\n  /**\n   * @public\n   */\n  getPrevious() {\n    return this.prev;\n  }\n  /**\n   * Returns the latest velocity of `MotionValue`\n   *\n   * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n   *\n   * @public\n   */\n  getVelocity() {\n    const currentTime = time.now();\n    if (!this.canTrackVelocity || this.prevFrameValue === undefined || currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n      return 0;\n    }\n    const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n    // Casts because of parseFloat's poor typing\n    return velocityPerSecond(parseFloat(this.current) - parseFloat(this.prevFrameValue), delta);\n  }\n  /**\n   * Registers a new animation to control this `MotionValue`. Only one\n   * animation can drive a `MotionValue` at one time.\n   *\n   * ```jsx\n   * value.start()\n   * ```\n   *\n   * @param animation - A function that starts the provided animation\n   */\n  start(startAnimation) {\n    this.stop();\n    return new Promise(resolve => {\n      this.hasAnimated = true;\n      this.animation = startAnimation(resolve);\n      if (this.events.animationStart) {\n        this.events.animationStart.notify();\n      }\n    }).then(() => {\n      if (this.events.animationComplete) {\n        this.events.animationComplete.notify();\n      }\n      this.clearAnimation();\n    });\n  }\n  /**\n   * Stop the currently active animation.\n   *\n   * @public\n   */\n  stop() {\n    if (this.animation) {\n      this.animation.stop();\n      if (this.events.animationCancel) {\n        this.events.animationCancel.notify();\n      }\n    }\n    this.clearAnimation();\n  }\n  /**\n   * Returns `true` if this value is currently animating.\n   *\n   * @public\n   */\n  isAnimating() {\n    return !!this.animation;\n  }\n  clearAnimation() {\n    delete this.animation;\n  }\n  /**\n   * Destroy and clean up subscribers to this `MotionValue`.\n   *\n   * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n   * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n   * created a `MotionValue` via the `motionValue` function.\n   *\n   * @public\n   */\n  destroy() {\n    this.dependents?.clear();\n    this.events.destroy?.notify();\n    this.clearListeners();\n    this.stop();\n    if (this.stopPassiveEffect) {\n      this.stopPassiveEffect();\n    }\n  }\n}\nfunction motionValue(init, options) {\n  return new MotionValue(init, options);\n}\nexport { MotionValue, collectMotionValues, motionValue };", "map": {"version": 3, "names": ["warnOnce", "SubscriptionManager", "velocityPerSecond", "time", "frame", "MAX_VELOCITY_DELTA", "isFloat", "value", "isNaN", "parseFloat", "collectMotionValues", "current", "undefined", "MotionValue", "constructor", "init", "options", "canTrackVelocity", "events", "updateAndNotify", "v", "render", "currentTime", "now", "updatedAt", "setPrevFrameValue", "prev", "setCurrent", "change", "notify", "dependents", "dependent", "dirty", "renderRequest", "hasAnimated", "owner", "prevFrameValue", "prevUpdatedAt", "onChange", "subscription", "process", "env", "NODE_ENV", "on", "eventName", "callback", "unsubscribe", "add", "read", "getSize", "stop", "clearListeners", "eventManagers", "clear", "attach", "passiveEffect", "stopPassiveEffect", "set", "setWithVelocity", "delta", "jump", "endAnimation", "addDependent", "Set", "removeDependent", "delete", "get", "push", "getPrevious", "getVelocity", "Math", "min", "start", "startAnimation", "Promise", "resolve", "animation", "animationStart", "then", "animationComplete", "clearAnimation", "animationCancel", "isAnimating", "destroy", "motionValue"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/value/index.mjs"], "sourcesContent": ["import { warnOnce, SubscriptionManager, velocityPerSecond } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     */\n    constructor(init, options = {}) {\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = null;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v, render = true) => {\n            const currentTime = time.now();\n            /**\n             * If we're updating the value during another frame or eventloop\n             * than the previous frame, then the we set the previous frame value\n             * to current.\n             */\n            if (this.updatedAt !== currentTime) {\n                this.setPrevFrameValue();\n            }\n            this.prev = this.current;\n            this.setCurrent(v);\n            // Update update subscribers\n            if (this.current !== this.prev) {\n                this.events.change?.notify(this.current);\n                if (this.dependents) {\n                    for (const dependent of this.dependents) {\n                        dependent.dirty();\n                    }\n                }\n            }\n            // Update render subscribers\n            if (render) {\n                this.events.renderRequest?.notify(this.current);\n            }\n        };\n        this.hasAnimated = false;\n        this.setCurrent(init);\n        this.owner = options.owner;\n    }\n    setCurrent(current) {\n        this.current = current;\n        this.updatedAt = time.now();\n        if (this.canTrackVelocity === null && current !== undefined) {\n            this.canTrackVelocity = isFloat(this.current);\n        }\n    }\n    setPrevFrameValue(prevFrameValue = this.current) {\n        this.prevFrameValue = prevFrameValue;\n        this.prevUpdatedAt = this.updatedAt;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v, render = true) {\n        if (!render || !this.passiveEffect) {\n            this.updateAndNotify(v, render);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = undefined;\n        this.prevFrameValue = prev;\n        this.prevUpdatedAt = this.updatedAt - delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v, endAnimation = true) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.prevUpdatedAt = this.prevFrameValue = undefined;\n        endAnimation && this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    dirty() {\n        this.events.change?.notify(this.current);\n    }\n    addDependent(dependent) {\n        if (!this.dependents) {\n            this.dependents = new Set();\n        }\n        this.dependents.add(dependent);\n    }\n    removeDependent(dependent) {\n        if (this.dependents) {\n            this.dependents.delete(dependent);\n        }\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        const currentTime = time.now();\n        if (!this.canTrackVelocity ||\n            this.prevFrameValue === undefined ||\n            currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n            return 0;\n        }\n        const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n        // Casts because of parseFloat's poor typing\n        return velocityPerSecond(parseFloat(this.current) -\n            parseFloat(this.prevFrameValue), delta);\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.dependents?.clear();\n        this.events.destroy?.notify();\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\nexport { MotionValue, collectMotionValues, motionValue };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,mBAAmB,EAAEC,iBAAiB,QAAQ,cAAc;AAC/E,SAASC,IAAI,QAAQ,4BAA4B;AACjD,SAASC,KAAK,QAAQ,wBAAwB;;AAE9C;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,EAAE;AAC7B,MAAMC,OAAO,GAAIC,KAAK,IAAK;EACvB,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,KAAK,CAAC,CAAC;AACpC,CAAC;AACD,MAAMG,mBAAmB,GAAG;EACxBC,OAAO,EAAEC;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,eAAe,GAAG,CAACC,CAAC,EAAEC,MAAM,GAAG,IAAI,KAAK;MACzC,MAAMC,WAAW,GAAGnB,IAAI,CAACoB,GAAG,CAAC,CAAC;MAC9B;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACC,SAAS,KAAKF,WAAW,EAAE;QAChC,IAAI,CAACG,iBAAiB,CAAC,CAAC;MAC5B;MACA,IAAI,CAACC,IAAI,GAAG,IAAI,CAACf,OAAO;MACxB,IAAI,CAACgB,UAAU,CAACP,CAAC,CAAC;MAClB;MACA,IAAI,IAAI,CAACT,OAAO,KAAK,IAAI,CAACe,IAAI,EAAE;QAC5B,IAAI,CAACR,MAAM,CAACU,MAAM,EAAEC,MAAM,CAAC,IAAI,CAAClB,OAAO,CAAC;QACxC,IAAI,IAAI,CAACmB,UAAU,EAAE;UACjB,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACD,UAAU,EAAE;YACrCC,SAAS,CAACC,KAAK,CAAC,CAAC;UACrB;QACJ;MACJ;MACA;MACA,IAAIX,MAAM,EAAE;QACR,IAAI,CAACH,MAAM,CAACe,aAAa,EAAEJ,MAAM,CAAC,IAAI,CAAClB,OAAO,CAAC;MACnD;IACJ,CAAC;IACD,IAAI,CAACuB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACP,UAAU,CAACZ,IAAI,CAAC;IACrB,IAAI,CAACoB,KAAK,GAAGnB,OAAO,CAACmB,KAAK;EAC9B;EACAR,UAAUA,CAAChB,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACa,SAAS,GAAGrB,IAAI,CAACoB,GAAG,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACN,gBAAgB,KAAK,IAAI,IAAIN,OAAO,KAAKC,SAAS,EAAE;MACzD,IAAI,CAACK,gBAAgB,GAAGX,OAAO,CAAC,IAAI,CAACK,OAAO,CAAC;IACjD;EACJ;EACAc,iBAAiBA,CAACW,cAAc,GAAG,IAAI,CAACzB,OAAO,EAAE;IAC7C,IAAI,CAACyB,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACb,SAAS;EACvC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIc,QAAQA,CAACC,YAAY,EAAE;IACnB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvC1C,QAAQ,CAAC,KAAK,EAAE,iFAAiF,CAAC;IACtG;IACA,OAAO,IAAI,CAAC2C,EAAE,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EAC1C;EACAI,EAAEA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACpB,IAAI,CAAC,IAAI,CAAC3B,MAAM,CAAC0B,SAAS,CAAC,EAAE;MACzB,IAAI,CAAC1B,MAAM,CAAC0B,SAAS,CAAC,GAAG,IAAI3C,mBAAmB,CAAC,CAAC;IACtD;IACA,MAAM6C,WAAW,GAAG,IAAI,CAAC5B,MAAM,CAAC0B,SAAS,CAAC,CAACG,GAAG,CAACF,QAAQ,CAAC;IACxD,IAAID,SAAS,KAAK,QAAQ,EAAE;MACxB,OAAO,MAAM;QACTE,WAAW,CAAC,CAAC;QACb;AAChB;AACA;AACA;QACgB1C,KAAK,CAAC4C,IAAI,CAAC,MAAM;UACb,IAAI,CAAC,IAAI,CAAC9B,MAAM,CAACU,MAAM,CAACqB,OAAO,CAAC,CAAC,EAAE;YAC/B,IAAI,CAACC,IAAI,CAAC,CAAC;UACf;QACJ,CAAC,CAAC;MACN,CAAC;IACL;IACA,OAAOJ,WAAW;EACtB;EACAK,cAAcA,CAAA,EAAG;IACb,KAAK,MAAMC,aAAa,IAAI,IAAI,CAAClC,MAAM,EAAE;MACrC,IAAI,CAACA,MAAM,CAACkC,aAAa,CAAC,CAACC,KAAK,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;EACIC,MAAMA,CAACC,aAAa,EAAEC,iBAAiB,EAAE;IACrC,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,GAAGA,CAACrC,CAAC,EAAEC,MAAM,GAAG,IAAI,EAAE;IAClB,IAAI,CAACA,MAAM,IAAI,CAAC,IAAI,CAACkC,aAAa,EAAE;MAChC,IAAI,CAACpC,eAAe,CAACC,CAAC,EAAEC,MAAM,CAAC;IACnC,CAAC,MACI;MACD,IAAI,CAACkC,aAAa,CAACnC,CAAC,EAAE,IAAI,CAACD,eAAe,CAAC;IAC/C;EACJ;EACAuC,eAAeA,CAAChC,IAAI,EAAEf,OAAO,EAAEgD,KAAK,EAAE;IAClC,IAAI,CAACF,GAAG,CAAC9C,OAAO,CAAC;IACjB,IAAI,CAACe,IAAI,GAAGd,SAAS;IACrB,IAAI,CAACwB,cAAc,GAAGV,IAAI;IAC1B,IAAI,CAACW,aAAa,GAAG,IAAI,CAACb,SAAS,GAAGmC,KAAK;EAC/C;EACA;AACJ;AACA;AACA;EACIC,IAAIA,CAACxC,CAAC,EAAEyC,YAAY,GAAG,IAAI,EAAE;IACzB,IAAI,CAAC1C,eAAe,CAACC,CAAC,CAAC;IACvB,IAAI,CAACM,IAAI,GAAGN,CAAC;IACb,IAAI,CAACiB,aAAa,GAAG,IAAI,CAACD,cAAc,GAAGxB,SAAS;IACpDiD,YAAY,IAAI,IAAI,CAACX,IAAI,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACM,iBAAiB,EACtB,IAAI,CAACA,iBAAiB,CAAC,CAAC;EAChC;EACAxB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACd,MAAM,CAACU,MAAM,EAAEC,MAAM,CAAC,IAAI,CAAClB,OAAO,CAAC;EAC5C;EACAmD,YAAYA,CAAC/B,SAAS,EAAE;IACpB,IAAI,CAAC,IAAI,CAACD,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAIiC,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACjC,UAAU,CAACiB,GAAG,CAAChB,SAAS,CAAC;EAClC;EACAiC,eAAeA,CAACjC,SAAS,EAAE;IACvB,IAAI,IAAI,CAACD,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACmC,MAAM,CAAClC,SAAS,CAAC;IACrC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACImC,GAAGA,CAAA,EAAG;IACF,IAAIxD,mBAAmB,CAACC,OAAO,EAAE;MAC7BD,mBAAmB,CAACC,OAAO,CAACwD,IAAI,CAAC,IAAI,CAAC;IAC1C;IACA,OAAO,IAAI,CAACxD,OAAO;EACvB;EACA;AACJ;AACA;EACIyD,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC1C,IAAI;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2C,WAAWA,CAAA,EAAG;IACV,MAAM/C,WAAW,GAAGnB,IAAI,CAACoB,GAAG,CAAC,CAAC;IAC9B,IAAI,CAAC,IAAI,CAACN,gBAAgB,IACtB,IAAI,CAACmB,cAAc,KAAKxB,SAAS,IACjCU,WAAW,GAAG,IAAI,CAACE,SAAS,GAAGnB,kBAAkB,EAAE;MACnD,OAAO,CAAC;IACZ;IACA,MAAMsD,KAAK,GAAGW,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/C,SAAS,GAAG,IAAI,CAACa,aAAa,EAAEhC,kBAAkB,CAAC;IAC/E;IACA,OAAOH,iBAAiB,CAACO,UAAU,CAAC,IAAI,CAACE,OAAO,CAAC,GAC7CF,UAAU,CAAC,IAAI,CAAC2B,cAAc,CAAC,EAAEuB,KAAK,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIa,KAAKA,CAACC,cAAc,EAAE;IAClB,IAAI,CAACvB,IAAI,CAAC,CAAC;IACX,OAAO,IAAIwB,OAAO,CAAEC,OAAO,IAAK;MAC5B,IAAI,CAACzC,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC0C,SAAS,GAAGH,cAAc,CAACE,OAAO,CAAC;MACxC,IAAI,IAAI,CAACzD,MAAM,CAAC2D,cAAc,EAAE;QAC5B,IAAI,CAAC3D,MAAM,CAAC2D,cAAc,CAAChD,MAAM,CAAC,CAAC;MACvC;IACJ,CAAC,CAAC,CAACiD,IAAI,CAAC,MAAM;MACV,IAAI,IAAI,CAAC5D,MAAM,CAAC6D,iBAAiB,EAAE;QAC/B,IAAI,CAAC7D,MAAM,CAAC6D,iBAAiB,CAAClD,MAAM,CAAC,CAAC;MAC1C;MACA,IAAI,CAACmD,cAAc,CAAC,CAAC;IACzB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI9B,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC0B,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAC1B,IAAI,CAAC,CAAC;MACrB,IAAI,IAAI,CAAChC,MAAM,CAAC+D,eAAe,EAAE;QAC7B,IAAI,CAAC/D,MAAM,CAAC+D,eAAe,CAACpD,MAAM,CAAC,CAAC;MACxC;IACJ;IACA,IAAI,CAACmD,cAAc,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;EACIE,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACN,SAAS;EAC3B;EACAI,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,SAAS;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,OAAOA,CAAA,EAAG;IACN,IAAI,CAACrD,UAAU,EAAEuB,KAAK,CAAC,CAAC;IACxB,IAAI,CAACnC,MAAM,CAACiE,OAAO,EAAEtD,MAAM,CAAC,CAAC;IAC7B,IAAI,CAACsB,cAAc,CAAC,CAAC;IACrB,IAAI,CAACD,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACM,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;IAC5B;EACJ;AACJ;AACA,SAAS4B,WAAWA,CAACrE,IAAI,EAAEC,OAAO,EAAE;EAChC,OAAO,IAAIH,WAAW,CAACE,IAAI,EAAEC,OAAO,CAAC;AACzC;AAEA,SAASH,WAAW,EAAEH,mBAAmB,EAAE0E,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}