import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  gradient?: boolean;
  glass?: boolean;
  onClick?: () => void;
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  hover = false,
  gradient = false,
  glass = false,
  onClick
}) => {
  const Component = onClick ? motion.button : motion.div;

  return (
    <Component
      onClick={onClick}
      className={cn(
        "rounded-2xl border transition-all duration-300",
        glass 
          ? "bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-white/20" 
          : "bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700",
        gradient && "bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900",
        hover && "hover:shadow-lg hover:scale-[1.02] cursor-pointer",
        "shadow-sm",
        className
      )}
      whileHover={hover ? { y: -2 } : undefined}
      whileTap={onClick ? { scale: 0.98 } : undefined}
    >
      {children}
    </Component>
  );
};

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export const CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => {
  return (
    <div className={cn("p-6 border-b border-slate-200 dark:border-slate-700", className)}>
      {children}
    </div>
  );
};

interface CardBodyProps {
  children: React.ReactNode;
  className?: string;
}

export const CardBody: React.FC<CardBodyProps> = ({ children, className }) => {
  return (
    <div className={cn("p-6", className)}>
      {children}
    </div>
  );
};

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const CardFooter: React.FC<CardFooterProps> = ({ children, className }) => {
  return (
    <div className={cn("p-6 border-t border-slate-200 dark:border-slate-700", className)}>
      {children}
    </div>
  );
};

// Animated Background Card
interface AnimatedCardProps extends CardProps {
  animationType?: 'float' | 'glow' | 'pulse' | 'none';
}

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  className,
  animationType = 'none',
  ...props
}) => {
  const animations = {
    float: {
      y: [0, -10, 0],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    glow: {
      boxShadow: [
        "0 0 20px rgba(59, 130, 246, 0.3)",
        "0 0 40px rgba(59, 130, 246, 0.6)",
        "0 0 20px rgba(59, 130, 246, 0.3)"
      ],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    pulse: {
      scale: [1, 1.02, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    none: {}
  };

  return (
    <motion.div
      animate={animations[animationType]}
      className={cn("relative", className)}
    >
      <Card {...props}>
        {children}
      </Card>
    </motion.div>
  );
};

// Gradient Border Card
export const GradientBorderCard: React.FC<CardProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div className="relative p-[1px] rounded-2xl bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500">
      <Card
        className={cn("bg-white dark:bg-slate-900 border-0", className)}
        {...props}
      >
        {children}
      </Card>
    </div>
  );
};

// Stats Card Component
interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<any>;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  color?: string;
  className?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon: Icon,
  change,
  changeType = 'neutral',
  color = "blue",
  className
}) => {
  const colorClasses = {
    blue: "from-blue-500 to-blue-600 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400",
    green: "from-green-500 to-green-600 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400",
    purple: "from-purple-500 to-purple-600 bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400",
    orange: "from-orange-500 to-orange-600 bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400",
    red: "from-red-500 to-red-600 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400"
  };

  const changeColors = {
    positive: "text-green-600 dark:text-green-400",
    negative: "text-red-600 dark:text-red-400",
    neutral: "text-slate-600 dark:text-slate-400"
  };

  return (
    <Card hover glass className={cn("group", className)}>
      <CardBody>
        <div className="flex items-center justify-between mb-4">
          <div className={cn("p-3 rounded-xl", colorClasses[color as keyof typeof colorClasses].split(' ').slice(2).join(' '))}>
            <Icon className={cn("w-6 h-6", colorClasses[color as keyof typeof colorClasses].split(' ').slice(-2).join(' '))} />
          </div>
          {change && (
            <div className={cn("text-sm font-medium", changeColors[changeType])}>
              {change}
            </div>
          )}
        </div>
        
        <div className="space-y-1">
          <p className="text-slate-600 dark:text-slate-300 text-sm font-medium">
            {title}
          </p>
          <p className="text-3xl font-bold text-slate-900 dark:text-white">
            {value}
          </p>
        </div>
        
        {/* Gradient overlay on hover */}
        <div className={cn(
          "absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-10",
          "bg-gradient-to-br transition-opacity duration-300",
          colorClasses[color as keyof typeof colorClasses].split(' ').slice(0, 2).join(' ')
        )} />
      </CardBody>
    </Card>
  );
};
