{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\Register.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner, Nav } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { UserPlus, GraduationCap, Store } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport Layout from './Layout';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('student');\n  const [studentData, setStudentData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    studentNumber: '',\n    firstName: '',\n    lastName: '',\n    phoneNumber: '',\n    department: '',\n    year: undefined\n  });\n  const [xeroxData, setXeroxData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    xeroxCenterName: '',\n    location: '',\n    contactPerson: '',\n    phoneNumber: '',\n    description: ''\n  });\n  const {\n    registerStudent,\n    registerXeroxCenter,\n    isLoading,\n    error,\n    user\n  } = useAuth();\n  const {\n    isDarkMode\n  } = useTheme();\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n  const handleStudentSubmit = async e => {\n    e.preventDefault();\n    const success = await registerStudent(studentData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n  const handleXeroxSubmit = async e => {\n    e.preventDefault();\n    const success = await registerXeroxCenter(xeroxData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n  const handleStudentChange = (field, value) => {\n    setStudentData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleXeroxChange = (field, value) => {\n    setXeroxData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const cardVariants = {\n    hidden: {\n      opacity: 0,\n      y: 50,\n      scale: 0.9\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.6,\n        ease: [0.6, -0.05, 0.01, 0.99]\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      className: \"d-flex align-items-center justify-content-center py-5\",\n      style: {\n        minHeight: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center w-100\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            variants: cardVariants,\n            initial: \"hidden\",\n            animate: \"visible\",\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: `shadow-lg border-0 ${isDarkMode ? 'bg-dark text-light' : ''}`,\n              style: {\n                borderRadius: '20px',\n                overflow: 'hidden'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"position-relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-100 h-100 position-absolute\",\n                  style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    height: '100px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"p-4 position-relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                      initial: {\n                        scale: 0,\n                        rotate: -180\n                      },\n                      animate: {\n                        scale: 1,\n                        rotate: 0\n                      },\n                      transition: {\n                        delay: 0.3,\n                        duration: 0.5\n                      },\n                      className: \"d-inline-block p-3 rounded-circle bg-white shadow-sm mb-3\",\n                      style: {\n                        marginTop: '10px'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(UserPlus, {\n                        size: 32,\n                        className: \"text-primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 106,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 99,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"h4 mb-2\",\n                      style: {\n                        color: isDarkMode ? '#fff' : '#333',\n                        marginTop: '40px'\n                      },\n                      children: \"Create Account\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 108,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted small\",\n                      children: \"Join XeroxHub today\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 109,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 98,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Nav, {\n                    variant: \"pills\",\n                    className: \"mb-4 justify-content-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Nav.Item, {\n                      children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                        active: activeTab === 'student',\n                        onClick: () => setActiveTab('student'),\n                        className: `px-4 py-2 mx-1 ${activeTab === 'student' ? 'btn-gradient' : ''}`,\n                        style: {\n                          borderRadius: '25px',\n                          fontWeight: '500',\n                          transition: 'all 0.3s ease',\n                          background: activeTab === 'student' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                          border: activeTab === 'student' ? 'none' : '2px solid #e9ecef',\n                          color: activeTab === 'student' ? 'white' : isDarkMode ? '#fff' : '#333'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(GraduationCap, {\n                          size: 16,\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 127,\n                          columnNumber: 27\n                        }, this), \"Student\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 114,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 113,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n                      children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                        active: activeTab === 'xerox',\n                        onClick: () => setActiveTab('xerox'),\n                        className: `px-4 py-2 mx-1 ${activeTab === 'xerox' ? 'btn-gradient' : ''}`,\n                        style: {\n                          borderRadius: '25px',\n                          fontWeight: '500',\n                          transition: 'all 0.3s ease',\n                          background: activeTab === 'xerox' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                          border: activeTab === 'xerox' ? 'none' : '2px solid #e9ecef',\n                          color: activeTab === 'xerox' ? 'white' : isDarkMode ? '#fff' : '#333'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Store, {\n                          size: 16,\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 145,\n                          columnNumber: 27\n                        }, this), \"Xerox Center\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 132,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 131,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 21\n                  }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n                    variant: \"danger\",\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-exclamation-triangle me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 153,\n                      columnNumber: 19\n                    }, this), error]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 17\n                  }, this), activeTab === 'student' ? /*#__PURE__*/_jsxDEV(Form, {\n                    onSubmit: handleStudentSubmit,\n                    children: [/*#__PURE__*/_jsxDEV(Row, {\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Username\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 163,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Enter username\",\n                            value: studentData.username,\n                            onChange: e => handleStudentChange('username', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 164,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 162,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 161,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Student Number\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 176,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Enter student number\",\n                            value: studentData.studentNumber,\n                            onChange: e => handleStudentChange('studentNumber', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 177,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 175,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 174,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Email Address\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 190,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"email\",\n                        placeholder: \"Enter email\",\n                        value: studentData.email,\n                        onChange: e => handleStudentChange('email', e.target.value),\n                        required: true,\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 191,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 202,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"password\",\n                        placeholder: \"Password\",\n                        value: studentData.password,\n                        onChange: e => handleStudentChange('password', e.target.value),\n                        required: true,\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 203,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Row, {\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"First Name\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 216,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"First name\",\n                            value: studentData.firstName,\n                            onChange: e => handleStudentChange('firstName', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 217,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 215,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 214,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Last Name\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 229,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Last name\",\n                            value: studentData.lastName,\n                            onChange: e => handleStudentChange('lastName', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 230,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 228,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 227,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Row, {\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Department\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 245,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Department (optional)\",\n                            value: studentData.department,\n                            onChange: e => handleStudentChange('department', e.target.value),\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 246,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 244,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 243,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Year\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 257,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"number\",\n                            placeholder: \"Year (optional)\",\n                            value: studentData.year || '',\n                            onChange: e => handleStudentChange('year', e.target.value ? parseInt(e.target.value) : undefined),\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 258,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 256,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 255,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Phone Number\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 270,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"tel\",\n                        placeholder: \"Phone number (optional)\",\n                        value: studentData.phoneNumber,\n                        onChange: e => handleStudentChange('phoneNumber', e.target.value),\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 271,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"primary\",\n                      type: \"submit\",\n                      className: \"w-100 mb-3\",\n                      disabled: isLoading,\n                      children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                          as: \"span\",\n                          animation: \"border\",\n                          size: \"sm\",\n                          role: \"status\",\n                          \"aria-hidden\": \"true\",\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 288,\n                          columnNumber: 25\n                        }, this), \"Creating Account...\"]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-user-plus me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 300,\n                          columnNumber: 25\n                        }, this), \"Register as Student\"]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 17\n                  }, this) : /*#__PURE__*/_jsxDEV(Form, {\n                    onSubmit: handleXeroxSubmit,\n                    children: [/*#__PURE__*/_jsxDEV(Row, {\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Username\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 311,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Enter username\",\n                            value: xeroxData.username,\n                            onChange: e => handleXeroxChange('username', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 312,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 310,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 309,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Xerox Center Name\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 324,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Center name\",\n                            value: xeroxData.xeroxCenterName,\n                            onChange: e => handleXeroxChange('xeroxCenterName', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 325,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 323,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 322,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Email Address\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 338,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"email\",\n                        placeholder: \"Enter email\",\n                        value: xeroxData.email,\n                        onChange: e => handleXeroxChange('email', e.target.value),\n                        required: true,\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 339,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 350,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"password\",\n                        placeholder: \"Password\",\n                        value: xeroxData.password,\n                        onChange: e => handleXeroxChange('password', e.target.value),\n                        required: true,\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 351,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Location\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 362,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"text\",\n                        placeholder: \"Center location\",\n                        value: xeroxData.location,\n                        onChange: e => handleXeroxChange('location', e.target.value),\n                        required: true,\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Row, {\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Contact Person\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 376,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            placeholder: \"Contact person (optional)\",\n                            value: xeroxData.contactPerson,\n                            onChange: e => handleXeroxChange('contactPerson', e.target.value),\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 377,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 375,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 374,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            children: \"Phone Number\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 388,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"tel\",\n                            placeholder: \"Phone number\",\n                            value: xeroxData.phoneNumber,\n                            onChange: e => handleXeroxChange('phoneNumber', e.target.value),\n                            required: true,\n                            disabled: isLoading\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 389,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 387,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 386,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Description\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 402,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        as: \"textarea\",\n                        rows: 3,\n                        placeholder: \"Center description (optional)\",\n                        value: xeroxData.description,\n                        onChange: e => handleXeroxChange('description', e.target.value),\n                        disabled: isLoading\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 403,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"primary\",\n                      type: \"submit\",\n                      className: \"w-100 mb-3\",\n                      disabled: isLoading,\n                      children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                          as: \"span\",\n                          animation: \"border\",\n                          size: \"sm\",\n                          role: \"status\",\n                          \"aria-hidden\": \"true\",\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 421,\n                          columnNumber: 25\n                        }, this), \"Creating Account...\"]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-store me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 433,\n                          columnNumber: 25\n                        }, this), \"Register as Xerox Center\"]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                    style: {\n                      margin: '2rem 0',\n                      borderColor: isDarkMode ? '#404040' : '#e9ecef'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/login\",\n                      className: \"text-decoration-none\",\n                      style: {\n                        color: '#667eea',\n                        fontWeight: '500',\n                        transition: 'all 0.3s ease'\n                      },\n                      onMouseEnter: e => e.target.style.color = '#764ba2',\n                      onMouseLeave: e => e.target.style.color = '#667eea',\n                      children: \"Already have an account? Login!\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"innhoHiNPBQ7HtBFsbfyS9Rqisk=\", false, function () {\n  return [useAuth, useTheme, useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Nav", "Link", "useNavigate", "motion", "UserPlus", "GraduationCap", "Store", "useAuth", "useTheme", "Layout", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Register", "_s", "activeTab", "setActiveTab", "studentData", "setStudentData", "username", "email", "password", "studentNumber", "firstName", "lastName", "phoneNumber", "department", "year", "undefined", "xeroxData", "setXeroxData", "xeroxCenterName", "location", "<PERSON><PERSON><PERSON>", "description", "registerStudent", "registerXeroxCenter", "isLoading", "error", "user", "isDarkMode", "navigate", "handleStudentSubmit", "e", "preventDefault", "success", "handleXeroxSubmit", "handleStudentChange", "field", "value", "prev", "handleXeroxChange", "cardVariants", "hidden", "opacity", "y", "scale", "visible", "transition", "duration", "ease", "children", "className", "style", "minHeight", "md", "lg", "div", "variants", "initial", "animate", "borderRadius", "overflow", "background", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "rotate", "delay", "marginTop", "size", "color", "variant", "<PERSON><PERSON>", "active", "onClick", "fontWeight", "border", "onSubmit", "Group", "Label", "Control", "type", "placeholder", "onChange", "target", "required", "disabled", "parseInt", "as", "animation", "role", "rows", "margin", "borderColor", "to", "onMouseEnter", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/Register.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, But<PERSON>, Al<PERSON>, Spinner, Nav } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { UserPlus, GraduationCap, Store } from 'lucide-react';\nimport { useAuth, StudentRegistrationData, XeroxCenterRegistrationData } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport Layout from './Layout';\n\nconst Register: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<'student' | 'xerox'>('student');\n  const [studentData, setStudentData] = useState<StudentRegistrationData>({\n    username: '',\n    email: '',\n    password: '',\n    studentNumber: '',\n    firstName: '',\n    lastName: '',\n    phoneNumber: '',\n    department: '',\n    year: undefined\n  });\n  const [xeroxData, setXeroxData] = useState<XeroxCenterRegistrationData>({\n    username: '',\n    email: '',\n    password: '',\n    xeroxCenterName: '',\n    location: '',\n    contactPerson: '',\n    phoneNumber: '',\n    description: ''\n  });\n\n  const { registerStudent, registerXeroxCenter, isLoading, error, user } = useAuth();\n  const { isDarkMode } = useTheme();\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n\n  const handleStudentSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    const success = await registerStudent(studentData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n\n  const handleXeroxSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    const success = await registerXeroxCenter(xeroxData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n\n  const handleStudentChange = (field: keyof StudentRegistrationData, value: string | number | undefined) => {\n    setStudentData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleXeroxChange = (field: keyof XeroxCenterRegistrationData, value: string) => {\n    setXeroxData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, y: 50, scale: 0.9 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] }\n    }\n  };\n\n  return (\n    <Layout>\n      <Container className=\"d-flex align-items-center justify-content-center py-5\" style={{ minHeight: '100vh' }}>\n        <Row className=\"justify-content-center w-100\">\n          <Col md={8} lg={6}>\n            <motion.div\n              variants={cardVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n            >\n              <Card className={`shadow-lg border-0 ${isDarkMode ? 'bg-dark text-light' : ''}`} style={{ borderRadius: '20px', overflow: 'hidden' }}>\n                <div className=\"position-relative\">\n                  <div\n                    className=\"w-100 h-100 position-absolute\"\n                    style={{\n                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                      height: '100px'\n                    }}\n                  />\n                  <Card.Body className=\"p-4 position-relative\">\n                    <div className=\"text-center mb-4\">\n                      <motion.div\n                        initial={{ scale: 0, rotate: -180 }}\n                        animate={{ scale: 1, rotate: 0 }}\n                        transition={{ delay: 0.3, duration: 0.5 }}\n                        className=\"d-inline-block p-3 rounded-circle bg-white shadow-sm mb-3\"\n                        style={{ marginTop: '10px' }}\n                      >\n                        <UserPlus size={32} className=\"text-primary\" />\n                      </motion.div>\n                      <h2 className=\"h4 mb-2\" style={{ color: isDarkMode ? '#fff' : '#333', marginTop: '40px' }}>Create Account</h2>\n                      <p className=\"text-muted small\">Join XeroxHub today</p>\n                    </div>\n\n                    <Nav variant=\"pills\" className=\"mb-4 justify-content-center\">\n                      <Nav.Item>\n                        <Nav.Link\n                          active={activeTab === 'student'}\n                          onClick={() => setActiveTab('student')}\n                          className={`px-4 py-2 mx-1 ${activeTab === 'student' ? 'btn-gradient' : ''}`}\n                          style={{\n                            borderRadius: '25px',\n                            fontWeight: '500',\n                            transition: 'all 0.3s ease',\n                            background: activeTab === 'student' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                            border: activeTab === 'student' ? 'none' : '2px solid #e9ecef',\n                            color: activeTab === 'student' ? 'white' : (isDarkMode ? '#fff' : '#333')\n                          }}\n                        >\n                          <GraduationCap size={16} className=\"me-2\" />\n                          Student\n                        </Nav.Link>\n                      </Nav.Item>\n                      <Nav.Item>\n                        <Nav.Link\n                          active={activeTab === 'xerox'}\n                          onClick={() => setActiveTab('xerox')}\n                          className={`px-4 py-2 mx-1 ${activeTab === 'xerox' ? 'btn-gradient' : ''}`}\n                          style={{\n                            borderRadius: '25px',\n                            fontWeight: '500',\n                            transition: 'all 0.3s ease',\n                            background: activeTab === 'xerox' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                            border: activeTab === 'xerox' ? 'none' : '2px solid #e9ecef',\n                            color: activeTab === 'xerox' ? 'white' : (isDarkMode ? '#fff' : '#333')\n                          }}\n                        >\n                          <Store size={16} className=\"me-2\" />\n                          Xerox Center\n                        </Nav.Link>\n                      </Nav.Item>\n                    </Nav>\n\n              {error && (\n                <Alert variant=\"danger\" className=\"mb-3\">\n                  <i className=\"fas fa-exclamation-triangle me-2\"></i>\n                  {error}\n                </Alert>\n              )}\n\n              {activeTab === 'student' ? (\n                <Form onSubmit={handleStudentSubmit}>\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Username</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Enter username\"\n                          value={studentData.username}\n                          onChange={(e) => handleStudentChange('username', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Student Number</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Enter student number\"\n                          value={studentData.studentNumber}\n                          onChange={(e) => handleStudentChange('studentNumber', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Email Address</Form.Label>\n                    <Form.Control\n                      type=\"email\"\n                      placeholder=\"Enter email\"\n                      value={studentData.email}\n                      onChange={(e) => handleStudentChange('email', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Password</Form.Label>\n                    <Form.Control\n                      type=\"password\"\n                      placeholder=\"Password\"\n                      value={studentData.password}\n                      onChange={(e) => handleStudentChange('password', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>First Name</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"First name\"\n                          value={studentData.firstName}\n                          onChange={(e) => handleStudentChange('firstName', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Last Name</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Last name\"\n                          value={studentData.lastName}\n                          onChange={(e) => handleStudentChange('lastName', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Department</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Department (optional)\"\n                          value={studentData.department}\n                          onChange={(e) => handleStudentChange('department', e.target.value)}\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Year</Form.Label>\n                        <Form.Control\n                          type=\"number\"\n                          placeholder=\"Year (optional)\"\n                          value={studentData.year || ''}\n                          onChange={(e) => handleStudentChange('year', e.target.value ? parseInt(e.target.value) : undefined)}\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Phone Number</Form.Label>\n                    <Form.Control\n                      type=\"tel\"\n                      placeholder=\"Phone number (optional)\"\n                      value={studentData.phoneNumber}\n                      onChange={(e) => handleStudentChange('phoneNumber', e.target.value)}\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Button \n                    variant=\"primary\" \n                    type=\"submit\" \n                    className=\"w-100 mb-3\"\n                    disabled={isLoading}\n                  >\n                    {isLoading ? (\n                      <>\n                        <Spinner\n                          as=\"span\"\n                          animation=\"border\"\n                          size=\"sm\"\n                          role=\"status\"\n                          aria-hidden=\"true\"\n                          className=\"me-2\"\n                        />\n                        Creating Account...\n                      </>\n                    ) : (\n                      <>\n                        <i className=\"fas fa-user-plus me-2\"></i>\n                        Register as Student\n                      </>\n                    )}\n                  </Button>\n                </Form>\n              ) : (\n                <Form onSubmit={handleXeroxSubmit}>\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Username</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Enter username\"\n                          value={xeroxData.username}\n                          onChange={(e) => handleXeroxChange('username', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Xerox Center Name</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Center name\"\n                          value={xeroxData.xeroxCenterName}\n                          onChange={(e) => handleXeroxChange('xeroxCenterName', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Email Address</Form.Label>\n                    <Form.Control\n                      type=\"email\"\n                      placeholder=\"Enter email\"\n                      value={xeroxData.email}\n                      onChange={(e) => handleXeroxChange('email', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Password</Form.Label>\n                    <Form.Control\n                      type=\"password\"\n                      placeholder=\"Password\"\n                      value={xeroxData.password}\n                      onChange={(e) => handleXeroxChange('password', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Location</Form.Label>\n                    <Form.Control\n                      type=\"text\"\n                      placeholder=\"Center location\"\n                      value={xeroxData.location}\n                      onChange={(e) => handleXeroxChange('location', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Contact Person</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Contact person (optional)\"\n                          value={xeroxData.contactPerson}\n                          onChange={(e) => handleXeroxChange('contactPerson', e.target.value)}\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Phone Number</Form.Label>\n                        <Form.Control\n                          type=\"tel\"\n                          placeholder=\"Phone number\"\n                          value={xeroxData.phoneNumber}\n                          onChange={(e) => handleXeroxChange('phoneNumber', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Description</Form.Label>\n                    <Form.Control\n                      as=\"textarea\"\n                      rows={3}\n                      placeholder=\"Center description (optional)\"\n                      value={xeroxData.description}\n                      onChange={(e) => handleXeroxChange('description', e.target.value)}\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Button \n                    variant=\"primary\" \n                    type=\"submit\" \n                    className=\"w-100 mb-3\"\n                    disabled={isLoading}\n                  >\n                    {isLoading ? (\n                      <>\n                        <Spinner\n                          as=\"span\"\n                          animation=\"border\"\n                          size=\"sm\"\n                          role=\"status\"\n                          aria-hidden=\"true\"\n                          className=\"me-2\"\n                        />\n                        Creating Account...\n                      </>\n                    ) : (\n                      <>\n                        <i className=\"fas fa-store me-2\"></i>\n                        Register as Xerox Center\n                      </>\n                    )}\n                  </Button>\n                </Form>\n              )}\n\n                    <hr style={{ margin: '2rem 0', borderColor: isDarkMode ? '#404040' : '#e9ecef' }} />\n\n                    <div className=\"text-center\">\n                      <Link\n                        to=\"/login\"\n                        className=\"text-decoration-none\"\n                        style={{\n                          color: '#667eea',\n                          fontWeight: '500',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#764ba2'}\n                        onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#667eea'}\n                      >\n                        Already have an account? Login!\n                      </Link>\n                    </div>\n                  </Card.Body>\n                </div>\n              </Card>\n            </motion.div>\n          </Col>\n        </Row>\n      </Container>\n    </Layout>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,QAAQ,iBAAiB;AAC9F,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,EAAEC,aAAa,EAAEC,KAAK,QAAQ,cAAc;AAC7D,SAASC,OAAO,QAA8D,yBAAyB;AACvG,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAsB,SAAS,CAAC;EAC1E,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAA0B;IACtE8B,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAEC;EACR,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAA8B;IACtE8B,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZU,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBR,WAAW,EAAE,EAAE;IACfS,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM;IAAEC,eAAe;IAAEC,mBAAmB;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGjC,OAAO,CAAC,CAAC;EAClF,MAAM;IAAEkC;EAAW,CAAC,GAAGjC,QAAQ,CAAC,CAAC;EACjC,MAAMkC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAE9BX,SAAS,CAAC,MAAM;IACd,IAAIiD,IAAI,EAAE;MACRE,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACF,IAAI,EAAEE,QAAQ,CAAC,CAAC;EAEpB,MAAMC,mBAAmB,GAAG,MAAOC,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,OAAO,GAAG,MAAMV,eAAe,CAAClB,WAAW,CAAC;IAClD,IAAI4B,OAAO,EAAE;MACXJ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAG,MAAOH,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,OAAO,GAAG,MAAMT,mBAAmB,CAACP,SAAS,CAAC;IACpD,IAAIgB,OAAO,EAAE;MACXJ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMM,mBAAmB,GAAGA,CAACC,KAAoC,EAAEC,KAAkC,KAAK;IACxG/B,cAAc,CAACgC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACvD,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAACH,KAAwC,EAAEC,KAAa,KAAK;IACrFnB,YAAY,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,YAAY,GAAG;IACnBC,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAC;IACzCC,OAAO,EAAE;MACPH,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAE,CAAC;MACRE,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;MAAE;IAC9D;EACF,CAAC;EAED,oBACElD,OAAA,CAACF,MAAM;IAAAqD,QAAA,eACLnD,OAAA,CAACnB,SAAS;MAACuE,SAAS,EAAC,uDAAuD;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAAH,QAAA,eACzGnD,OAAA,CAAClB,GAAG;QAACsE,SAAS,EAAC,8BAA8B;QAAAD,QAAA,eAC3CnD,OAAA,CAACjB,GAAG;UAACwE,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAL,QAAA,eAChBnD,OAAA,CAACR,MAAM,CAACiE,GAAG;YACTC,QAAQ,EAAEhB,YAAa;YACvBiB,OAAO,EAAC,QAAQ;YAChBC,OAAO,EAAC,SAAS;YAAAT,QAAA,eAEjBnD,OAAA,CAAChB,IAAI;cAACoE,SAAS,EAAE,sBAAsBtB,UAAU,GAAG,oBAAoB,GAAG,EAAE,EAAG;cAACuB,KAAK,EAAE;gBAAEQ,YAAY,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAS,CAAE;cAAAX,QAAA,eACnInD,OAAA;gBAAKoD,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChCnD,OAAA;kBACEoD,SAAS,EAAC,+BAA+B;kBACzCC,KAAK,EAAE;oBACLU,UAAU,EAAE,mDAAmD;oBAC/DC,MAAM,EAAE;kBACV;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFpE,OAAA,CAAChB,IAAI,CAACqF,IAAI;kBAACjB,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,gBAC1CnD,OAAA;oBAAKoD,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC/BnD,OAAA,CAACR,MAAM,CAACiE,GAAG;sBACTE,OAAO,EAAE;wBAAEb,KAAK,EAAE,CAAC;wBAAEwB,MAAM,EAAE,CAAC;sBAAI,CAAE;sBACpCV,OAAO,EAAE;wBAAEd,KAAK,EAAE,CAAC;wBAAEwB,MAAM,EAAE;sBAAE,CAAE;sBACjCtB,UAAU,EAAE;wBAAEuB,KAAK,EAAE,GAAG;wBAAEtB,QAAQ,EAAE;sBAAI,CAAE;sBAC1CG,SAAS,EAAC,2DAA2D;sBACrEC,KAAK,EAAE;wBAAEmB,SAAS,EAAE;sBAAO,CAAE;sBAAArB,QAAA,eAE7BnD,OAAA,CAACP,QAAQ;wBAACgF,IAAI,EAAE,EAAG;wBAACrB,SAAS,EAAC;sBAAc;wBAAAa,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC,eACbpE,OAAA;sBAAIoD,SAAS,EAAC,SAAS;sBAACC,KAAK,EAAE;wBAAEqB,KAAK,EAAE5C,UAAU,GAAG,MAAM,GAAG,MAAM;wBAAE0C,SAAS,EAAE;sBAAO,CAAE;sBAAArB,QAAA,EAAC;oBAAc;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9GpE,OAAA;sBAAGoD,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAAmB;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eAENpE,OAAA,CAACX,GAAG;oBAACsF,OAAO,EAAC,OAAO;oBAACvB,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1DnD,OAAA,CAACX,GAAG,CAACuF,IAAI;sBAAAzB,QAAA,eACPnD,OAAA,CAACX,GAAG,CAACC,IAAI;wBACPuF,MAAM,EAAExE,SAAS,KAAK,SAAU;wBAChCyE,OAAO,EAAEA,CAAA,KAAMxE,YAAY,CAAC,SAAS,CAAE;wBACvC8C,SAAS,EAAE,kBAAkB/C,SAAS,KAAK,SAAS,GAAG,cAAc,GAAG,EAAE,EAAG;wBAC7EgD,KAAK,EAAE;0BACLQ,YAAY,EAAE,MAAM;0BACpBkB,UAAU,EAAE,KAAK;0BACjB/B,UAAU,EAAE,eAAe;0BAC3Be,UAAU,EAAE1D,SAAS,KAAK,SAAS,GAAG,mDAAmD,GAAG,aAAa;0BACzG2E,MAAM,EAAE3E,SAAS,KAAK,SAAS,GAAG,MAAM,GAAG,mBAAmB;0BAC9DqE,KAAK,EAAErE,SAAS,KAAK,SAAS,GAAG,OAAO,GAAIyB,UAAU,GAAG,MAAM,GAAG;wBACpE,CAAE;wBAAAqB,QAAA,gBAEFnD,OAAA,CAACN,aAAa;0BAAC+E,IAAI,EAAE,EAAG;0BAACrB,SAAS,EAAC;wBAAM;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,WAE9C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAU;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACXpE,OAAA,CAACX,GAAG,CAACuF,IAAI;sBAAAzB,QAAA,eACPnD,OAAA,CAACX,GAAG,CAACC,IAAI;wBACPuF,MAAM,EAAExE,SAAS,KAAK,OAAQ;wBAC9ByE,OAAO,EAAEA,CAAA,KAAMxE,YAAY,CAAC,OAAO,CAAE;wBACrC8C,SAAS,EAAE,kBAAkB/C,SAAS,KAAK,OAAO,GAAG,cAAc,GAAG,EAAE,EAAG;wBAC3EgD,KAAK,EAAE;0BACLQ,YAAY,EAAE,MAAM;0BACpBkB,UAAU,EAAE,KAAK;0BACjB/B,UAAU,EAAE,eAAe;0BAC3Be,UAAU,EAAE1D,SAAS,KAAK,OAAO,GAAG,mDAAmD,GAAG,aAAa;0BACvG2E,MAAM,EAAE3E,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,mBAAmB;0BAC5DqE,KAAK,EAAErE,SAAS,KAAK,OAAO,GAAG,OAAO,GAAIyB,UAAU,GAAG,MAAM,GAAG;wBAClE,CAAE;wBAAAqB,QAAA,gBAEFnD,OAAA,CAACL,KAAK;0BAAC8E,IAAI,EAAE,EAAG;0BAACrB,SAAS,EAAC;wBAAM;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAEtC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAU;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,EAEXxC,KAAK,iBACJ5B,OAAA,CAACb,KAAK;oBAACwF,OAAO,EAAC,QAAQ;oBAACvB,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACtCnD,OAAA;sBAAGoD,SAAS,EAAC;oBAAkC;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACnDxC,KAAK;kBAAA;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CACR,EAEA/D,SAAS,KAAK,SAAS,gBACtBL,OAAA,CAACf,IAAI;oBAACgG,QAAQ,EAAEjD,mBAAoB;oBAAAmB,QAAA,gBAClCnD,OAAA,CAAClB,GAAG;sBAAAqE,QAAA,gBACFnD,OAAA,CAACjB,GAAG;wBAACwE,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACTnD,OAAA,CAACf,IAAI,CAACiG,KAAK;0BAAC9B,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;4BAAAhC,QAAA,EAAC;0BAAQ;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACjCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,gBAAgB;4BAC5B/C,KAAK,EAAEhC,WAAW,CAACE,QAAS;4BAC5B8E,QAAQ,EAAGtD,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;4BACjEkD,QAAQ;4BACRC,QAAQ,EAAE/D;0BAAU;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNpE,OAAA,CAACjB,GAAG;wBAACwE,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACTnD,OAAA,CAACf,IAAI,CAACiG,KAAK;0BAAC9B,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;4BAAAhC,QAAA,EAAC;0BAAc;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACvCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,sBAAsB;4BAClC/C,KAAK,EAAEhC,WAAW,CAACK,aAAc;4BACjC2E,QAAQ,EAAGtD,CAAC,IAAKI,mBAAmB,CAAC,eAAe,EAAEJ,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;4BACtEkD,QAAQ;4BACRC,QAAQ,EAAE/D;0BAAU;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENpE,OAAA,CAACf,IAAI,CAACiG,KAAK;sBAAC9B,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;wBAAAhC,QAAA,EAAC;sBAAa;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACtCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;wBACXC,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,aAAa;wBACzB/C,KAAK,EAAEhC,WAAW,CAACG,KAAM;wBACzB6E,QAAQ,EAAGtD,CAAC,IAAKI,mBAAmB,CAAC,OAAO,EAAEJ,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;wBAC9DkD,QAAQ;wBACRC,QAAQ,EAAE/D;sBAAU;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEbpE,OAAA,CAACf,IAAI,CAACiG,KAAK;sBAAC9B,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;wBAAAhC,QAAA,EAAC;sBAAQ;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;wBACXC,IAAI,EAAC,UAAU;wBACfC,WAAW,EAAC,UAAU;wBACtB/C,KAAK,EAAEhC,WAAW,CAACI,QAAS;wBAC5B4E,QAAQ,EAAGtD,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;wBACjEkD,QAAQ;wBACRC,QAAQ,EAAE/D;sBAAU;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEbpE,OAAA,CAAClB,GAAG;sBAAAqE,QAAA,gBACFnD,OAAA,CAACjB,GAAG;wBAACwE,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACTnD,OAAA,CAACf,IAAI,CAACiG,KAAK;0BAAC9B,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;4BAAAhC,QAAA,EAAC;0BAAU;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACnCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,YAAY;4BACxB/C,KAAK,EAAEhC,WAAW,CAACM,SAAU;4BAC7B0E,QAAQ,EAAGtD,CAAC,IAAKI,mBAAmB,CAAC,WAAW,EAAEJ,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;4BAClEkD,QAAQ;4BACRC,QAAQ,EAAE/D;0BAAU;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNpE,OAAA,CAACjB,GAAG;wBAACwE,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACTnD,OAAA,CAACf,IAAI,CAACiG,KAAK;0BAAC9B,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;4BAAAhC,QAAA,EAAC;0BAAS;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eAClCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,WAAW;4BACvB/C,KAAK,EAAEhC,WAAW,CAACO,QAAS;4BAC5ByE,QAAQ,EAAGtD,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;4BACjEkD,QAAQ;4BACRC,QAAQ,EAAE/D;0BAAU;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENpE,OAAA,CAAClB,GAAG;sBAAAqE,QAAA,gBACFnD,OAAA,CAACjB,GAAG;wBAACwE,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACTnD,OAAA,CAACf,IAAI,CAACiG,KAAK;0BAAC9B,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;4BAAAhC,QAAA,EAAC;0BAAU;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACnCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,uBAAuB;4BACnC/C,KAAK,EAAEhC,WAAW,CAACS,UAAW;4BAC9BuE,QAAQ,EAAGtD,CAAC,IAAKI,mBAAmB,CAAC,YAAY,EAAEJ,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;4BACnEmD,QAAQ,EAAE/D;0BAAU;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNpE,OAAA,CAACjB,GAAG;wBAACwE,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACTnD,OAAA,CAACf,IAAI,CAACiG,KAAK;0BAAC9B,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;4BAAAhC,QAAA,EAAC;0BAAI;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eAC7BpE,OAAA,CAACf,IAAI,CAACmG,OAAO;4BACXC,IAAI,EAAC,QAAQ;4BACbC,WAAW,EAAC,iBAAiB;4BAC7B/C,KAAK,EAAEhC,WAAW,CAACU,IAAI,IAAI,EAAG;4BAC9BsE,QAAQ,EAAGtD,CAAC,IAAKI,mBAAmB,CAAC,MAAM,EAAEJ,CAAC,CAACuD,MAAM,CAACjD,KAAK,GAAGoD,QAAQ,CAAC1D,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAC,GAAGrB,SAAS,CAAE;4BACpGwE,QAAQ,EAAE/D;0BAAU;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENpE,OAAA,CAACf,IAAI,CAACiG,KAAK;sBAAC9B,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;wBAAAhC,QAAA,EAAC;sBAAY;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACrCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;wBACXC,IAAI,EAAC,KAAK;wBACVC,WAAW,EAAC,yBAAyB;wBACrC/C,KAAK,EAAEhC,WAAW,CAACQ,WAAY;wBAC/BwE,QAAQ,EAAGtD,CAAC,IAAKI,mBAAmB,CAAC,aAAa,EAAEJ,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;wBACpEmD,QAAQ,EAAE/D;sBAAU;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEbpE,OAAA,CAACd,MAAM;sBACLyF,OAAO,EAAC,SAAS;sBACjBU,IAAI,EAAC,QAAQ;sBACbjC,SAAS,EAAC,YAAY;sBACtBsC,QAAQ,EAAE/D,SAAU;sBAAAwB,QAAA,EAEnBxB,SAAS,gBACR3B,OAAA,CAAAE,SAAA;wBAAAiD,QAAA,gBACEnD,OAAA,CAACZ,OAAO;0BACNwG,EAAE,EAAC,MAAM;0BACTC,SAAS,EAAC,QAAQ;0BAClBpB,IAAI,EAAC,IAAI;0BACTqB,IAAI,EAAC,QAAQ;0BACb,eAAY,MAAM;0BAClB1C,SAAS,EAAC;wBAAM;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,uBAEJ;sBAAA,eAAE,CAAC,gBAEHpE,OAAA,CAAAE,SAAA;wBAAAiD,QAAA,gBACEnD,OAAA;0BAAGoD,SAAS,EAAC;wBAAuB;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,uBAE3C;sBAAA,eAAE;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAEPpE,OAAA,CAACf,IAAI;oBAACgG,QAAQ,EAAE7C,iBAAkB;oBAAAe,QAAA,gBAChCnD,OAAA,CAAClB,GAAG;sBAAAqE,QAAA,gBACFnD,OAAA,CAACjB,GAAG;wBAACwE,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACTnD,OAAA,CAACf,IAAI,CAACiG,KAAK;0BAAC9B,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;4BAAAhC,QAAA,EAAC;0BAAQ;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACjCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,gBAAgB;4BAC5B/C,KAAK,EAAEpB,SAAS,CAACV,QAAS;4BAC1B8E,QAAQ,EAAGtD,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;4BAC/DkD,QAAQ;4BACRC,QAAQ,EAAE/D;0BAAU;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNpE,OAAA,CAACjB,GAAG;wBAACwE,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACTnD,OAAA,CAACf,IAAI,CAACiG,KAAK;0BAAC9B,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;4BAAAhC,QAAA,EAAC;0BAAiB;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eAC1CpE,OAAA,CAACf,IAAI,CAACmG,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,aAAa;4BACzB/C,KAAK,EAAEpB,SAAS,CAACE,eAAgB;4BACjCkE,QAAQ,EAAGtD,CAAC,IAAKQ,iBAAiB,CAAC,iBAAiB,EAAER,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;4BACtEkD,QAAQ;4BACRC,QAAQ,EAAE/D;0BAAU;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENpE,OAAA,CAACf,IAAI,CAACiG,KAAK;sBAAC9B,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;wBAAAhC,QAAA,EAAC;sBAAa;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACtCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;wBACXC,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,aAAa;wBACzB/C,KAAK,EAAEpB,SAAS,CAACT,KAAM;wBACvB6E,QAAQ,EAAGtD,CAAC,IAAKQ,iBAAiB,CAAC,OAAO,EAAER,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;wBAC5DkD,QAAQ;wBACRC,QAAQ,EAAE/D;sBAAU;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEbpE,OAAA,CAACf,IAAI,CAACiG,KAAK;sBAAC9B,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;wBAAAhC,QAAA,EAAC;sBAAQ;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;wBACXC,IAAI,EAAC,UAAU;wBACfC,WAAW,EAAC,UAAU;wBACtB/C,KAAK,EAAEpB,SAAS,CAACR,QAAS;wBAC1B4E,QAAQ,EAAGtD,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;wBAC/DkD,QAAQ;wBACRC,QAAQ,EAAE/D;sBAAU;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEbpE,OAAA,CAACf,IAAI,CAACiG,KAAK;sBAAC9B,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;wBAAAhC,QAAA,EAAC;sBAAQ;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;wBACXC,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,iBAAiB;wBAC7B/C,KAAK,EAAEpB,SAAS,CAACG,QAAS;wBAC1BiE,QAAQ,EAAGtD,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;wBAC/DkD,QAAQ;wBACRC,QAAQ,EAAE/D;sBAAU;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEbpE,OAAA,CAAClB,GAAG;sBAAAqE,QAAA,gBACFnD,OAAA,CAACjB,GAAG;wBAACwE,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACTnD,OAAA,CAACf,IAAI,CAACiG,KAAK;0BAAC9B,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;4BAAAhC,QAAA,EAAC;0BAAc;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACvCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXC,WAAW,EAAC,2BAA2B;4BACvC/C,KAAK,EAAEpB,SAAS,CAACI,aAAc;4BAC/BgE,QAAQ,EAAGtD,CAAC,IAAKQ,iBAAiB,CAAC,eAAe,EAAER,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;4BACpEmD,QAAQ,EAAE/D;0BAAU;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNpE,OAAA,CAACjB,GAAG;wBAACwE,EAAE,EAAE,CAAE;wBAAAJ,QAAA,eACTnD,OAAA,CAACf,IAAI,CAACiG,KAAK;0BAAC9B,SAAS,EAAC,MAAM;0BAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;4BAAAhC,QAAA,EAAC;0BAAY;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACrCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;4BACXC,IAAI,EAAC,KAAK;4BACVC,WAAW,EAAC,cAAc;4BAC1B/C,KAAK,EAAEpB,SAAS,CAACJ,WAAY;4BAC7BwE,QAAQ,EAAGtD,CAAC,IAAKQ,iBAAiB,CAAC,aAAa,EAAER,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;4BAClEkD,QAAQ;4BACRC,QAAQ,EAAE/D;0BAAU;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENpE,OAAA,CAACf,IAAI,CAACiG,KAAK;sBAAC9B,SAAS,EAAC,MAAM;sBAAAD,QAAA,gBAC1BnD,OAAA,CAACf,IAAI,CAACkG,KAAK;wBAAAhC,QAAA,EAAC;sBAAW;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACpCpE,OAAA,CAACf,IAAI,CAACmG,OAAO;wBACXQ,EAAE,EAAC,UAAU;wBACbG,IAAI,EAAE,CAAE;wBACRT,WAAW,EAAC,+BAA+B;wBAC3C/C,KAAK,EAAEpB,SAAS,CAACK,WAAY;wBAC7B+D,QAAQ,EAAGtD,CAAC,IAAKQ,iBAAiB,CAAC,aAAa,EAAER,CAAC,CAACuD,MAAM,CAACjD,KAAK,CAAE;wBAClEmD,QAAQ,EAAE/D;sBAAU;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eAEbpE,OAAA,CAACd,MAAM;sBACLyF,OAAO,EAAC,SAAS;sBACjBU,IAAI,EAAC,QAAQ;sBACbjC,SAAS,EAAC,YAAY;sBACtBsC,QAAQ,EAAE/D,SAAU;sBAAAwB,QAAA,EAEnBxB,SAAS,gBACR3B,OAAA,CAAAE,SAAA;wBAAAiD,QAAA,gBACEnD,OAAA,CAACZ,OAAO;0BACNwG,EAAE,EAAC,MAAM;0BACTC,SAAS,EAAC,QAAQ;0BAClBpB,IAAI,EAAC,IAAI;0BACTqB,IAAI,EAAC,QAAQ;0BACb,eAAY,MAAM;0BAClB1C,SAAS,EAAC;wBAAM;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,uBAEJ;sBAAA,eAAE,CAAC,gBAEHpE,OAAA,CAAAE,SAAA;wBAAAiD,QAAA,gBACEnD,OAAA;0BAAGoD,SAAS,EAAC;wBAAmB;0BAAAa,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,4BAEvC;sBAAA,eAAE;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACP,eAEKpE,OAAA;oBAAIqD,KAAK,EAAE;sBAAE2C,MAAM,EAAE,QAAQ;sBAAEC,WAAW,EAAEnE,UAAU,GAAG,SAAS,GAAG;oBAAU;kBAAE;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEpFpE,OAAA;oBAAKoD,SAAS,EAAC,aAAa;oBAAAD,QAAA,eAC1BnD,OAAA,CAACV,IAAI;sBACH4G,EAAE,EAAC,QAAQ;sBACX9C,SAAS,EAAC,sBAAsB;sBAChCC,KAAK,EAAE;wBACLqB,KAAK,EAAE,SAAS;wBAChBK,UAAU,EAAE,KAAK;wBACjB/B,UAAU,EAAE;sBACd,CAAE;sBACFmD,YAAY,EAAGlE,CAAC,IAAMA,CAAC,CAACuD,MAAM,CAAiBnC,KAAK,CAACqB,KAAK,GAAG,SAAU;sBACvE0B,YAAY,EAAGnE,CAAC,IAAMA,CAAC,CAACuD,MAAM,CAAiBnC,KAAK,CAACqB,KAAK,GAAG,SAAU;sBAAAvB,QAAA,EACxE;oBAED;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEb,CAAC;AAAChE,EAAA,CAzcID,QAAkB;EAAA,QAwBmDP,OAAO,EACzDC,QAAQ,EACdN,WAAW;AAAA;AAAA8G,EAAA,GA1BxBlG,QAAkB;AA2cxB,eAAeA,QAAQ;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}