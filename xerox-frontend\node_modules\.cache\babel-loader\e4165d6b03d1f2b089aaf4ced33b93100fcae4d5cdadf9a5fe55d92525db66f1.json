{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\XeroxCenterDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Clock, CheckCircle, DollarSign, Info, Check, Activity, Eye, X, AlertTriangle, ArrowUp, ArrowDown, Minus } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, fileUploadApi, messageApi } from '../services/api';\nimport { cn } from '../lib/utils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst XeroxCenterDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [quoteData, setQuoteData] = useState({\n    cost: '',\n    estimatedHours: '',\n    notes: ''\n  });\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [sortBy, setSortBy] = useState('created');\n  const [sortOrder, setSortOrder] = useState('desc');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [viewMode, setViewMode] = useState('table');\n  const [showJobDetailsModal, setShowJobDetailsModal] = useState(false);\n  const [refreshInterval, setRefreshInterval] = useState(null);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs for this xerox center\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error fetching print jobs:', error);\n        setPrintJobs([]);\n      }\n    };\n    fetchData();\n\n    // Set up auto-refresh every 30 seconds\n    const interval = setInterval(fetchData, 30000);\n    setRefreshInterval(interval);\n\n    // Cleanup interval on unmount\n    return () => {\n      if (refreshInterval) {\n        clearInterval(refreshInterval);\n      }\n      clearInterval(interval);\n    };\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        color: 'bg-gray-100 text-gray-800 border-gray-200',\n        icon: Clock\n      },\n      'UnderReview': {\n        color: 'bg-blue-100 text-blue-800 border-blue-200',\n        icon: Eye\n      },\n      'Quoted': {\n        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n        icon: DollarSign\n      },\n      'WaitingConfirmation': {\n        color: 'bg-orange-100 text-orange-800 border-orange-200',\n        icon: Clock\n      },\n      'Confirmed': {\n        color: 'bg-indigo-100 text-indigo-800 border-indigo-200',\n        icon: Check\n      },\n      'InProgress': {\n        color: 'bg-purple-100 text-purple-800 border-purple-200',\n        icon: Activity\n      },\n      'Completed': {\n        color: 'bg-green-100 text-green-800 border-green-200',\n        icon: CheckCircle\n      },\n      'Delivered': {\n        color: 'bg-emerald-100 text-emerald-800 border-emerald-200',\n        icon: CheckCircle\n      },\n      'Rejected': {\n        color: 'bg-red-100 text-red-800 border-red-200',\n        icon: X\n      },\n      'Cancelled': {\n        color: 'bg-gray-100 text-gray-800 border-gray-200',\n        icon: X\n      }\n    };\n    const config = statusConfig[status] || {\n      color: 'bg-gray-100 text-gray-800 border-gray-200',\n      icon: Info\n    };\n    const IconComponent = config.icon;\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        scale: 0.8,\n        opacity: 0\n      },\n      animate: {\n        scale: 1,\n        opacity: 1\n      },\n      className: cn(\"inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border\", config.color),\n      children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n        className: \"w-3 h-3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this);\n  };\n  const getPriorityBadge = (priority = 'Normal') => {\n    const priorityConfig = {\n      'Low': {\n        color: 'bg-slate-100 text-slate-600 border-slate-200',\n        icon: ArrowDown\n      },\n      'Normal': {\n        color: 'bg-blue-100 text-blue-600 border-blue-200',\n        icon: Minus\n      },\n      'High': {\n        color: 'bg-orange-100 text-orange-600 border-orange-200',\n        icon: ArrowUp\n      },\n      'Urgent': {\n        color: 'bg-red-100 text-red-600 border-red-200',\n        icon: AlertTriangle\n      }\n    };\n    const config = priorityConfig[priority] || priorityConfig['Normal'];\n    const IconComponent = config.icon;\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        scale: 0.8,\n        opacity: 0\n      },\n      animate: {\n        scale: 1,\n        opacity: 1\n      },\n      className: cn(\"inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border\", config.color),\n      children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n        className: \"w-3 h-3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), priority]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this);\n  };\n  const getTimeAgo = dateString => {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n    return `${Math.floor(diffInMinutes / 1440)}d ago`;\n  };\n  const formatFileSize = (bytes = 0) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  const handleQuoteSubmit = async () => {\n    if (selectedJob) {\n      try {\n        const estimatedCompletion = new Date();\n        estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));\n        await printJobApi.setJobQuote(selectedJob.id, parseFloat(quoteData.cost), estimatedCompletion.toISOString(), quoteData.notes);\n\n        // Refresh print jobs after update\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error submitting quote:', error);\n      }\n    }\n    setShowQuoteModal(false);\n    setSelectedJob(null);\n    setQuoteData({\n      cost: '',\n      estimatedHours: '',\n      notes: ''\n    });\n  };\n  const handleStatusUpdate = async (jobId, newStatus) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n\n      // Refresh print jobs after update\n      const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error updating status:', error);\n    }\n  };\n  const handleDownloadFile = async (jobId, fileName) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n  const handleOpenChat = async job => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      // Load messages for this job\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n  const handleSendMessage = async () => {\n    if (!newMessage.trim() || !selectedJob) return;\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, newMessage.trim());\n\n      // Add the new message to the list\n      setMessages(prev => [...prev, response.data]);\n      setNewMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n  const getSortedAndFilteredJobs = () => {\n    let filtered = printJobs;\n\n    // Apply status filter\n    if (filterStatus !== 'all') {\n      filtered = filtered.filter(job => job.status === filterStatus);\n    }\n\n    // Apply search filter\n    if (searchTerm) {\n      const term = searchTerm.toLowerCase();\n      filtered = filtered.filter(job => job.jobNumber.toLowerCase().includes(term) || job.fileName.toLowerCase().includes(term) || job.studentName.toLowerCase().includes(term) || job.studentEmail.toLowerCase().includes(term) || job.printType.toLowerCase().includes(term));\n    }\n\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let aValue, bValue;\n      switch (sortBy) {\n        case 'created':\n          aValue = new Date(a.created).getTime();\n          bValue = new Date(b.created).getTime();\n          break;\n        case 'priority':\n          const priorityOrder = {\n            'Urgent': 4,\n            'High': 3,\n            'Normal': 2,\n            'Low': 1\n          };\n          aValue = priorityOrder[a.priority] || 2;\n          bValue = priorityOrder[b.priority] || 2;\n          break;\n        case 'status':\n          aValue = a.status;\n          bValue = b.status;\n          break;\n        case 'cost':\n          aValue = a.cost || 0;\n          bValue = b.cost || 0;\n          break;\n        default:\n          aValue = a.created;\n          bValue = b.created;\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    return filtered;\n  };\n  const filteredJobs = getSortedAndFilteredJobs();\n  const stats = {\n    total: printJobs.length,\n    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,\n    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,\n    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"job-queue-container\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-store me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), \"Xerox Center Dashboard\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted\",\n            children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"stats-card text-center border-left-primary\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-file-alt fa-2x text-primary mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Total Jobs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-primary\",\n                children: stats.total\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"stats-card text-center border-left-warning\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-clock fa-2x text-warning mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Pending\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-warning\",\n                children: stats.pending\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"stats-card text-center border-left-info\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-cog fa-2x text-info mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"In Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-info\",\n                children: stats.inProgress\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"stats-card text-center border-left-success\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-dollar-sign fa-2x text-success mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Revenue\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-success\",\n                children: [\"$\", stats.revenue.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            className: \"align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-tasks me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 17\n                }, this), \"Job Queue (\", filteredJobs.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                className: \"g-2\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 3,\n                  children: /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    placeholder: \"Search jobs...\",\n                    value: searchTerm,\n                    onChange: e => setSearchTerm(e.target.value),\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 3,\n                  children: /*#__PURE__*/_jsxDEV(Form.Select, {\n                    size: \"sm\",\n                    value: filterStatus,\n                    onChange: e => setFilterStatus(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Requested\",\n                      children: \"Requested\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 426,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UnderReview\",\n                      children: \"Under Review\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Quoted\",\n                      children: \"Quoted\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"WaitingConfirmation\",\n                      children: \"Waiting Confirmation\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 429,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Confirmed\",\n                      children: \"Confirmed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"InProgress\",\n                      children: \"In Progress\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 431,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Completed\",\n                      children: \"Completed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 432,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 3,\n                  children: /*#__PURE__*/_jsxDEV(Form.Select, {\n                    size: \"sm\",\n                    value: sortBy,\n                    onChange: e => setSortBy(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"created\",\n                      children: \"Sort by Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"priority\",\n                      children: \"Sort by Priority\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"status\",\n                      children: \"Sort by Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"cost\",\n                      children: \"Sort by Cost\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 3,\n                  children: /*#__PURE__*/_jsxDEV(ButtonGroup, {\n                    size: \"sm\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: sortOrder === 'desc' ? 'primary' : 'outline-primary',\n                      onClick: () => setSortOrder('desc'),\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-sort-amount-down\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 453,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: sortOrder === 'asc' ? 'primary' : 'outline-primary',\n                      onClick: () => setSortOrder('asc'),\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-sort-amount-up\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 459,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: viewMode === 'table' ? 'primary' : 'outline-primary',\n                      onClick: () => setViewMode('table'),\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-table\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 465,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: viewMode === 'cards' ? 'primary' : 'outline-primary',\n                      onClick: () => setViewMode('cards'),\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-th-large\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 471,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: filteredJobs.length > 0 ? viewMode === 'table' ? /*#__PURE__*/_jsxDEV(Table, {\n            responsive: true,\n            hover: true,\n            className: \"job-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Job #\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Priority\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"File Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Print Specs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Cost\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: filteredJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: job.jobNumber\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: getTimeAgo(job.created)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: getPriorityBadge(job.priority)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: job.studentName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: job.studentEmail\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-file-pdf me-2 text-danger\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: job.fileSize ? formatFileSize(job.fileSize) : 'Unknown size'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 521,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"small\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Type:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 528,\n                        columnNumber: 32\n                      }, this), \" \", job.printType]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Copies:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 529,\n                        columnNumber: 32\n                      }, this), \" \", job.copies]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Color:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 530,\n                        columnNumber: 32\n                      }, this), \" \", job.colorType]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Size:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 531,\n                        columnNumber: 32\n                      }, this), \" \", job.paperSize]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: getStatusBadge(job.status)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"small\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Created:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 540,\n                        columnNumber: 32\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 540,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: new Date(job.created).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 541,\n                      columnNumber: 27\n                    }, this), job.estimatedCompletionTime && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"ETA:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 544,\n                          columnNumber: 36\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: new Date(job.estimatedCompletionTime).toLocaleDateString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 545,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"btn-group-vertical\",\n                    role: \"group\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-info\",\n                      size: \"sm\",\n                      onClick: () => handleDownloadFile(job.id, job.fileName),\n                      title: \"Download File\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-download me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 559,\n                        columnNumber: 27\n                      }, this), \"Download\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 25\n                    }, this), job.status === 'Requested' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-primary\",\n                        size: \"sm\",\n                        onClick: () => {\n                          setSelectedJob(job);\n                          setShowQuoteModal(true);\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-dollar-sign me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 573,\n                          columnNumber: 31\n                        }, this), \"Quote\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 565,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleStatusUpdate(job.id, 'Rejected'),\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-times me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 581,\n                          columnNumber: 31\n                        }, this), \"Reject\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 576,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true), job.status === 'Confirmed' && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-info\",\n                      size: \"sm\",\n                      onClick: () => handleStatusUpdate(job.id, 'InProgress'),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-play me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 593,\n                        columnNumber: 29\n                      }, this), \"Start\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 27\n                    }, this), job.status === 'InProgress' && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-success\",\n                      size: \"sm\",\n                      onClick: () => handleStatusUpdate(job.id, 'Completed'),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-check me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 604,\n                        columnNumber: 29\n                      }, this), \"Complete\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 27\n                    }, this), job.status === 'Completed' && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-success\",\n                      size: \"sm\",\n                      onClick: () => handleStatusUpdate(job.id, 'Delivered'),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-truck me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 615,\n                        columnNumber: 29\n                      }, this), \"Deliver\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-secondary\",\n                      size: \"sm\",\n                      onClick: () => handleOpenChat(job),\n                      title: \"Chat\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-comment\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 626,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 620,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 21\n                }, this)]\n              }, job.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 15\n          }, this) :\n          /*#__PURE__*/\n          // Card View\n          _jsxDEV(Row, {\n            children: filteredJobs.map(job => {\n              var _job$priority;\n              return /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                lg: 4,\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: `job-card h-100 shadow-sm ${job.priority === 'Urgent' ? 'priority-urgent' : ''} priority-${((_job$priority = job.priority) === null || _job$priority === void 0 ? void 0 : _job$priority.toLowerCase()) || 'normal'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                    className: \"d-flex justify-content-between align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: job.jobNumber\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 642,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 643,\n                        columnNumber: 27\n                      }, this), getPriorityBadge(job.priority)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 25\n                    }, this), getStatusBadge(job.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: job.studentName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 650,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 651,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted\",\n                        children: job.studentEmail\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 652,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-file-pdf me-2 text-danger\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 655,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        children: job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 656,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 654,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-2 small\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Type:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 659,\n                          columnNumber: 32\n                        }, this), \" \", job.printType]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 659,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Copies:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 660,\n                          columnNumber: 32\n                        }, this), \" \", job.copies]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 660,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Color:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 661,\n                          columnNumber: 32\n                        }, this), \" \", job.colorType]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 661,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Size:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 662,\n                          columnNumber: 32\n                        }, this), \" \", job.paperSize]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 662,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 658,\n                      columnNumber: 25\n                    }, this), job.cost && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-success\",\n                        children: [\"$\", job.cost.toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 666,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 665,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small text-muted\",\n                      children: getTimeAgo(job.created)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Card.Footer, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"action-buttons d-flex flex-wrap gap-1\",\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-info\",\n                        size: \"sm\",\n                        onClick: () => handleDownloadFile(job.id, job.fileName),\n                        title: \"Download File\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-download\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 681,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 675,\n                        columnNumber: 27\n                      }, this), job.status === 'Requested' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-primary\",\n                          size: \"sm\",\n                          onClick: () => {\n                            setSelectedJob(job);\n                            setShowQuoteModal(true);\n                          },\n                          title: \"Quote\",\n                          children: /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-dollar-sign\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 695,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 686,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-danger\",\n                          size: \"sm\",\n                          onClick: () => handleStatusUpdate(job.id, 'Rejected'),\n                          title: \"Reject\",\n                          children: /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-times\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 703,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 697,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true), job.status === 'Confirmed' && /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-info\",\n                        size: \"sm\",\n                        onClick: () => handleStatusUpdate(job.id, 'InProgress'),\n                        title: \"Start\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-play\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 715,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 709,\n                        columnNumber: 29\n                      }, this), job.status === 'InProgress' && /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-success\",\n                        size: \"sm\",\n                        onClick: () => handleStatusUpdate(job.id, 'Completed'),\n                        title: \"Complete\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-check\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 726,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 720,\n                        columnNumber: 29\n                      }, this), job.status === 'Completed' && /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-success\",\n                        size: \"sm\",\n                        onClick: () => handleStatusUpdate(job.id, 'Delivered'),\n                        title: \"Deliver\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-truck\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 737,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 731,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-secondary\",\n                        size: \"sm\",\n                        onClick: () => handleOpenChat(job),\n                        title: \"Chat\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-comment\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 747,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 741,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-dark\",\n                        size: \"sm\",\n                        onClick: () => {\n                          setSelectedJob(job);\n                          setShowJobDetailsModal(true);\n                        },\n                        title: \"View Details\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-eye\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 759,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 750,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 674,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 673,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 21\n                }, this)\n              }, job.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"info\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-info-circle me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 15\n            }, this), \"No jobs found for the selected filter.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showQuoteModal,\n        onHide: () => setShowQuoteModal(false),\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-dollar-sign me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 13\n            }, this), \"Provide Quote - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: selectedJob && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3 p-3 bg-light rounded\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Job Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"File:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 792,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.fileName, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 792,\n                    columnNumber: 66\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Type:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 793,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.printType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 793,\n                    columnNumber: 67\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Copies:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.copies]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Color:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.colorType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 68\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Size:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.paperSize, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 67\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Student:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 21\n                  }, this), \" \", selectedJob.studentName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this), selectedJob.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Remarks:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.remarks]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Cost ($)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 813,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n                      children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                        children: \"$\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 815,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"number\",\n                        step: \"0.01\",\n                        placeholder: \"0.00\",\n                        value: quoteData.cost,\n                        onChange: e => setQuoteData(prev => ({\n                          ...prev,\n                          cost: e.target.value\n                        })),\n                        required: true\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 816,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 814,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 812,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Estimated Hours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 829,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"number\",\n                      min: \"1\",\n                      placeholder: \"Hours to complete\",\n                      value: quoteData.estimatedHours,\n                      onChange: e => setQuoteData(prev => ({\n                        ...prev,\n                        estimatedHours: e.target.value\n                      })),\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 830,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Notes (Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  as: \"textarea\",\n                  rows: 3,\n                  placeholder: \"Any additional notes for the student...\",\n                  value: quoteData.notes,\n                  onChange: e => setQuoteData(prev => ({\n                    ...prev,\n                    notes: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowQuoteModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleQuoteSubmit,\n            disabled: !quoteData.cost || !quoteData.estimatedHours,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-paper-plane me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 13\n            }, this), \"Send Quote\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showChatModal,\n        onHide: () => setShowChatModal(false),\n        size: \"lg\",\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-comment me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 875,\n              columnNumber: 13\n            }, this), \"Chat - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 874,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 873,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: '400px',\n              overflowY: 'auto',\n              border: '1px solid #dee2e6',\n              borderRadius: '0.375rem',\n              padding: '1rem',\n              marginBottom: '1rem'\n            },\n            children: messages.length > 0 ? messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `mb-3 ${message.isFromCurrentUser ? 'text-end' : 'text-start'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `d-inline-block p-2 rounded ${message.isFromCurrentUser ? 'bg-primary text-white' : 'bg-light'}`,\n                style: {\n                  maxWidth: '70%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: `d-block mt-1 ${message.isFromCurrentUser ? 'text-light' : 'text-muted'}`,\n                  children: [message.senderName, \" - \", new Date(message.sentAt).toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 19\n              }, this)\n            }, message.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 17\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-muted\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-comments fa-3x mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No messages yet. Start a conversation!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              placeholder: \"Type your message...\",\n              value: newMessage,\n              onChange: e => setNewMessage(e.target.value),\n              onKeyDown: e => e.key === 'Enter' && handleSendMessage(),\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              onClick: handleSendMessage,\n              disabled: !newMessage.trim(),\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-paper-plane\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 879,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowChatModal(false),\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 914,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 913,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 872,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showJobDetailsModal,\n        onHide: () => setShowJobDetailsModal(false),\n        size: \"lg\",\n        className: \"job-details-modal\",\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-info-circle me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 924,\n              columnNumber: 13\n            }, this), \"Job Details - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 923,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 922,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: selectedJob && /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-user me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 935,\n                      columnNumber: 23\n                    }, this), \"Student Information\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 934,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Name:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 940,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.studentName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Email:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 941,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.studentEmail]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-file me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 948,\n                      columnNumber: 23\n                    }, this), \"File Information\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 947,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"File Name:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 953,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.fileName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 953,\n                    columnNumber: 21\n                  }, this), selectedJob.fileSize && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"File Size:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 955,\n                      columnNumber: 28\n                    }, this), \" \", formatFileSize(selectedJob.fileSize)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      onClick: () => handleDownloadFile(selectedJob.id, selectedJob.fileName),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-download me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 963,\n                        columnNumber: 25\n                      }, this), \"Download File\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 958,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 957,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 952,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-print me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 975,\n                      columnNumber: 23\n                    }, this), \"Print Specifications\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 974,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 973,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Print Type:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 980,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.printType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 980,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Copies:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 981,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.copies]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 981,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Color Type:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 982,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.colorType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 982,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Paper Size:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 983,\n                      columnNumber: 26\n                    }, this), \" \", selectedJob.paperSize]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 983,\n                    columnNumber: 21\n                  }, this), selectedJob.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Remarks:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 986,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 bg-light rounded mt-1\",\n                      children: selectedJob.remarks\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 987,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 985,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 979,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-info me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 998,\n                      columnNumber: 23\n                    }, this), \"Job Status\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 997,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 996,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Status:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1004,\n                      columnNumber: 23\n                    }, this), \" \", getStatusBadge(selectedJob.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1003,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Priority:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1007,\n                      columnNumber: 23\n                    }, this), \" \", getPriorityBadge(selectedJob.priority)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1006,\n                    columnNumber: 21\n                  }, this), selectedJob.cost && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Cost:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1011,\n                      columnNumber: 25\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-success\",\n                      children: [\"$\", selectedJob.cost.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1011,\n                      columnNumber: 48\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1010,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Created:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1015,\n                      columnNumber: 23\n                    }, this), \" \", new Date(selectedJob.created).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1014,\n                    columnNumber: 21\n                  }, this), selectedJob.estimatedCompletionTime && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Estimated Completion:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1019,\n                      columnNumber: 25\n                    }, this), \" \", new Date(selectedJob.estimatedCompletionTime).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1018,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1002,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 971,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 928,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowJobDetailsModal(false),\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1029,\n            columnNumber: 11\n          }, this), selectedJob && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: () => {\n              setShowJobDetailsModal(false);\n              handleOpenChat(selectedJob);\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-comment me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 15\n            }, this), \"Start Chat\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1033,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1028,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 921,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 346,\n    columnNumber: 5\n  }, this);\n};\n_s(XeroxCenterDashboard, \"yOBp+pTRgzl3M3JWyhOKa/OV4y4=\", false, function () {\n  return [useAuth];\n});\n_c = XeroxCenterDashboard;\nexport default XeroxCenterDashboard;\nvar _c;\n$RefreshReg$(_c, \"XeroxCenterDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Clock", "CheckCircle", "DollarSign", "Info", "Check", "Activity", "Eye", "X", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ArrowUp", "ArrowDown", "Minus", "useAuth", "printJobApi", "fileUploadApi", "messageApi", "cn", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "XeroxCenterDashboard", "_s", "user", "printJobs", "setPrintJobs", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "showQuoteModal", "setShowQuoteModal", "quoteData", "setQuoteData", "cost", "estimatedHours", "notes", "filterStatus", "setFilterStatus", "showChatModal", "setShowChatModal", "messages", "setMessages", "newMessage", "setNewMessage", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "searchTerm", "setSearchTerm", "viewMode", "setViewMode", "showJobDetailsModal", "setShowJobDetailsModal", "refreshInterval", "setRefreshInterval", "fetchData", "printJobsResponse", "getXeroxCenterJobs", "data", "error", "console", "interval", "setInterval", "clearInterval", "getStatusBadge", "status", "statusConfig", "color", "icon", "config", "IconComponent", "div", "initial", "scale", "opacity", "animate", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getPriorityBadge", "priority", "priorityConfig", "getTimeAgo", "dateString", "now", "Date", "date", "diffInMinutes", "Math", "floor", "getTime", "formatFileSize", "bytes", "k", "sizes", "i", "log", "parseFloat", "pow", "toFixed", "handleQuoteSubmit", "estimatedCompletion", "setHours", "getHours", "parseInt", "setJobQuote", "id", "toISOString", "handleStatusUpdate", "jobId", "newStatus", "updateJobStatus", "handleDownloadFile", "response", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleOpenChat", "job", "getJobMessages", "handleSendMessage", "trim", "sendMessage", "prev", "getSortedAndFilteredJobs", "filtered", "filter", "term", "toLowerCase", "jobNumber", "includes", "studentName", "studentEmail", "printType", "sort", "a", "b", "aValue", "bValue", "created", "priorityOrder", "filteredJobs", "stats", "total", "length", "pending", "inProgress", "completed", "revenue", "reduce", "sum", "Container", "fluid", "Row", "Col", "username", "md", "Card", "Body", "Header", "Form", "Control", "type", "placeholder", "value", "onChange", "e", "target", "size", "Select", "ButtonGroup", "<PERSON><PERSON>", "variant", "onClick", "Table", "responsive", "hover", "map", "slice", "fileSize", "copies", "colorType", "paperSize", "toLocaleDateString", "estimatedCompletionTime", "role", "title", "_job$priority", "lg", "Footer", "<PERSON><PERSON>", "Modal", "show", "onHide", "closeButton", "Title", "remarks", "Group", "Label", "InputGroup", "Text", "step", "required", "min", "as", "rows", "disabled", "style", "height", "overflowY", "border", "borderRadius", "padding", "marginBottom", "message", "isFromCurrentUser", "max<PERSON><PERSON><PERSON>", "content", "sender<PERSON>ame", "sentAt", "toLocaleString", "onKeyDown", "key", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/XeroxCenterDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  BarChart3,\n  FileText,\n  Clock,\n  CheckCircle,\n  DollarSign,\n  Download,\n  MessageCircle,\n  Info,\n  Check,\n  Star,\n  MapPin,\n  TrendingUp,\n  Activity,\n  Users,\n  Printer,\n  Eye,\n  Send,\n  X,\n  Plus,\n  Filter,\n  Search,\n  Calendar,\n  Settings,\n  AlertTriangle,\n  ArrowUp,\n  ArrowDown,\n  Minus,\n  ExternalLink,\n  RefreshCw,\n  Grid,\n  List,\n  SortAsc,\n  SortDesc\n} from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../services/api';\nimport { cn } from '../lib/utils';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  studentName: string;\n  studentEmail: string;\n  printType: string;\n  copies: number;\n  colorType: string;\n  paperSize: string;\n  remarks?: string;\n  created: string;\n  priority?: 'Low' | 'Normal' | 'High' | 'Urgent';\n  fileSize?: number;\n  estimatedDuration?: number;\n}\n\nconst XeroxCenterDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [selectedJob, setSelectedJob] = useState<PrintJob | null>(null);\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [quoteData, setQuoteData] = useState({\n    cost: '',\n    estimatedHours: '',\n    notes: ''\n  });\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [messages, setMessages] = useState<any[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [sortBy, setSortBy] = useState<'created' | 'priority' | 'status' | 'cost'>('created');\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');\n  const [showJobDetailsModal, setShowJobDetailsModal] = useState(false);\n  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs for this xerox center\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error fetching print jobs:', error);\n        setPrintJobs([]);\n      }\n    };\n\n    fetchData();\n\n    // Set up auto-refresh every 30 seconds\n    const interval = setInterval(fetchData, 30000);\n    setRefreshInterval(interval);\n\n    // Cleanup interval on unmount\n    return () => {\n      if (refreshInterval) {\n        clearInterval(refreshInterval);\n      }\n      clearInterval(interval);\n    };\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: Clock },\n      'UnderReview': { color: 'bg-blue-100 text-blue-800 border-blue-200', icon: Eye },\n      'Quoted': { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: DollarSign },\n      'WaitingConfirmation': { color: 'bg-orange-100 text-orange-800 border-orange-200', icon: Clock },\n      'Confirmed': { color: 'bg-indigo-100 text-indigo-800 border-indigo-200', icon: Check },\n      'InProgress': { color: 'bg-purple-100 text-purple-800 border-purple-200', icon: Activity },\n      'Completed': { color: 'bg-green-100 text-green-800 border-green-200', icon: CheckCircle },\n      'Delivered': { color: 'bg-emerald-100 text-emerald-800 border-emerald-200', icon: CheckCircle },\n      'Rejected': { color: 'bg-red-100 text-red-800 border-red-200', icon: X },\n      'Cancelled': { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: X }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || {\n      color: 'bg-gray-100 text-gray-800 border-gray-200',\n      icon: Info\n    };\n\n    const IconComponent = config.icon;\n\n    return (\n      <motion.div\n        initial={{ scale: 0.8, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        className={cn(\n          \"inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border\",\n          config.color\n        )}\n      >\n        <IconComponent className=\"w-3 h-3\" />\n        {status}\n      </motion.div>\n    );\n  };\n\n  const getPriorityBadge = (priority: string = 'Normal') => {\n    const priorityConfig = {\n      'Low': { color: 'bg-slate-100 text-slate-600 border-slate-200', icon: ArrowDown },\n      'Normal': { color: 'bg-blue-100 text-blue-600 border-blue-200', icon: Minus },\n      'High': { color: 'bg-orange-100 text-orange-600 border-orange-200', icon: ArrowUp },\n      'Urgent': { color: 'bg-red-100 text-red-600 border-red-200', icon: AlertTriangle }\n    };\n\n    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig['Normal'];\n    const IconComponent = config.icon;\n\n    return (\n      <motion.div\n        initial={{ scale: 0.8, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        className={cn(\n          \"inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border\",\n          config.color\n        )}\n      >\n        <IconComponent className=\"w-3 h-3\" />\n        {priority}\n      </motion.div>\n    );\n  };\n\n  const getTimeAgo = (dateString: string) => {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n    return `${Math.floor(diffInMinutes / 1440)}d ago`;\n  };\n\n  const formatFileSize = (bytes: number = 0) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const handleQuoteSubmit = async () => {\n    if (selectedJob) {\n      try {\n        const estimatedCompletion = new Date();\n        estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));\n\n        await printJobApi.setJobQuote(\n          selectedJob.id,\n          parseFloat(quoteData.cost),\n          estimatedCompletion.toISOString(),\n          quoteData.notes\n        );\n\n        // Refresh print jobs after update\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error submitting quote:', error);\n      }\n    }\n\n    setShowQuoteModal(false);\n    setSelectedJob(null);\n    setQuoteData({ cost: '', estimatedHours: '', notes: '' });\n  };\n\n  const handleStatusUpdate = async (jobId: number, newStatus: string) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n\n      // Refresh print jobs after update\n      const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error updating status:', error);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  const handleOpenChat = async (job: PrintJob) => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      // Load messages for this job\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!newMessage.trim() || !selectedJob) return;\n\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, newMessage.trim());\n\n      // Add the new message to the list\n      setMessages(prev => [...prev, response.data]);\n      setNewMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  const getSortedAndFilteredJobs = () => {\n    let filtered = printJobs;\n\n    // Apply status filter\n    if (filterStatus !== 'all') {\n      filtered = filtered.filter(job => job.status === filterStatus);\n    }\n\n    // Apply search filter\n    if (searchTerm) {\n      const term = searchTerm.toLowerCase();\n      filtered = filtered.filter(job =>\n        job.jobNumber.toLowerCase().includes(term) ||\n        job.fileName.toLowerCase().includes(term) ||\n        job.studentName.toLowerCase().includes(term) ||\n        job.studentEmail.toLowerCase().includes(term) ||\n        job.printType.toLowerCase().includes(term)\n      );\n    }\n\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let aValue: any, bValue: any;\n\n      switch (sortBy) {\n        case 'created':\n          aValue = new Date(a.created).getTime();\n          bValue = new Date(b.created).getTime();\n          break;\n        case 'priority':\n          const priorityOrder = { 'Urgent': 4, 'High': 3, 'Normal': 2, 'Low': 1 };\n          aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 2;\n          bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 2;\n          break;\n        case 'status':\n          aValue = a.status;\n          bValue = b.status;\n          break;\n        case 'cost':\n          aValue = a.cost || 0;\n          bValue = b.cost || 0;\n          break;\n        default:\n          aValue = a.created;\n          bValue = b.created;\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    return filtered;\n  };\n\n  const filteredJobs = getSortedAndFilteredJobs();\n\n  const stats = {\n    total: printJobs.length,\n    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,\n    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,\n    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)\n  };\n\n  return (\n    <div className=\"job-queue-container\">\n      <Container fluid>\n        <Row className=\"mb-4\">\n          <Col>\n            <h2>\n              <i className=\"fas fa-store me-2\"></i>\n              Xerox Center Dashboard\n            </h2>\n            <p className=\"text-muted\">Welcome back, {user?.username}!</p>\n          </Col>\n        </Row>\n\n        {/* Statistics Cards */}\n        <Row className=\"mb-4\">\n          <Col md={3}>\n            <Card className=\"stats-card text-center border-left-primary\">\n              <Card.Body>\n                <i className=\"fas fa-file-alt fa-2x text-primary mb-2\"></i>\n                <h5>Total Jobs</h5>\n                <h3 className=\"text-primary\">{stats.total}</h3>\n              </Card.Body>\n            </Card>\n          </Col>\n          <Col md={3}>\n            <Card className=\"stats-card text-center border-left-warning\">\n              <Card.Body>\n                <i className=\"fas fa-clock fa-2x text-warning mb-2\"></i>\n                <h5>Pending</h5>\n                <h3 className=\"text-warning\">{stats.pending}</h3>\n              </Card.Body>\n            </Card>\n          </Col>\n          <Col md={3}>\n            <Card className=\"stats-card text-center border-left-info\">\n              <Card.Body>\n                <i className=\"fas fa-cog fa-2x text-info mb-2\"></i>\n                <h5>In Progress</h5>\n                <h3 className=\"text-info\">{stats.inProgress}</h3>\n              </Card.Body>\n            </Card>\n          </Col>\n          <Col md={3}>\n            <Card className=\"stats-card text-center border-left-success\">\n              <Card.Body>\n                <i className=\"fas fa-dollar-sign fa-2x text-success mb-2\"></i>\n                <h5>Revenue</h5>\n                <h3 className=\"text-success\">${stats.revenue.toFixed(2)}</h3>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n\n      {/* Job Queue */}\n      <Card>\n        <Card.Header>\n          <Row className=\"align-items-center\">\n            <Col md={4}>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-tasks me-2\"></i>\n                Job Queue ({filteredJobs.length})\n              </h5>\n            </Col>\n            <Col md={8}>\n              <Row className=\"g-2\">\n                <Col md={3}>\n                  <Form.Control\n                    type=\"text\"\n                    placeholder=\"Search jobs...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    size=\"sm\"\n                  />\n                </Col>\n                <Col md={3}>\n                  <Form.Select\n                    size=\"sm\"\n                    value={filterStatus}\n                    onChange={(e) => setFilterStatus(e.target.value)}\n                  >\n                    <option value=\"all\">All Status</option>\n                    <option value=\"Requested\">Requested</option>\n                    <option value=\"UnderReview\">Under Review</option>\n                    <option value=\"Quoted\">Quoted</option>\n                    <option value=\"WaitingConfirmation\">Waiting Confirmation</option>\n                    <option value=\"Confirmed\">Confirmed</option>\n                    <option value=\"InProgress\">In Progress</option>\n                    <option value=\"Completed\">Completed</option>\n                  </Form.Select>\n                </Col>\n                <Col md={3}>\n                  <Form.Select\n                    size=\"sm\"\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value as any)}\n                  >\n                    <option value=\"created\">Sort by Date</option>\n                    <option value=\"priority\">Sort by Priority</option>\n                    <option value=\"status\">Sort by Status</option>\n                    <option value=\"cost\">Sort by Cost</option>\n                  </Form.Select>\n                </Col>\n                <Col md={3}>\n                  <ButtonGroup size=\"sm\">\n                    <Button\n                      variant={sortOrder === 'desc' ? 'primary' : 'outline-primary'}\n                      onClick={() => setSortOrder('desc')}\n                    >\n                      <i className=\"fas fa-sort-amount-down\"></i>\n                    </Button>\n                    <Button\n                      variant={sortOrder === 'asc' ? 'primary' : 'outline-primary'}\n                      onClick={() => setSortOrder('asc')}\n                    >\n                      <i className=\"fas fa-sort-amount-up\"></i>\n                    </Button>\n                    <Button\n                      variant={viewMode === 'table' ? 'primary' : 'outline-primary'}\n                      onClick={() => setViewMode('table')}\n                    >\n                      <i className=\"fas fa-table\"></i>\n                    </Button>\n                    <Button\n                      variant={viewMode === 'cards' ? 'primary' : 'outline-primary'}\n                      onClick={() => setViewMode('cards')}\n                    >\n                      <i className=\"fas fa-th-large\"></i>\n                    </Button>\n                  </ButtonGroup>\n                </Col>\n              </Row>\n            </Col>\n          </Row>\n        </Card.Header>\n        <Card.Body>\n          {filteredJobs.length > 0 ? (\n            viewMode === 'table' ? (\n              <Table responsive hover className=\"job-table\">\n                <thead>\n                  <tr>\n                    <th>Job #</th>\n                    <th>Priority</th>\n                    <th>Student</th>\n                    <th>File Details</th>\n                    <th>Print Specs</th>\n                    <th>Status</th>\n                    <th>Cost</th>\n                    <th>Time</th>\n                    <th>Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {filteredJobs.map(job => (\n                    <tr key={job.id}>\n                      <td>\n                        <strong>{job.jobNumber}</strong>\n                        <br />\n                        <small className=\"text-muted\">\n                          {getTimeAgo(job.created)}\n                        </small>\n                      </td>\n                      <td>\n                        {getPriorityBadge(job.priority)}\n                      </td>\n                      <td>\n                        <div>\n                          <strong>{job.studentName}</strong>\n                          <br />\n                          <small className=\"text-muted\">{job.studentEmail}</small>\n                        </div>\n                      </td>\n                      <td>\n                        <div>\n                          <i className=\"fas fa-file-pdf me-2 text-danger\"></i>\n                          <strong>{job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName}</strong>\n                          <br />\n                          <small className=\"text-muted\">\n                            {job.fileSize ? formatFileSize(job.fileSize) : 'Unknown size'}\n                          </small>\n                        </div>\n                      </td>\n                      <td>\n                        <div className=\"small\">\n                          <div><strong>Type:</strong> {job.printType}</div>\n                          <div><strong>Copies:</strong> {job.copies}</div>\n                          <div><strong>Color:</strong> {job.colorType}</div>\n                          <div><strong>Size:</strong> {job.paperSize}</div>\n                        </div>\n                      </td>\n                      <td>{getStatusBadge(job.status)}</td>\n                      <td>\n                        {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                      </td>\n                      <td>\n                        <div className=\"small\">\n                          <div><strong>Created:</strong></div>\n                          <div>{new Date(job.created).toLocaleDateString()}</div>\n                          {job.estimatedCompletionTime && (\n                            <>\n                              <div><strong>ETA:</strong></div>\n                              <div>{new Date(job.estimatedCompletionTime).toLocaleDateString()}</div>\n                            </>\n                          )}\n                        </div>\n                      </td>\n                    <td>\n                      <div className=\"btn-group-vertical\" role=\"group\">\n                        {/* Download button - always available */}\n                        <Button\n                          variant=\"outline-info\"\n                          size=\"sm\"\n                          onClick={() => handleDownloadFile(job.id, job.fileName)}\n                          title=\"Download File\"\n                        >\n                          <i className=\"fas fa-download me-1\"></i>\n                          Download\n                        </Button>\n\n                        {job.status === 'Requested' && (\n                          <>\n                            <Button\n                              variant=\"outline-primary\"\n                              size=\"sm\"\n                              onClick={() => {\n                                setSelectedJob(job);\n                                setShowQuoteModal(true);\n                              }}\n                            >\n                              <i className=\"fas fa-dollar-sign me-1\"></i>\n                              Quote\n                            </Button>\n                            <Button\n                              variant=\"outline-danger\"\n                              size=\"sm\"\n                              onClick={() => handleStatusUpdate(job.id, 'Rejected')}\n                            >\n                              <i className=\"fas fa-times me-1\"></i>\n                              Reject\n                            </Button>\n                          </>\n                        )}\n\n                        {job.status === 'Confirmed' && (\n                          <Button\n                            variant=\"outline-info\"\n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'InProgress')}\n                          >\n                            <i className=\"fas fa-play me-1\"></i>\n                            Start\n                          </Button>\n                        )}\n\n                        {job.status === 'InProgress' && (\n                          <Button\n                            variant=\"outline-success\"\n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'Completed')}\n                          >\n                            <i className=\"fas fa-check me-1\"></i>\n                            Complete\n                          </Button>\n                        )}\n\n                        {job.status === 'Completed' && (\n                          <Button\n                            variant=\"outline-success\"\n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'Delivered')}\n                          >\n                            <i className=\"fas fa-truck me-1\"></i>\n                            Deliver\n                          </Button>\n                        )}\n\n                        <Button\n                          variant=\"outline-secondary\"\n                          size=\"sm\"\n                          onClick={() => handleOpenChat(job)}\n                          title=\"Chat\"\n                        >\n                          <i className=\"fas fa-comment\"></i>\n                        </Button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n              </Table>\n            ) : (\n              // Card View\n              <Row>\n                {filteredJobs.map(job => (\n                  <Col key={job.id} md={6} lg={4} className=\"mb-3\">\n                    <Card className={`job-card h-100 shadow-sm ${job.priority === 'Urgent' ? 'priority-urgent' : ''} priority-${job.priority?.toLowerCase() || 'normal'}`}>\n                      <Card.Header className=\"d-flex justify-content-between align-items-center\">\n                        <div>\n                          <strong>{job.jobNumber}</strong>\n                          <br />\n                          {getPriorityBadge(job.priority)}\n                        </div>\n                        {getStatusBadge(job.status)}\n                      </Card.Header>\n                      <Card.Body>\n                        <div className=\"mb-2\">\n                          <strong>{job.studentName}</strong>\n                          <br />\n                          <small className=\"text-muted\">{job.studentEmail}</small>\n                        </div>\n                        <div className=\"mb-2\">\n                          <i className=\"fas fa-file-pdf me-2 text-danger\"></i>\n                          <small>{job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName}</small>\n                        </div>\n                        <div className=\"mb-2 small\">\n                          <div><strong>Type:</strong> {job.printType}</div>\n                          <div><strong>Copies:</strong> {job.copies}</div>\n                          <div><strong>Color:</strong> {job.colorType}</div>\n                          <div><strong>Size:</strong> {job.paperSize}</div>\n                        </div>\n                        {job.cost && (\n                          <div className=\"mb-2\">\n                            <strong className=\"text-success\">${job.cost.toFixed(2)}</strong>\n                          </div>\n                        )}\n                        <div className=\"small text-muted\">\n                          {getTimeAgo(job.created)}\n                        </div>\n                      </Card.Body>\n                      <Card.Footer>\n                        <div className=\"action-buttons d-flex flex-wrap gap-1\">\n                          <Button\n                            variant=\"outline-info\"\n                            size=\"sm\"\n                            onClick={() => handleDownloadFile(job.id, job.fileName)}\n                            title=\"Download File\"\n                          >\n                            <i className=\"fas fa-download\"></i>\n                          </Button>\n\n                          {job.status === 'Requested' && (\n                            <>\n                              <Button\n                                variant=\"outline-primary\"\n                                size=\"sm\"\n                                onClick={() => {\n                                  setSelectedJob(job);\n                                  setShowQuoteModal(true);\n                                }}\n                                title=\"Quote\"\n                              >\n                                <i className=\"fas fa-dollar-sign\"></i>\n                              </Button>\n                              <Button\n                                variant=\"outline-danger\"\n                                size=\"sm\"\n                                onClick={() => handleStatusUpdate(job.id, 'Rejected')}\n                                title=\"Reject\"\n                              >\n                                <i className=\"fas fa-times\"></i>\n                              </Button>\n                            </>\n                          )}\n\n                          {job.status === 'Confirmed' && (\n                            <Button\n                              variant=\"outline-info\"\n                              size=\"sm\"\n                              onClick={() => handleStatusUpdate(job.id, 'InProgress')}\n                              title=\"Start\"\n                            >\n                              <i className=\"fas fa-play\"></i>\n                            </Button>\n                          )}\n\n                          {job.status === 'InProgress' && (\n                            <Button\n                              variant=\"outline-success\"\n                              size=\"sm\"\n                              onClick={() => handleStatusUpdate(job.id, 'Completed')}\n                              title=\"Complete\"\n                            >\n                              <i className=\"fas fa-check\"></i>\n                            </Button>\n                          )}\n\n                          {job.status === 'Completed' && (\n                            <Button\n                              variant=\"outline-success\"\n                              size=\"sm\"\n                              onClick={() => handleStatusUpdate(job.id, 'Delivered')}\n                              title=\"Deliver\"\n                            >\n                              <i className=\"fas fa-truck\"></i>\n                            </Button>\n                          )}\n\n                          <Button\n                            variant=\"outline-secondary\"\n                            size=\"sm\"\n                            onClick={() => handleOpenChat(job)}\n                            title=\"Chat\"\n                          >\n                            <i className=\"fas fa-comment\"></i>\n                          </Button>\n\n                          <Button\n                            variant=\"outline-dark\"\n                            size=\"sm\"\n                            onClick={() => {\n                              setSelectedJob(job);\n                              setShowJobDetailsModal(true);\n                            }}\n                            title=\"View Details\"\n                          >\n                            <i className=\"fas fa-eye\"></i>\n                          </Button>\n                        </div>\n                      </Card.Footer>\n                    </Card>\n                  </Col>\n                ))}\n              </Row>\n            )\n          ) : (\n            <Alert variant=\"info\">\n              <i className=\"fas fa-info-circle me-2\"></i>\n              No jobs found for the selected filter.\n            </Alert>\n          )}\n        </Card.Body>\n      </Card>\n\n      {/* Quote Modal */}\n      <Modal show={showQuoteModal} onHide={() => setShowQuoteModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-dollar-sign me-2\"></i>\n            Provide Quote - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedJob && (\n            <>\n              <div className=\"mb-3 p-3 bg-light rounded\">\n                <h6>Job Details:</h6>\n                <div className=\"row\">\n                  <div className=\"col-6\">\n                    <strong>File:</strong> {selectedJob.fileName}<br />\n                    <strong>Type:</strong> {selectedJob.printType}<br />\n                    <strong>Copies:</strong> {selectedJob.copies}\n                  </div>\n                  <div className=\"col-6\">\n                    <strong>Color:</strong> {selectedJob.colorType}<br />\n                    <strong>Size:</strong> {selectedJob.paperSize}<br />\n                    <strong>Student:</strong> {selectedJob.studentName}\n                  </div>\n                </div>\n                {selectedJob.remarks && (\n                  <div className=\"mt-2\">\n                    <strong>Remarks:</strong> {selectedJob.remarks}\n                  </div>\n                )}\n              </div>\n\n              <Form>\n                <Row>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Cost ($)</Form.Label>\n                      <InputGroup>\n                        <InputGroup.Text>$</InputGroup.Text>\n                        <Form.Control\n                          type=\"number\"\n                          step=\"0.01\"\n                          placeholder=\"0.00\"\n                          value={quoteData.cost}\n                          onChange={(e) => setQuoteData(prev => ({ ...prev, cost: e.target.value }))}\n                          required\n                        />\n                      </InputGroup>\n                    </Form.Group>\n                  </Col>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Estimated Hours</Form.Label>\n                      <Form.Control\n                        type=\"number\"\n                        min=\"1\"\n                        placeholder=\"Hours to complete\"\n                        value={quoteData.estimatedHours}\n                        onChange={(e) => setQuoteData(prev => ({ ...prev, estimatedHours: e.target.value }))}\n                        required\n                      />\n                    </Form.Group>\n                  </Col>\n                </Row>\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Notes (Optional)</Form.Label>\n                  <Form.Control\n                    as=\"textarea\"\n                    rows={3}\n                    placeholder=\"Any additional notes for the student...\"\n                    value={quoteData.notes}\n                    onChange={(e) => setQuoteData(prev => ({ ...prev, notes: e.target.value }))}\n                  />\n                </Form.Group>\n              </Form>\n            </>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowQuoteModal(false)}>\n            Cancel\n          </Button>\n          <Button \n            variant=\"primary\" \n            onClick={handleQuoteSubmit}\n            disabled={!quoteData.cost || !quoteData.estimatedHours}\n          >\n            <i className=\"fas fa-paper-plane me-2\"></i>\n            Send Quote\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* Chat Modal */}\n      <Modal show={showChatModal} onHide={() => setShowChatModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-comment me-2\"></i>\n            Chat - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <div style={{ height: '400px', overflowY: 'auto', border: '1px solid #dee2e6', borderRadius: '0.375rem', padding: '1rem', marginBottom: '1rem' }}>\n            {messages.length > 0 ? (\n              messages.map((message) => (\n                <div key={message.id} className={`mb-3 ${message.isFromCurrentUser ? 'text-end' : 'text-start'}`}>\n                  <div className={`d-inline-block p-2 rounded ${message.isFromCurrentUser ? 'bg-primary text-white' : 'bg-light'}`} style={{ maxWidth: '70%' }}>\n                    <div>{message.content}</div>\n                    <small className={`d-block mt-1 ${message.isFromCurrentUser ? 'text-light' : 'text-muted'}`}>\n                      {message.senderName} - {new Date(message.sentAt).toLocaleString()}\n                    </small>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"text-center text-muted\">\n                <i className=\"fas fa-comments fa-3x mb-3\"></i>\n                <p>No messages yet. Start a conversation!</p>\n              </div>\n            )}\n          </div>\n          <div className=\"d-flex\">\n            <Form.Control\n              type=\"text\"\n              placeholder=\"Type your message...\"\n              value={newMessage}\n              onChange={(e) => setNewMessage(e.target.value)}\n              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}\n              className=\"me-2\"\n            />\n            <Button variant=\"primary\" onClick={handleSendMessage} disabled={!newMessage.trim()}>\n              <i className=\"fas fa-paper-plane\"></i>\n            </Button>\n          </div>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowChatModal(false)}>\n            Close\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* Job Details Modal */}\n      <Modal show={showJobDetailsModal} onHide={() => setShowJobDetailsModal(false)} size=\"lg\" className=\"job-details-modal\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-info-circle me-2\"></i>\n            Job Details - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedJob && (\n            <Row>\n              <Col md={6}>\n                <Card className=\"mb-3\">\n                  <Card.Header>\n                    <h6 className=\"mb-0\">\n                      <i className=\"fas fa-user me-2\"></i>\n                      Student Information\n                    </h6>\n                  </Card.Header>\n                  <Card.Body>\n                    <div><strong>Name:</strong> {selectedJob.studentName}</div>\n                    <div><strong>Email:</strong> {selectedJob.studentEmail}</div>\n                  </Card.Body>\n                </Card>\n\n                <Card className=\"mb-3\">\n                  <Card.Header>\n                    <h6 className=\"mb-0\">\n                      <i className=\"fas fa-file me-2\"></i>\n                      File Information\n                    </h6>\n                  </Card.Header>\n                  <Card.Body>\n                    <div><strong>File Name:</strong> {selectedJob.fileName}</div>\n                    {selectedJob.fileSize && (\n                      <div><strong>File Size:</strong> {formatFileSize(selectedJob.fileSize)}</div>\n                    )}\n                    <div className=\"mt-2\">\n                      <Button\n                        variant=\"outline-primary\"\n                        size=\"sm\"\n                        onClick={() => handleDownloadFile(selectedJob.id, selectedJob.fileName)}\n                      >\n                        <i className=\"fas fa-download me-2\"></i>\n                        Download File\n                      </Button>\n                    </div>\n                  </Card.Body>\n                </Card>\n              </Col>\n\n              <Col md={6}>\n                <Card className=\"mb-3\">\n                  <Card.Header>\n                    <h6 className=\"mb-0\">\n                      <i className=\"fas fa-print me-2\"></i>\n                      Print Specifications\n                    </h6>\n                  </Card.Header>\n                  <Card.Body>\n                    <div><strong>Print Type:</strong> {selectedJob.printType}</div>\n                    <div><strong>Copies:</strong> {selectedJob.copies}</div>\n                    <div><strong>Color Type:</strong> {selectedJob.colorType}</div>\n                    <div><strong>Paper Size:</strong> {selectedJob.paperSize}</div>\n                    {selectedJob.remarks && (\n                      <div className=\"mt-2\">\n                        <strong>Remarks:</strong>\n                        <div className=\"p-2 bg-light rounded mt-1\">\n                          {selectedJob.remarks}\n                        </div>\n                      </div>\n                    )}\n                  </Card.Body>\n                </Card>\n\n                <Card className=\"mb-3\">\n                  <Card.Header>\n                    <h6 className=\"mb-0\">\n                      <i className=\"fas fa-info me-2\"></i>\n                      Job Status\n                    </h6>\n                  </Card.Header>\n                  <Card.Body>\n                    <div className=\"mb-2\">\n                      <strong>Status:</strong> {getStatusBadge(selectedJob.status)}\n                    </div>\n                    <div className=\"mb-2\">\n                      <strong>Priority:</strong> {getPriorityBadge(selectedJob.priority)}\n                    </div>\n                    {selectedJob.cost && (\n                      <div className=\"mb-2\">\n                        <strong>Cost:</strong> <span className=\"text-success\">${selectedJob.cost.toFixed(2)}</span>\n                      </div>\n                    )}\n                    <div className=\"mb-2\">\n                      <strong>Created:</strong> {new Date(selectedJob.created).toLocaleString()}\n                    </div>\n                    {selectedJob.estimatedCompletionTime && (\n                      <div>\n                        <strong>Estimated Completion:</strong> {new Date(selectedJob.estimatedCompletionTime).toLocaleString()}\n                      </div>\n                    )}\n                  </Card.Body>\n                </Card>\n              </Col>\n            </Row>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowJobDetailsModal(false)}>\n            Close\n          </Button>\n          {selectedJob && (\n            <Button\n              variant=\"primary\"\n              onClick={() => {\n                setShowJobDetailsModal(false);\n                handleOpenChat(selectedJob);\n              }}\n            >\n              <i className=\"fas fa-comment me-2\"></i>\n              Start Chat\n            </Button>\n          )}\n        </Modal.Footer>\n      </Modal>\n      </Container>\n    </div>\n  );\n};\n\nexport default XeroxCenterDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAyB,eAAe;AACvD,SAGEC,KAAK,EACLC,WAAW,EACXC,UAAU,EAGVC,IAAI,EACJC,KAAK,EAILC,QAAQ,EAGRC,GAAG,EAEHC,CAAC,EAMDC,aAAa,EACbC,OAAO,EACPC,SAAS,EACTC,KAAK,QAOA,cAAc;AACrB,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,SAASC,WAAW,EAAkBC,aAAa,EAAEC,UAAU,QAAQ,iBAAiB;AACxF,SAASC,EAAE,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAsBlC,MAAMC,oBAA8B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3C,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAkB,IAAI,CAAC;EACrE,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC;IACzCmC,IAAI,EAAE,EAAE;IACRC,cAAc,EAAE,EAAE;IAClBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAA6C,SAAS,CAAC;EAC3F,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAiB,MAAM,CAAC;EAClE,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAoB,OAAO,CAAC;EACpE,MAAM,CAACsD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACwD,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CAAwB,IAAI,CAAC;EAEnFC,SAAS,CAAC,MAAM;IACd,MAAMyD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,iBAAiB,GAAG,MAAM3C,WAAW,CAAC4C,kBAAkB,CAAC,CAAC;QAChEhC,YAAY,CAAC+B,iBAAiB,CAACE,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDlC,YAAY,CAAC,EAAE,CAAC;MAClB;IACF,CAAC;IAED8B,SAAS,CAAC,CAAC;;IAEX;IACA,MAAMM,QAAQ,GAAGC,WAAW,CAACP,SAAS,EAAE,KAAK,CAAC;IAC9CD,kBAAkB,CAACO,QAAQ,CAAC;;IAE5B;IACA,OAAO,MAAM;MACX,IAAIR,eAAe,EAAE;QACnBU,aAAa,CAACV,eAAe,CAAC;MAChC;MACAU,aAAa,CAACF,QAAQ,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,KAAK,EAAE,2CAA2C;QAAEC,IAAI,EAAEpE;MAAM,CAAC;MAChF,aAAa,EAAE;QAAEmE,KAAK,EAAE,2CAA2C;QAAEC,IAAI,EAAE9D;MAAI,CAAC;MAChF,QAAQ,EAAE;QAAE6D,KAAK,EAAE,iDAAiD;QAAEC,IAAI,EAAElE;MAAW,CAAC;MACxF,qBAAqB,EAAE;QAAEiE,KAAK,EAAE,iDAAiD;QAAEC,IAAI,EAAEpE;MAAM,CAAC;MAChG,WAAW,EAAE;QAAEmE,KAAK,EAAE,iDAAiD;QAAEC,IAAI,EAAEhE;MAAM,CAAC;MACtF,YAAY,EAAE;QAAE+D,KAAK,EAAE,iDAAiD;QAAEC,IAAI,EAAE/D;MAAS,CAAC;MAC1F,WAAW,EAAE;QAAE8D,KAAK,EAAE,8CAA8C;QAAEC,IAAI,EAAEnE;MAAY,CAAC;MACzF,WAAW,EAAE;QAAEkE,KAAK,EAAE,oDAAoD;QAAEC,IAAI,EAAEnE;MAAY,CAAC;MAC/F,UAAU,EAAE;QAAEkE,KAAK,EAAE,wCAAwC;QAAEC,IAAI,EAAE7D;MAAE,CAAC;MACxE,WAAW,EAAE;QAAE4D,KAAK,EAAE,2CAA2C;QAAEC,IAAI,EAAE7D;MAAE;IAC7E,CAAC;IAED,MAAM8D,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAClEE,KAAK,EAAE,2CAA2C;MAClDC,IAAI,EAAEjE;IACR,CAAC;IAED,MAAMmE,aAAa,GAAGD,MAAM,CAACD,IAAI;IAEjC,oBACElD,OAAA,CAACnB,MAAM,CAACwE,GAAG;MACTC,OAAO,EAAE;QAAEC,KAAK,EAAE,GAAG;QAAEC,OAAO,EAAE;MAAE,CAAE;MACpCC,OAAO,EAAE;QAAEF,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAClCE,SAAS,EAAE5D,EAAE,CACX,sFAAsF,EACtFqD,MAAM,CAACF,KACT,CAAE;MAAAU,QAAA,gBAEF3D,OAAA,CAACoD,aAAa;QAACM,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACpChB,MAAM;IAAA;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEjB,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACC,QAAgB,GAAG,QAAQ,KAAK;IACxD,MAAMC,cAAc,GAAG;MACrB,KAAK,EAAE;QAAEjB,KAAK,EAAE,8CAA8C;QAAEC,IAAI,EAAE1D;MAAU,CAAC;MACjF,QAAQ,EAAE;QAAEyD,KAAK,EAAE,2CAA2C;QAAEC,IAAI,EAAEzD;MAAM,CAAC;MAC7E,MAAM,EAAE;QAAEwD,KAAK,EAAE,iDAAiD;QAAEC,IAAI,EAAE3D;MAAQ,CAAC;MACnF,QAAQ,EAAE;QAAE0D,KAAK,EAAE,wCAAwC;QAAEC,IAAI,EAAE5D;MAAc;IACnF,CAAC;IAED,MAAM6D,MAAM,GAAGe,cAAc,CAACD,QAAQ,CAAgC,IAAIC,cAAc,CAAC,QAAQ,CAAC;IAClG,MAAMd,aAAa,GAAGD,MAAM,CAACD,IAAI;IAEjC,oBACElD,OAAA,CAACnB,MAAM,CAACwE,GAAG;MACTC,OAAO,EAAE;QAAEC,KAAK,EAAE,GAAG;QAAEC,OAAO,EAAE;MAAE,CAAE;MACpCC,OAAO,EAAE;QAAEF,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAClCE,SAAS,EAAE5D,EAAE,CACX,sFAAsF,EACtFqD,MAAM,CAACF,KACT,CAAE;MAAAU,QAAA,gBAEF3D,OAAA,CAACoD,aAAa;QAACM,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACpCE,QAAQ;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEjB,CAAC;EAED,MAAMI,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC,GAAGJ,IAAI,CAACI,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,UAAU;IACxC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,OAAO;IACtD,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC,OAAO;IACzE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC,OAAO;EACnD,CAAC;EAED,MAAMI,cAAc,GAAGA,CAACC,KAAa,GAAG,CAAC,KAAK;IAC5C,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGP,IAAI,CAACC,KAAK,CAACD,IAAI,CAACQ,GAAG,CAACJ,KAAK,CAAC,GAAGJ,IAAI,CAACQ,GAAG,CAACH,CAAC,CAAC,CAAC;IACnD,OAAOI,UAAU,CAAC,CAACL,KAAK,GAAGJ,IAAI,CAACU,GAAG,CAACL,CAAC,EAAEE,CAAC,CAAC,EAAEI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGL,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI7E,WAAW,EAAE;MACf,IAAI;QACF,MAAM8E,mBAAmB,GAAG,IAAIhB,IAAI,CAAC,CAAC;QACtCgB,mBAAmB,CAACC,QAAQ,CAACD,mBAAmB,CAACE,QAAQ,CAAC,CAAC,GAAGC,QAAQ,CAAC7E,SAAS,CAACG,cAAc,CAAC,CAAC;QAEjG,MAAMpB,WAAW,CAAC+F,WAAW,CAC3BlF,WAAW,CAACmF,EAAE,EACdT,UAAU,CAACtE,SAAS,CAACE,IAAI,CAAC,EAC1BwE,mBAAmB,CAACM,WAAW,CAAC,CAAC,EACjChF,SAAS,CAACI,KACZ,CAAC;;QAED;QACA,MAAMsB,iBAAiB,GAAG,MAAM3C,WAAW,CAAC4C,kBAAkB,CAAC,CAAC;QAChEhC,YAAY,CAAC+B,iBAAiB,CAACE,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IACF;IAEA9B,iBAAiB,CAAC,KAAK,CAAC;IACxBF,cAAc,CAAC,IAAI,CAAC;IACpBI,YAAY,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,cAAc,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;EAC3D,CAAC;EAED,MAAM6E,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAEC,SAAiB,KAAK;IACrE,IAAI;MACF,MAAMpG,WAAW,CAACqG,eAAe,CAACF,KAAK,EAAEC,SAAS,CAAC;;MAEnD;MACA,MAAMzD,iBAAiB,GAAG,MAAM3C,WAAW,CAAC4C,kBAAkB,CAAC,CAAC;MAChEhC,YAAY,CAAC+B,iBAAiB,CAACE,IAAI,CAAC;IACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMwD,kBAAkB,GAAG,MAAAA,CAAOH,KAAa,EAAElC,QAAgB,KAAK;IACpE,IAAI;MACF,MAAMsC,QAAQ,GAAG,MAAMtG,aAAa,CAACuG,YAAY,CAACL,KAAK,CAAC;;MAExD;MACA,MAAMM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,QAAQ,CAAC1D,IAAI,CAAC,CAAC;MACtC,MAAM8D,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAGlD,QAAQ;MACxB+C,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;IACF;EACF,CAAC;EAED,MAAM2E,cAAc,GAAG,MAAOC,GAAa,IAAK;IAC9C,IAAI;MACF5G,cAAc,CAAC4G,GAAG,CAAC;MACnBjG,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAM8E,QAAQ,GAAG,MAAMrG,UAAU,CAACyH,cAAc,CAACD,GAAG,CAAC1B,EAAE,CAAC;MACxDrE,WAAW,CAAC4E,QAAQ,CAAC1D,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CnB,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;EAED,MAAMiG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAChG,UAAU,CAACiG,IAAI,CAAC,CAAC,IAAI,CAAChH,WAAW,EAAE;IAExC,IAAI;MACF,MAAM0F,QAAQ,GAAG,MAAMrG,UAAU,CAAC4H,WAAW,CAACjH,WAAW,CAACmF,EAAE,EAAEpE,UAAU,CAACiG,IAAI,CAAC,CAAC,CAAC;;MAEhF;MACAlG,WAAW,CAACoG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAExB,QAAQ,CAAC1D,IAAI,CAAC,CAAC;MAC7ChB,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMkF,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIC,QAAQ,GAAGtH,SAAS;;IAExB;IACA,IAAIW,YAAY,KAAK,KAAK,EAAE;MAC1B2G,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACR,GAAG,IAAIA,GAAG,CAACtE,MAAM,KAAK9B,YAAY,CAAC;IAChE;;IAEA;IACA,IAAIY,UAAU,EAAE;MACd,MAAMiG,IAAI,GAAGjG,UAAU,CAACkG,WAAW,CAAC,CAAC;MACrCH,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACR,GAAG,IAC5BA,GAAG,CAACW,SAAS,CAACD,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAAC,IAC1CT,GAAG,CAACzD,QAAQ,CAACmE,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAAC,IACzCT,GAAG,CAACa,WAAW,CAACH,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAAC,IAC5CT,GAAG,CAACc,YAAY,CAACJ,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAAC,IAC7CT,GAAG,CAACe,SAAS,CAACL,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAC3C,CAAC;IACH;;IAEA;IACAF,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAW,EAAEC,MAAW;MAE5B,QAAQhH,MAAM;QACZ,KAAK,SAAS;UACZ+G,MAAM,GAAG,IAAIlE,IAAI,CAACgE,CAAC,CAACI,OAAO,CAAC,CAAC/D,OAAO,CAAC,CAAC;UACtC8D,MAAM,GAAG,IAAInE,IAAI,CAACiE,CAAC,CAACG,OAAO,CAAC,CAAC/D,OAAO,CAAC,CAAC;UACtC;QACF,KAAK,UAAU;UACb,MAAMgE,aAAa,GAAG;YAAE,QAAQ,EAAE,CAAC;YAAE,MAAM,EAAE,CAAC;YAAE,QAAQ,EAAE,CAAC;YAAE,KAAK,EAAE;UAAE,CAAC;UACvEH,MAAM,GAAGG,aAAa,CAACL,CAAC,CAACrE,QAAQ,CAA+B,IAAI,CAAC;UACrEwE,MAAM,GAAGE,aAAa,CAACJ,CAAC,CAACtE,QAAQ,CAA+B,IAAI,CAAC;UACrE;QACF,KAAK,QAAQ;UACXuE,MAAM,GAAGF,CAAC,CAACvF,MAAM;UACjB0F,MAAM,GAAGF,CAAC,CAACxF,MAAM;UACjB;QACF,KAAK,MAAM;UACTyF,MAAM,GAAGF,CAAC,CAACxH,IAAI,IAAI,CAAC;UACpB2H,MAAM,GAAGF,CAAC,CAACzH,IAAI,IAAI,CAAC;UACpB;QACF;UACE0H,MAAM,GAAGF,CAAC,CAACI,OAAO;UAClBD,MAAM,GAAGF,CAAC,CAACG,OAAO;MACtB;MAEA,IAAI/G,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO6G,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEF,OAAOb,QAAQ;EACjB,CAAC;EAED,MAAMgB,YAAY,GAAGjB,wBAAwB,CAAC,CAAC;EAE/C,MAAMkB,KAAK,GAAG;IACZC,KAAK,EAAExI,SAAS,CAACyI,MAAM;IACvBC,OAAO,EAAE1I,SAAS,CAACuH,MAAM,CAACR,GAAG,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAACY,QAAQ,CAACZ,GAAG,CAACtE,MAAM,CAAC,CAAC,CAACgG,MAAM;IAC3HE,UAAU,EAAE3I,SAAS,CAACuH,MAAM,CAACR,GAAG,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAACY,QAAQ,CAACZ,GAAG,CAACtE,MAAM,CAAC,CAAC,CAACgG,MAAM;IAC5FG,SAAS,EAAE5I,SAAS,CAACuH,MAAM,CAACR,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACY,QAAQ,CAACZ,GAAG,CAACtE,MAAM,CAAC,CAAC,CAACgG,MAAM;IAC1FI,OAAO,EAAE7I,SAAS,CAAC8I,MAAM,CAAC,CAACC,GAAG,EAAEhC,GAAG,KAAKgC,GAAG,IAAIhC,GAAG,CAACvG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;EAClE,CAAC;EAED,oBACEd,OAAA;IAAK0D,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eAClC3D,OAAA,CAACsJ,SAAS;MAACC,KAAK;MAAA5F,QAAA,gBACd3D,OAAA,CAACwJ,GAAG;QAAC9F,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB3D,OAAA,CAACyJ,GAAG;UAAA9F,QAAA,gBACF3D,OAAA;YAAA2D,QAAA,gBACE3D,OAAA;cAAG0D,SAAS,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,0BAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL/D,OAAA;YAAG0D,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,gBAAc,EAACtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqJ,QAAQ,EAAC,GAAC;UAAA;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/D,OAAA,CAACwJ,GAAG;QAAC9F,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3D,OAAA,CAACyJ,GAAG;UAACE,EAAE,EAAE,CAAE;UAAAhG,QAAA,eACT3D,OAAA,CAAC4J,IAAI;YAAClG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,eAC1D3D,OAAA,CAAC4J,IAAI,CAACC,IAAI;cAAAlG,QAAA,gBACR3D,OAAA;gBAAG0D,SAAS,EAAC;cAAyC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3D/D,OAAA;gBAAA2D,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB/D,OAAA;gBAAI0D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEkF,KAAK,CAACC;cAAK;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN/D,OAAA,CAACyJ,GAAG;UAACE,EAAE,EAAE,CAAE;UAAAhG,QAAA,eACT3D,OAAA,CAAC4J,IAAI;YAAClG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,eAC1D3D,OAAA,CAAC4J,IAAI,CAACC,IAAI;cAAAlG,QAAA,gBACR3D,OAAA;gBAAG0D,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxD/D,OAAA;gBAAA2D,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB/D,OAAA;gBAAI0D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEkF,KAAK,CAACG;cAAO;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN/D,OAAA,CAACyJ,GAAG;UAACE,EAAE,EAAE,CAAE;UAAAhG,QAAA,eACT3D,OAAA,CAAC4J,IAAI;YAAClG,SAAS,EAAC,yCAAyC;YAAAC,QAAA,eACvD3D,OAAA,CAAC4J,IAAI,CAACC,IAAI;cAAAlG,QAAA,gBACR3D,OAAA;gBAAG0D,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnD/D,OAAA;gBAAA2D,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB/D,OAAA;gBAAI0D,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEkF,KAAK,CAACI;cAAU;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN/D,OAAA,CAACyJ,GAAG;UAACE,EAAE,EAAE,CAAE;UAAAhG,QAAA,eACT3D,OAAA,CAAC4J,IAAI;YAAClG,SAAS,EAAC,4CAA4C;YAAAC,QAAA,eAC1D3D,OAAA,CAAC4J,IAAI,CAACC,IAAI;cAAAlG,QAAA,gBACR3D,OAAA;gBAAG0D,SAAS,EAAC;cAA4C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9D/D,OAAA;gBAAA2D,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB/D,OAAA;gBAAI0D,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAC,GAAC,EAACkF,KAAK,CAACM,OAAO,CAAC/D,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGR/D,OAAA,CAAC4J,IAAI;QAAAjG,QAAA,gBACH3D,OAAA,CAAC4J,IAAI,CAACE,MAAM;UAAAnG,QAAA,eACV3D,OAAA,CAACwJ,GAAG;YAAC9F,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC3D,OAAA,CAACyJ,GAAG;cAACE,EAAE,EAAE,CAAE;cAAAhG,QAAA,eACT3D,OAAA;gBAAI0D,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAClB3D,OAAA;kBAAG0D,SAAS,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1B,EAAC6E,YAAY,CAACG,MAAM,EAAC,GAClC;cAAA;gBAAAnF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACN/D,OAAA,CAACyJ,GAAG;cAACE,EAAE,EAAE,CAAE;cAAAhG,QAAA,eACT3D,OAAA,CAACwJ,GAAG;gBAAC9F,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB3D,OAAA,CAACyJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAhG,QAAA,eACT3D,OAAA,CAAC+J,IAAI,CAACC,OAAO;oBACXC,IAAI,EAAC,MAAM;oBACXC,WAAW,EAAC,gBAAgB;oBAC5BC,KAAK,EAAEtI,UAAW;oBAClBuI,QAAQ,EAAGC,CAAC,IAAKvI,aAAa,CAACuI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC/CI,IAAI,EAAC;kBAAI;oBAAA3G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN/D,OAAA,CAACyJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAhG,QAAA,eACT3D,OAAA,CAAC+J,IAAI,CAACS,MAAM;oBACVD,IAAI,EAAC,IAAI;oBACTJ,KAAK,EAAElJ,YAAa;oBACpBmJ,QAAQ,EAAGC,CAAC,IAAKnJ,eAAe,CAACmJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAAAxG,QAAA,gBAEjD3D,OAAA;sBAAQmK,KAAK,EAAC,KAAK;sBAAAxG,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC/D,OAAA;sBAAQmK,KAAK,EAAC,WAAW;sBAAAxG,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5C/D,OAAA;sBAAQmK,KAAK,EAAC,aAAa;sBAAAxG,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjD/D,OAAA;sBAAQmK,KAAK,EAAC,QAAQ;sBAAAxG,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC/D,OAAA;sBAAQmK,KAAK,EAAC,qBAAqB;sBAAAxG,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjE/D,OAAA;sBAAQmK,KAAK,EAAC,WAAW;sBAAAxG,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5C/D,OAAA;sBAAQmK,KAAK,EAAC,YAAY;sBAAAxG,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC/C/D,OAAA;sBAAQmK,KAAK,EAAC,WAAW;sBAAAxG,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACN/D,OAAA,CAACyJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAhG,QAAA,eACT3D,OAAA,CAAC+J,IAAI,CAACS,MAAM;oBACVD,IAAI,EAAC,IAAI;oBACTJ,KAAK,EAAE1I,MAAO;oBACd2I,QAAQ,EAAGC,CAAC,IAAK3I,SAAS,CAAC2I,CAAC,CAACC,MAAM,CAACH,KAAY,CAAE;oBAAAxG,QAAA,gBAElD3D,OAAA;sBAAQmK,KAAK,EAAC,SAAS;sBAAAxG,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC7C/D,OAAA;sBAAQmK,KAAK,EAAC,UAAU;sBAAAxG,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClD/D,OAAA;sBAAQmK,KAAK,EAAC,QAAQ;sBAAAxG,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9C/D,OAAA;sBAAQmK,KAAK,EAAC,MAAM;sBAAAxG,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACN/D,OAAA,CAACyJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAhG,QAAA,eACT3D,OAAA,CAACyK,WAAW;oBAACF,IAAI,EAAC,IAAI;oBAAA5G,QAAA,gBACpB3D,OAAA,CAAC0K,MAAM;sBACLC,OAAO,EAAEhJ,SAAS,KAAK,MAAM,GAAG,SAAS,GAAG,iBAAkB;sBAC9DiJ,OAAO,EAAEA,CAAA,KAAMhJ,YAAY,CAAC,MAAM,CAAE;sBAAA+B,QAAA,eAEpC3D,OAAA;wBAAG0D,SAAS,EAAC;sBAAyB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC,eACT/D,OAAA,CAAC0K,MAAM;sBACLC,OAAO,EAAEhJ,SAAS,KAAK,KAAK,GAAG,SAAS,GAAG,iBAAkB;sBAC7DiJ,OAAO,EAAEA,CAAA,KAAMhJ,YAAY,CAAC,KAAK,CAAE;sBAAA+B,QAAA,eAEnC3D,OAAA;wBAAG0D,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACT/D,OAAA,CAAC0K,MAAM;sBACLC,OAAO,EAAE5I,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,iBAAkB;sBAC9D6I,OAAO,EAAEA,CAAA,KAAM5I,WAAW,CAAC,OAAO,CAAE;sBAAA2B,QAAA,eAEpC3D,OAAA;wBAAG0D,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC,eACT/D,OAAA,CAAC0K,MAAM;sBACLC,OAAO,EAAE5I,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,iBAAkB;sBAC9D6I,OAAO,EAAEA,CAAA,KAAM5I,WAAW,CAAC,OAAO,CAAE;sBAAA2B,QAAA,eAEpC3D,OAAA;wBAAG0D,SAAS,EAAC;sBAAiB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACd/D,OAAA,CAAC4J,IAAI,CAACC,IAAI;UAAAlG,QAAA,EACPiF,YAAY,CAACG,MAAM,GAAG,CAAC,GACtBhH,QAAQ,KAAK,OAAO,gBAClB/B,OAAA,CAAC6K,KAAK;YAACC,UAAU;YAACC,KAAK;YAACrH,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAC3C3D,OAAA;cAAA2D,QAAA,eACE3D,OAAA;gBAAA2D,QAAA,gBACE3D,OAAA;kBAAA2D,QAAA,EAAI;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd/D,OAAA;kBAAA2D,QAAA,EAAI;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjB/D,OAAA;kBAAA2D,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChB/D,OAAA;kBAAA2D,QAAA,EAAI;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrB/D,OAAA;kBAAA2D,QAAA,EAAI;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpB/D,OAAA;kBAAA2D,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACf/D,OAAA;kBAAA2D,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb/D,OAAA;kBAAA2D,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb/D,OAAA;kBAAA2D,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR/D,OAAA;cAAA2D,QAAA,EACGiF,YAAY,CAACoC,GAAG,CAAC3D,GAAG,iBACnBrH,OAAA;gBAAA2D,QAAA,gBACE3D,OAAA;kBAAA2D,QAAA,gBACE3D,OAAA;oBAAA2D,QAAA,EAAS0D,GAAG,CAACW;kBAAS;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eAChC/D,OAAA;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN/D,OAAA;oBAAO0D,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAC1BQ,UAAU,CAACkD,GAAG,CAACqB,OAAO;kBAAC;oBAAA9E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACL/D,OAAA;kBAAA2D,QAAA,EACGK,gBAAgB,CAACqD,GAAG,CAACpD,QAAQ;gBAAC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACL/D,OAAA;kBAAA2D,QAAA,eACE3D,OAAA;oBAAA2D,QAAA,gBACE3D,OAAA;sBAAA2D,QAAA,EAAS0D,GAAG,CAACa;oBAAW;sBAAAtE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eAClC/D,OAAA;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN/D,OAAA;sBAAO0D,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAE0D,GAAG,CAACc;oBAAY;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACL/D,OAAA;kBAAA2D,QAAA,eACE3D,OAAA;oBAAA2D,QAAA,gBACE3D,OAAA;sBAAG0D,SAAS,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpD/D,OAAA;sBAAA2D,QAAA,EAAS0D,GAAG,CAACzD,QAAQ,CAACmF,MAAM,GAAG,EAAE,GAAG1B,GAAG,CAACzD,QAAQ,CAACqH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG5D,GAAG,CAACzD;oBAAQ;sBAAAA,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eAC9F/D,OAAA;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN/D,OAAA;sBAAO0D,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAC1B0D,GAAG,CAAC6D,QAAQ,GAAGtG,cAAc,CAACyC,GAAG,CAAC6D,QAAQ,CAAC,GAAG;oBAAc;sBAAAtH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACL/D,OAAA;kBAAA2D,QAAA,eACE3D,OAAA;oBAAK0D,SAAS,EAAC,OAAO;oBAAAC,QAAA,gBACpB3D,OAAA;sBAAA2D,QAAA,gBAAK3D,OAAA;wBAAA2D,QAAA,EAAQ;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACsD,GAAG,CAACe,SAAS;oBAAA;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjD/D,OAAA;sBAAA2D,QAAA,gBAAK3D,OAAA;wBAAA2D,QAAA,EAAQ;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACsD,GAAG,CAAC8D,MAAM;oBAAA;sBAAAvH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChD/D,OAAA;sBAAA2D,QAAA,gBAAK3D,OAAA;wBAAA2D,QAAA,EAAQ;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACsD,GAAG,CAAC+D,SAAS;oBAAA;sBAAAxH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClD/D,OAAA;sBAAA2D,QAAA,gBAAK3D,OAAA;wBAAA2D,QAAA,EAAQ;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACsD,GAAG,CAACgE,SAAS;oBAAA;sBAAAzH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACL/D,OAAA;kBAAA2D,QAAA,EAAKb,cAAc,CAACuE,GAAG,CAACtE,MAAM;gBAAC;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrC/D,OAAA;kBAAA2D,QAAA,EACG0D,GAAG,CAACvG,IAAI,GAAG,IAAIuG,GAAG,CAACvG,IAAI,CAACsE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;gBAAG;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACL/D,OAAA;kBAAA2D,QAAA,eACE3D,OAAA;oBAAK0D,SAAS,EAAC,OAAO;oBAAAC,QAAA,gBACpB3D,OAAA;sBAAA2D,QAAA,eAAK3D,OAAA;wBAAA2D,QAAA,EAAQ;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpC/D,OAAA;sBAAA2D,QAAA,EAAM,IAAIW,IAAI,CAAC+C,GAAG,CAACqB,OAAO,CAAC,CAAC4C,kBAAkB,CAAC;oBAAC;sBAAA1H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EACtDsD,GAAG,CAACkE,uBAAuB,iBAC1BvL,OAAA,CAAAE,SAAA;sBAAAyD,QAAA,gBACE3D,OAAA;wBAAA2D,QAAA,eAAK3D,OAAA;0BAAA2D,QAAA,EAAQ;wBAAI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChC/D,OAAA;wBAAA2D,QAAA,EAAM,IAAIW,IAAI,CAAC+C,GAAG,CAACkE,uBAAuB,CAAC,CAACD,kBAAkB,CAAC;sBAAC;wBAAA1H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,eACvE,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACP/D,OAAA;kBAAA2D,QAAA,eACE3D,OAAA;oBAAK0D,SAAS,EAAC,oBAAoB;oBAAC8H,IAAI,EAAC,OAAO;oBAAA7H,QAAA,gBAE9C3D,OAAA,CAAC0K,MAAM;sBACLC,OAAO,EAAC,cAAc;sBACtBJ,IAAI,EAAC,IAAI;sBACTK,OAAO,EAAEA,CAAA,KAAM3E,kBAAkB,CAACoB,GAAG,CAAC1B,EAAE,EAAE0B,GAAG,CAACzD,QAAQ,CAAE;sBACxD6H,KAAK,EAAC,eAAe;sBAAA9H,QAAA,gBAErB3D,OAAA;wBAAG0D,SAAS,EAAC;sBAAsB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,YAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAERsD,GAAG,CAACtE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAAAE,SAAA;sBAAAyD,QAAA,gBACE3D,OAAA,CAAC0K,MAAM;wBACLC,OAAO,EAAC,iBAAiB;wBACzBJ,IAAI,EAAC,IAAI;wBACTK,OAAO,EAAEA,CAAA,KAAM;0BACbnK,cAAc,CAAC4G,GAAG,CAAC;0BACnB1G,iBAAiB,CAAC,IAAI,CAAC;wBACzB,CAAE;wBAAAgD,QAAA,gBAEF3D,OAAA;0BAAG0D,SAAS,EAAC;wBAAyB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,SAE7C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC0K,MAAM;wBACLC,OAAO,EAAC,gBAAgB;wBACxBJ,IAAI,EAAC,IAAI;wBACTK,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,UAAU,CAAE;wBAAAhC,QAAA,gBAEtD3D,OAAA;0BAAG0D,SAAS,EAAC;wBAAmB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,UAEvC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH,EAEAsD,GAAG,CAACtE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAAC0K,MAAM;sBACLC,OAAO,EAAC,cAAc;sBACtBJ,IAAI,EAAC,IAAI;sBACTK,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,YAAY,CAAE;sBAAAhC,QAAA,gBAExD3D,OAAA;wBAAG0D,SAAS,EAAC;sBAAkB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,SAEtC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT,EAEAsD,GAAG,CAACtE,MAAM,KAAK,YAAY,iBAC1B/C,OAAA,CAAC0K,MAAM;sBACLC,OAAO,EAAC,iBAAiB;sBACzBJ,IAAI,EAAC,IAAI;sBACTK,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,WAAW,CAAE;sBAAAhC,QAAA,gBAEvD3D,OAAA;wBAAG0D,SAAS,EAAC;sBAAmB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,YAEvC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT,EAEAsD,GAAG,CAACtE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAAC0K,MAAM;sBACLC,OAAO,EAAC,iBAAiB;sBACzBJ,IAAI,EAAC,IAAI;sBACTK,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,WAAW,CAAE;sBAAAhC,QAAA,gBAEvD3D,OAAA;wBAAG0D,SAAS,EAAC;sBAAmB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,WAEvC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT,eAED/D,OAAA,CAAC0K,MAAM;sBACLC,OAAO,EAAC,mBAAmB;sBAC3BJ,IAAI,EAAC,IAAI;sBACTK,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAACC,GAAG,CAAE;sBACnCoE,KAAK,EAAC,MAAM;sBAAA9H,QAAA,eAEZ3D,OAAA;wBAAG0D,SAAS,EAAC;sBAAgB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAnIIsD,GAAG,CAAC1B,EAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoIb,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;UAAA;UAER;UACA/D,OAAA,CAACwJ,GAAG;YAAA7F,QAAA,EACDiF,YAAY,CAACoC,GAAG,CAAC3D,GAAG;cAAA,IAAAqE,aAAA;cAAA,oBACnB1L,OAAA,CAACyJ,GAAG;gBAAcE,EAAE,EAAE,CAAE;gBAACgC,EAAE,EAAE,CAAE;gBAACjI,SAAS,EAAC,MAAM;gBAAAC,QAAA,eAC9C3D,OAAA,CAAC4J,IAAI;kBAAClG,SAAS,EAAE,4BAA4B2D,GAAG,CAACpD,QAAQ,KAAK,QAAQ,GAAG,iBAAiB,GAAG,EAAE,aAAa,EAAAyH,aAAA,GAAArE,GAAG,CAACpD,QAAQ,cAAAyH,aAAA,uBAAZA,aAAA,CAAc3D,WAAW,CAAC,CAAC,KAAI,QAAQ,EAAG;kBAAApE,QAAA,gBACpJ3D,OAAA,CAAC4J,IAAI,CAACE,MAAM;oBAACpG,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,gBACxE3D,OAAA;sBAAA2D,QAAA,gBACE3D,OAAA;wBAAA2D,QAAA,EAAS0D,GAAG,CAACW;sBAAS;wBAAApE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eAChC/D,OAAA;wBAAA4D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,EACLC,gBAAgB,CAACqD,GAAG,CAACpD,QAAQ,CAAC;oBAAA;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,EACLjB,cAAc,CAACuE,GAAG,CAACtE,MAAM,CAAC;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,eACd/D,OAAA,CAAC4J,IAAI,CAACC,IAAI;oBAAAlG,QAAA,gBACR3D,OAAA;sBAAK0D,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnB3D,OAAA;wBAAA2D,QAAA,EAAS0D,GAAG,CAACa;sBAAW;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eAClC/D,OAAA;wBAAA4D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN/D,OAAA;wBAAO0D,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAE0D,GAAG,CAACc;sBAAY;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC,eACN/D,OAAA;sBAAK0D,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnB3D,OAAA;wBAAG0D,SAAS,EAAC;sBAAkC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpD/D,OAAA;wBAAA2D,QAAA,EAAQ0D,GAAG,CAACzD,QAAQ,CAACmF,MAAM,GAAG,EAAE,GAAG1B,GAAG,CAACzD,QAAQ,CAACqH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG5D,GAAG,CAACzD;sBAAQ;wBAAAA,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC,eACN/D,OAAA;sBAAK0D,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzB3D,OAAA;wBAAA2D,QAAA,gBAAK3D,OAAA;0BAAA2D,QAAA,EAAQ;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACsD,GAAG,CAACe,SAAS;sBAAA;wBAAAxE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjD/D,OAAA;wBAAA2D,QAAA,gBAAK3D,OAAA;0BAAA2D,QAAA,EAAQ;wBAAO;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACsD,GAAG,CAAC8D,MAAM;sBAAA;wBAAAvH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAChD/D,OAAA;wBAAA2D,QAAA,gBAAK3D,OAAA;0BAAA2D,QAAA,EAAQ;wBAAM;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACsD,GAAG,CAAC+D,SAAS;sBAAA;wBAAAxH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAClD/D,OAAA;wBAAA2D,QAAA,gBAAK3D,OAAA;0BAAA2D,QAAA,EAAQ;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACsD,GAAG,CAACgE,SAAS;sBAAA;wBAAAzH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,EACLsD,GAAG,CAACvG,IAAI,iBACPd,OAAA;sBAAK0D,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB3D,OAAA;wBAAQ0D,SAAS,EAAC,cAAc;wBAAAC,QAAA,GAAC,GAAC,EAAC0D,GAAG,CAACvG,IAAI,CAACsE,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CACN,eACD/D,OAAA;sBAAK0D,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAC9BQ,UAAU,CAACkD,GAAG,CAACqB,OAAO;oBAAC;sBAAA9E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ/D,OAAA,CAAC4J,IAAI,CAACgC,MAAM;oBAAAjI,QAAA,eACV3D,OAAA;sBAAK0D,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,gBACpD3D,OAAA,CAAC0K,MAAM;wBACLC,OAAO,EAAC,cAAc;wBACtBJ,IAAI,EAAC,IAAI;wBACTK,OAAO,EAAEA,CAAA,KAAM3E,kBAAkB,CAACoB,GAAG,CAAC1B,EAAE,EAAE0B,GAAG,CAACzD,QAAQ,CAAE;wBACxD6H,KAAK,EAAC,eAAe;wBAAA9H,QAAA,eAErB3D,OAAA;0BAAG0D,SAAS,EAAC;wBAAiB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,EAERsD,GAAG,CAACtE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAAAE,SAAA;wBAAAyD,QAAA,gBACE3D,OAAA,CAAC0K,MAAM;0BACLC,OAAO,EAAC,iBAAiB;0BACzBJ,IAAI,EAAC,IAAI;0BACTK,OAAO,EAAEA,CAAA,KAAM;4BACbnK,cAAc,CAAC4G,GAAG,CAAC;4BACnB1G,iBAAiB,CAAC,IAAI,CAAC;0BACzB,CAAE;0BACF8K,KAAK,EAAC,OAAO;0BAAA9H,QAAA,eAEb3D,OAAA;4BAAG0D,SAAS,EAAC;0BAAoB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC,eACT/D,OAAA,CAAC0K,MAAM;0BACLC,OAAO,EAAC,gBAAgB;0BACxBJ,IAAI,EAAC,IAAI;0BACTK,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,UAAU,CAAE;0BACtD8F,KAAK,EAAC,QAAQ;0BAAA9H,QAAA,eAEd3D,OAAA;4BAAG0D,SAAS,EAAC;0BAAc;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1B,CAAC;sBAAA,eACT,CACH,EAEAsD,GAAG,CAACtE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAAC0K,MAAM;wBACLC,OAAO,EAAC,cAAc;wBACtBJ,IAAI,EAAC,IAAI;wBACTK,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,YAAY,CAAE;wBACxD8F,KAAK,EAAC,OAAO;wBAAA9H,QAAA,eAEb3D,OAAA;0BAAG0D,SAAS,EAAC;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CACT,EAEAsD,GAAG,CAACtE,MAAM,KAAK,YAAY,iBAC1B/C,OAAA,CAAC0K,MAAM;wBACLC,OAAO,EAAC,iBAAiB;wBACzBJ,IAAI,EAAC,IAAI;wBACTK,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,WAAW,CAAE;wBACvD8F,KAAK,EAAC,UAAU;wBAAA9H,QAAA,eAEhB3D,OAAA;0BAAG0D,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CACT,EAEAsD,GAAG,CAACtE,MAAM,KAAK,WAAW,iBACzB/C,OAAA,CAAC0K,MAAM;wBACLC,OAAO,EAAC,iBAAiB;wBACzBJ,IAAI,EAAC,IAAI;wBACTK,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAACwB,GAAG,CAAC1B,EAAE,EAAE,WAAW,CAAE;wBACvD8F,KAAK,EAAC,SAAS;wBAAA9H,QAAA,eAEf3D,OAAA;0BAAG0D,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CACT,eAED/D,OAAA,CAAC0K,MAAM;wBACLC,OAAO,EAAC,mBAAmB;wBAC3BJ,IAAI,EAAC,IAAI;wBACTK,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAACC,GAAG,CAAE;wBACnCoE,KAAK,EAAC,MAAM;wBAAA9H,QAAA,eAEZ3D,OAAA;0BAAG0D,SAAS,EAAC;wBAAgB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,eAET/D,OAAA,CAAC0K,MAAM;wBACLC,OAAO,EAAC,cAAc;wBACtBJ,IAAI,EAAC,IAAI;wBACTK,OAAO,EAAEA,CAAA,KAAM;0BACbnK,cAAc,CAAC4G,GAAG,CAAC;0BACnBnF,sBAAsB,CAAC,IAAI,CAAC;wBAC9B,CAAE;wBACFuJ,KAAK,EAAC,cAAc;wBAAA9H,QAAA,eAEpB3D,OAAA;0BAAG0D,SAAS,EAAC;wBAAY;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GA7HCsD,GAAG,CAAC1B,EAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8HX,CAAC;YAAA,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,gBAED/D,OAAA,CAAC6L,KAAK;YAAClB,OAAO,EAAC,MAAM;YAAAhH,QAAA,gBACnB3D,OAAA;cAAG0D,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,0CAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGP/D,OAAA,CAAC8L,KAAK;QAACC,IAAI,EAAErL,cAAe;QAACsL,MAAM,EAAEA,CAAA,KAAMrL,iBAAiB,CAAC,KAAK,CAAE;QAAAgD,QAAA,gBAClE3D,OAAA,CAAC8L,KAAK,CAAChC,MAAM;UAACmC,WAAW;UAAAtI,QAAA,eACvB3D,OAAA,CAAC8L,KAAK,CAACI,KAAK;YAAAvI,QAAA,gBACV3D,OAAA;cAAG0D,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,oBAC3B,EAACvD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwH,SAAS;UAAA;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACf/D,OAAA,CAAC8L,KAAK,CAACjC,IAAI;UAAAlG,QAAA,EACRnD,WAAW,iBACVR,OAAA,CAAAE,SAAA;YAAAyD,QAAA,gBACE3D,OAAA;cAAK0D,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxC3D,OAAA;gBAAA2D,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB/D,OAAA;gBAAK0D,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB3D,OAAA;kBAAK0D,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBACpB3D,OAAA;oBAAA2D,QAAA,EAAQ;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAACoD,QAAQ,eAAC5D,OAAA;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnD/D,OAAA;oBAAA2D,QAAA,EAAQ;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC4H,SAAS,eAACpI,OAAA;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpD/D,OAAA;oBAAA2D,QAAA,EAAQ;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC2K,MAAM;gBAAA;kBAAAvH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACN/D,OAAA;kBAAK0D,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBACpB3D,OAAA;oBAAA2D,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC4K,SAAS,eAACpL,OAAA;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrD/D,OAAA;oBAAA2D,QAAA,EAAQ;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC6K,SAAS,eAACrL,OAAA;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpD/D,OAAA;oBAAA2D,QAAA,EAAQ;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC0H,WAAW;gBAAA;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLvD,WAAW,CAAC2L,OAAO,iBAClBnM,OAAA;gBAAK0D,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB3D,OAAA;kBAAA2D,QAAA,EAAQ;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC2L,OAAO;cAAA;gBAAAvI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN/D,OAAA,CAAC+J,IAAI;cAAApG,QAAA,gBACH3D,OAAA,CAACwJ,GAAG;gBAAA7F,QAAA,gBACF3D,OAAA,CAACyJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAhG,QAAA,eACT3D,OAAA,CAAC+J,IAAI,CAACqC,KAAK;oBAAC1I,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B3D,OAAA,CAAC+J,IAAI,CAACsC,KAAK;sBAAA1I,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjC/D,OAAA,CAACsM,UAAU;sBAAA3I,QAAA,gBACT3D,OAAA,CAACsM,UAAU,CAACC,IAAI;wBAAA5I,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAiB,CAAC,eACpC/D,OAAA,CAAC+J,IAAI,CAACC,OAAO;wBACXC,IAAI,EAAC,QAAQ;wBACbuC,IAAI,EAAC,MAAM;wBACXtC,WAAW,EAAC,MAAM;wBAClBC,KAAK,EAAEvJ,SAAS,CAACE,IAAK;wBACtBsJ,QAAQ,EAAGC,CAAC,IAAKxJ,YAAY,CAAC6G,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE5G,IAAI,EAAEuJ,CAAC,CAACC,MAAM,CAACH;wBAAM,CAAC,CAAC,CAAE;wBAC3EsC,QAAQ;sBAAA;wBAAA7I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN/D,OAAA,CAACyJ,GAAG;kBAACE,EAAE,EAAE,CAAE;kBAAAhG,QAAA,eACT3D,OAAA,CAAC+J,IAAI,CAACqC,KAAK;oBAAC1I,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B3D,OAAA,CAAC+J,IAAI,CAACsC,KAAK;sBAAA1I,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACxC/D,OAAA,CAAC+J,IAAI,CAACC,OAAO;sBACXC,IAAI,EAAC,QAAQ;sBACbyC,GAAG,EAAC,GAAG;sBACPxC,WAAW,EAAC,mBAAmB;sBAC/BC,KAAK,EAAEvJ,SAAS,CAACG,cAAe;sBAChCqJ,QAAQ,EAAGC,CAAC,IAAKxJ,YAAY,CAAC6G,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAE3G,cAAc,EAAEsJ,CAAC,CAACC,MAAM,CAACH;sBAAM,CAAC,CAAC,CAAE;sBACrFsC,QAAQ;oBAAA;sBAAA7I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN/D,OAAA,CAAC+J,IAAI,CAACqC,KAAK;gBAAC1I,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B3D,OAAA,CAAC+J,IAAI,CAACsC,KAAK;kBAAA1I,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzC/D,OAAA,CAAC+J,IAAI,CAACC,OAAO;kBACX2C,EAAE,EAAC,UAAU;kBACbC,IAAI,EAAE,CAAE;kBACR1C,WAAW,EAAC,yCAAyC;kBACrDC,KAAK,EAAEvJ,SAAS,CAACI,KAAM;kBACvBoJ,QAAQ,EAAGC,CAAC,IAAKxJ,YAAY,CAAC6G,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE1G,KAAK,EAAEqJ,CAAC,CAACC,MAAM,CAACH;kBAAM,CAAC,CAAC;gBAAE;kBAAAvG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA,eACP;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eACb/D,OAAA,CAAC8L,KAAK,CAACF,MAAM;UAAAjI,QAAA,gBACX3D,OAAA,CAAC0K,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,OAAO,EAAEA,CAAA,KAAMjK,iBAAiB,CAAC,KAAK,CAAE;YAAAgD,QAAA,EAAC;UAErE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC0K,MAAM;YACLC,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAEvF,iBAAkB;YAC3BwH,QAAQ,EAAE,CAACjM,SAAS,CAACE,IAAI,IAAI,CAACF,SAAS,CAACG,cAAe;YAAA4C,QAAA,gBAEvD3D,OAAA;cAAG0D,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,cAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGR/D,OAAA,CAAC8L,KAAK;QAACC,IAAI,EAAE5K,aAAc;QAAC6K,MAAM,EAAEA,CAAA,KAAM5K,gBAAgB,CAAC,KAAK,CAAE;QAACmJ,IAAI,EAAC,IAAI;QAAA5G,QAAA,gBAC1E3D,OAAA,CAAC8L,KAAK,CAAChC,MAAM;UAACmC,WAAW;UAAAtI,QAAA,eACvB3D,OAAA,CAAC8L,KAAK,CAACI,KAAK;YAAAvI,QAAA,gBACV3D,OAAA;cAAG0D,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,WAChC,EAACvD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwH,SAAS;UAAA;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACf/D,OAAA,CAAC8L,KAAK,CAACjC,IAAI;UAAAlG,QAAA,gBACT3D,OAAA;YAAK8M,KAAK,EAAE;cAAEC,MAAM,EAAE,OAAO;cAAEC,SAAS,EAAE,MAAM;cAAEC,MAAM,EAAE,mBAAmB;cAAEC,YAAY,EAAE,UAAU;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAzJ,QAAA,EAC9ItC,QAAQ,CAAC0H,MAAM,GAAG,CAAC,GAClB1H,QAAQ,CAAC2J,GAAG,CAAEqC,OAAO,iBACnBrN,OAAA;cAAsB0D,SAAS,EAAE,QAAQ2J,OAAO,CAACC,iBAAiB,GAAG,UAAU,GAAG,YAAY,EAAG;cAAA3J,QAAA,eAC/F3D,OAAA;gBAAK0D,SAAS,EAAE,8BAA8B2J,OAAO,CAACC,iBAAiB,GAAG,uBAAuB,GAAG,UAAU,EAAG;gBAACR,KAAK,EAAE;kBAAES,QAAQ,EAAE;gBAAM,CAAE;gBAAA5J,QAAA,gBAC3I3D,OAAA;kBAAA2D,QAAA,EAAM0J,OAAO,CAACG;gBAAO;kBAAA5J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5B/D,OAAA;kBAAO0D,SAAS,EAAE,gBAAgB2J,OAAO,CAACC,iBAAiB,GAAG,YAAY,GAAG,YAAY,EAAG;kBAAA3J,QAAA,GACzF0J,OAAO,CAACI,UAAU,EAAC,KAAG,EAAC,IAAInJ,IAAI,CAAC+I,OAAO,CAACK,MAAM,CAAC,CAACC,cAAc,CAAC,CAAC;gBAAA;kBAAA/J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC,GANEsJ,OAAO,CAAC1H,EAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOf,CACN,CAAC,gBAEF/D,OAAA;cAAK0D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC3D,OAAA;gBAAG0D,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9C/D,OAAA;gBAAA2D,QAAA,EAAG;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN/D,OAAA;YAAK0D,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB3D,OAAA,CAAC+J,IAAI,CAACC,OAAO;cACXC,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,sBAAsB;cAClCC,KAAK,EAAE5I,UAAW;cAClB6I,QAAQ,EAAGC,CAAC,IAAK7I,aAAa,CAAC6I,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CyD,SAAS,EAAGvD,CAAC,IAAKA,CAAC,CAACwD,GAAG,KAAK,OAAO,IAAItG,iBAAiB,CAAC,CAAE;cAC3D7D,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACF/D,OAAA,CAAC0K,MAAM;cAACC,OAAO,EAAC,SAAS;cAACC,OAAO,EAAErD,iBAAkB;cAACsF,QAAQ,EAAE,CAACtL,UAAU,CAACiG,IAAI,CAAC,CAAE;cAAA7D,QAAA,eACjF3D,OAAA;gBAAG0D,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACb/D,OAAA,CAAC8L,KAAK,CAACF,MAAM;UAAAjI,QAAA,eACX3D,OAAA,CAAC0K,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,OAAO,EAAEA,CAAA,KAAMxJ,gBAAgB,CAAC,KAAK,CAAE;YAAAuC,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGR/D,OAAA,CAAC8L,KAAK;QAACC,IAAI,EAAE9J,mBAAoB;QAAC+J,MAAM,EAAEA,CAAA,KAAM9J,sBAAsB,CAAC,KAAK,CAAE;QAACqI,IAAI,EAAC,IAAI;QAAC7G,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBACpH3D,OAAA,CAAC8L,KAAK,CAAChC,MAAM;UAACmC,WAAW;UAAAtI,QAAA,eACvB3D,OAAA,CAAC8L,KAAK,CAACI,KAAK;YAAAvI,QAAA,gBACV3D,OAAA;cAAG0D,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,kBAC7B,EAACvD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwH,SAAS;UAAA;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACf/D,OAAA,CAAC8L,KAAK,CAACjC,IAAI;UAAAlG,QAAA,EACRnD,WAAW,iBACVR,OAAA,CAACwJ,GAAG;YAAA7F,QAAA,gBACF3D,OAAA,CAACyJ,GAAG;cAACE,EAAE,EAAE,CAAE;cAAAhG,QAAA,gBACT3D,OAAA,CAAC4J,IAAI;gBAAClG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACpB3D,OAAA,CAAC4J,IAAI,CAACE,MAAM;kBAAAnG,QAAA,eACV3D,OAAA;oBAAI0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAClB3D,OAAA;sBAAG0D,SAAS,EAAC;oBAAkB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,uBAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eACd/D,OAAA,CAAC4J,IAAI,CAACC,IAAI;kBAAAlG,QAAA,gBACR3D,OAAA;oBAAA2D,QAAA,gBAAK3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC0H,WAAW;kBAAA;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3D/D,OAAA;oBAAA2D,QAAA,gBAAK3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC2H,YAAY;kBAAA;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eAEP/D,OAAA,CAAC4J,IAAI;gBAAClG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACpB3D,OAAA,CAAC4J,IAAI,CAACE,MAAM;kBAAAnG,QAAA,eACV3D,OAAA;oBAAI0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAClB3D,OAAA;sBAAG0D,SAAS,EAAC;oBAAkB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,oBAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eACd/D,OAAA,CAAC4J,IAAI,CAACC,IAAI;kBAAAlG,QAAA,gBACR3D,OAAA;oBAAA2D,QAAA,gBAAK3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAACoD,QAAQ;kBAAA;oBAAAA,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC5DvD,WAAW,CAAC0K,QAAQ,iBACnBlL,OAAA;oBAAA2D,QAAA,gBAAK3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACa,cAAc,CAACpE,WAAW,CAAC0K,QAAQ,CAAC;kBAAA;oBAAAtH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAC7E,eACD/D,OAAA;oBAAK0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnB3D,OAAA,CAAC0K,MAAM;sBACLC,OAAO,EAAC,iBAAiB;sBACzBJ,IAAI,EAAC,IAAI;sBACTK,OAAO,EAAEA,CAAA,KAAM3E,kBAAkB,CAACzF,WAAW,CAACmF,EAAE,EAAEnF,WAAW,CAACoD,QAAQ,CAAE;sBAAAD,QAAA,gBAExE3D,OAAA;wBAAG0D,SAAS,EAAC;sBAAsB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,iBAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN/D,OAAA,CAACyJ,GAAG;cAACE,EAAE,EAAE,CAAE;cAAAhG,QAAA,gBACT3D,OAAA,CAAC4J,IAAI;gBAAClG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACpB3D,OAAA,CAAC4J,IAAI,CAACE,MAAM;kBAAAnG,QAAA,eACV3D,OAAA;oBAAI0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAClB3D,OAAA;sBAAG0D,SAAS,EAAC;oBAAmB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,wBAEvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eACd/D,OAAA,CAAC4J,IAAI,CAACC,IAAI;kBAAAlG,QAAA,gBACR3D,OAAA;oBAAA2D,QAAA,gBAAK3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC4H,SAAS;kBAAA;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/D/D,OAAA;oBAAA2D,QAAA,gBAAK3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC2K,MAAM;kBAAA;oBAAAvH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxD/D,OAAA;oBAAA2D,QAAA,gBAAK3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC4K,SAAS;kBAAA;oBAAAxH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/D/D,OAAA;oBAAA2D,QAAA,gBAAK3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvD,WAAW,CAAC6K,SAAS;kBAAA;oBAAAzH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC9DvD,WAAW,CAAC2L,OAAO,iBAClBnM,OAAA;oBAAK0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzB/D,OAAA;sBAAK0D,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EACvCnD,WAAW,CAAC2L;oBAAO;sBAAAvI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eAEP/D,OAAA,CAAC4J,IAAI;gBAAClG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACpB3D,OAAA,CAAC4J,IAAI,CAACE,MAAM;kBAAAnG,QAAA,eACV3D,OAAA;oBAAI0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAClB3D,OAAA;sBAAG0D,SAAS,EAAC;oBAAkB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,cAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eACd/D,OAAA,CAAC4J,IAAI,CAACC,IAAI;kBAAAlG,QAAA,gBACR3D,OAAA;oBAAK0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACjB,cAAc,CAACtC,WAAW,CAACuC,MAAM,CAAC;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACN/D,OAAA;oBAAK0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACC,gBAAgB,CAACxD,WAAW,CAACyD,QAAQ,CAAC;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,EACLvD,WAAW,CAACM,IAAI,iBACfd,OAAA;oBAAK0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAA/D,OAAA;sBAAM0D,SAAS,EAAC,cAAc;sBAAAC,QAAA,GAAC,GAAC,EAACnD,WAAW,CAACM,IAAI,CAACsE,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF,CACN,eACD/D,OAAA;oBAAK0D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIO,IAAI,CAAC9D,WAAW,CAACkI,OAAO,CAAC,CAACiF,cAAc,CAAC,CAAC;kBAAA;oBAAA/J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,EACLvD,WAAW,CAAC+K,uBAAuB,iBAClCvL,OAAA;oBAAA2D,QAAA,gBACE3D,OAAA;sBAAA2D,QAAA,EAAQ;oBAAqB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIO,IAAI,CAAC9D,WAAW,CAAC+K,uBAAuB,CAAC,CAACoC,cAAc,CAAC,CAAC;kBAAA;oBAAA/J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnG,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eACb/D,OAAA,CAAC8L,KAAK,CAACF,MAAM;UAAAjI,QAAA,gBACX3D,OAAA,CAAC0K,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,OAAO,EAAEA,CAAA,KAAM1I,sBAAsB,CAAC,KAAK,CAAE;YAAAyB,QAAA,EAAC;UAE1E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRvD,WAAW,iBACVR,OAAA,CAAC0K,MAAM;YACLC,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAEA,CAAA,KAAM;cACb1I,sBAAsB,CAAC,KAAK,CAAC;cAC7BkF,cAAc,CAAC5G,WAAW,CAAC;YAC7B,CAAE;YAAAmD,QAAA,gBAEF3D,OAAA;cAAG0D,SAAS,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,cAEzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAC3D,EAAA,CA19BID,oBAA8B;EAAA,QACjBT,OAAO;AAAA;AAAAoO,EAAA,GADpB3N,oBAA8B;AA49BpC,eAAeA,oBAAoB;AAAC,IAAA2N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}