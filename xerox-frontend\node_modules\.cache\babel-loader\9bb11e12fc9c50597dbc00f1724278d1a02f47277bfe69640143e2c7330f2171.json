{"ast": null, "code": "import { isMotionValue, motionValue } from 'motion-dom';\nimport { animateMotionValue } from '../interfaces/motion-value.mjs';\nfunction animateSingleValue(value, keyframes, options) {\n  const motionValue$1 = isMotionValue(value) ? value : motionValue(value);\n  motionValue$1.start(animateMotionValue(\"\", motionValue$1, keyframes, options));\n  return motionValue$1.animation;\n}\nexport { animateSingleValue };", "map": {"version": 3, "names": ["isMotionValue", "motionValue", "animateMotionValue", "animateSingleValue", "value", "keyframes", "options", "motionValue$1", "start", "animation"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/animation/animate/single-value.mjs"], "sourcesContent": ["import { isMotionValue, motionValue } from 'motion-dom';\nimport { animateMotionValue } from '../interfaces/motion-value.mjs';\n\nfunction animateSingleValue(value, keyframes, options) {\n    const motionValue$1 = isMotionValue(value) ? value : motionValue(value);\n    motionValue$1.start(animateMotionValue(\"\", motionValue$1, keyframes, options));\n    return motionValue$1.animation;\n}\n\nexport { animateSingleValue };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,WAAW,QAAQ,YAAY;AACvD,SAASC,kBAAkB,QAAQ,gCAAgC;AAEnE,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACnD,MAAMC,aAAa,GAAGP,aAAa,CAACI,KAAK,CAAC,GAAGA,KAAK,GAAGH,WAAW,CAACG,KAAK,CAAC;EACvEG,aAAa,CAACC,KAAK,CAACN,kBAAkB,CAAC,EAAE,EAAEK,aAAa,EAAEF,SAAS,EAAEC,OAAO,CAAC,CAAC;EAC9E,OAAOC,aAAa,CAACE,SAAS;AAClC;AAEA,SAASN,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}