{"ast": null, "code": "import { getValueTransition, frame, JSAnimation, AsyncMotionValueAnimation } from 'motion-dom';\nimport { secondsToMilliseconds, MotionGlobalConfig } from 'motion-utils';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isTransitionDefined } from '../utils/is-transition-defined.mjs';\nconst animateMotionValue = (name, value, target, transition = {}, element, isHandoff) => onComplete => {\n  const valueTransition = getValueTransition(transition, name) || {};\n  /**\n   * Most transition values are currently completely overwritten by value-specific\n   * transitions. In the future it'd be nicer to blend these transitions. But for now\n   * delay actually does inherit from the root transition if not value-specific.\n   */\n  const delay = valueTransition.delay || transition.delay || 0;\n  /**\n   * Elapsed isn't a public transition option but can be passed through from\n   * optimized appear effects in milliseconds.\n   */\n  let {\n    elapsed = 0\n  } = transition;\n  elapsed = elapsed - secondsToMilliseconds(delay);\n  const options = {\n    keyframes: Array.isArray(target) ? target : [null, target],\n    ease: \"easeOut\",\n    velocity: value.getVelocity(),\n    ...valueTransition,\n    delay: -elapsed,\n    onUpdate: v => {\n      value.set(v);\n      valueTransition.onUpdate && valueTransition.onUpdate(v);\n    },\n    onComplete: () => {\n      onComplete();\n      valueTransition.onComplete && valueTransition.onComplete();\n    },\n    name,\n    motionValue: value,\n    element: isHandoff ? undefined : element\n  };\n  /**\n   * If there's no transition defined for this value, we can generate\n   * unique transition settings for this value.\n   */\n  if (!isTransitionDefined(valueTransition)) {\n    Object.assign(options, getDefaultTransition(name, options));\n  }\n  /**\n   * Both WAAPI and our internal animation functions use durations\n   * as defined by milliseconds, while our external API defines them\n   * as seconds.\n   */\n  options.duration && (options.duration = secondsToMilliseconds(options.duration));\n  options.repeatDelay && (options.repeatDelay = secondsToMilliseconds(options.repeatDelay));\n  /**\n   * Support deprecated way to set initial value. Prefer keyframe syntax.\n   */\n  if (options.from !== undefined) {\n    options.keyframes[0] = options.from;\n  }\n  let shouldSkip = false;\n  if (options.type === false || options.duration === 0 && !options.repeatDelay) {\n    options.duration = 0;\n    if (options.delay === 0) {\n      shouldSkip = true;\n    }\n  }\n  if (MotionGlobalConfig.instantAnimations || MotionGlobalConfig.skipAnimations) {\n    shouldSkip = true;\n    options.duration = 0;\n    options.delay = 0;\n  }\n  /**\n   * If the transition type or easing has been explicitly set by the user\n   * then we don't want to allow flattening the animation.\n   */\n  options.allowFlatten = !valueTransition.type && !valueTransition.ease;\n  /**\n   * If we can or must skip creating the animation, and apply only\n   * the final keyframe, do so. We also check once keyframes are resolved but\n   * this early check prevents the need to create an animation at all.\n   */\n  if (shouldSkip && !isHandoff && value.get() !== undefined) {\n    const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n    if (finalKeyframe !== undefined) {\n      frame.update(() => {\n        options.onUpdate(finalKeyframe);\n        options.onComplete();\n      });\n      return;\n    }\n  }\n  return valueTransition.isSync ? new JSAnimation(options) : new AsyncMotionValueAnimation(options);\n};\nexport { animateMotionValue };", "map": {"version": 3, "names": ["getValueTransition", "frame", "JSAnimation", "AsyncMotionValueAnimation", "secondsToMilliseconds", "MotionGlobalConfig", "getFinalKeyframe", "getDefaultTransition", "isTransitionDefined", "animateMotionValue", "name", "value", "target", "transition", "element", "<PERSON><PERSON><PERSON><PERSON>", "onComplete", "valueTransition", "delay", "elapsed", "options", "keyframes", "Array", "isArray", "ease", "velocity", "getVelocity", "onUpdate", "v", "set", "motionValue", "undefined", "Object", "assign", "duration", "repeatDelay", "from", "shouldSkip", "type", "instantAnimations", "skipAnimations", "allowFlatten", "get", "finalKeyframe", "update", "isSync"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs"], "sourcesContent": ["import { getValueTransition, frame, JSAnimation, AsyncMotionValueAnimation } from 'motion-dom';\nimport { secondsToMilliseconds, MotionGlobalConfig } from 'motion-utils';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isTransitionDefined } from '../utils/is-transition-defined.mjs';\n\nconst animateMotionValue = (name, value, target, transition = {}, element, isHandoff) => (onComplete) => {\n    const valueTransition = getValueTransition(transition, name) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let { elapsed = 0 } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const options = {\n        keyframes: Array.isArray(target) ? target : [null, target],\n        ease: \"easeOut\",\n        velocity: value.getVelocity(),\n        ...valueTransition,\n        delay: -elapsed,\n        onUpdate: (v) => {\n            value.set(v);\n            valueTransition.onUpdate && valueTransition.onUpdate(v);\n        },\n        onComplete: () => {\n            onComplete();\n            valueTransition.onComplete && valueTransition.onComplete();\n        },\n        name,\n        motionValue: value,\n        element: isHandoff ? undefined : element,\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unique transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n        Object.assign(options, getDefaultTransition(name, options));\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    options.duration && (options.duration = secondsToMilliseconds(options.duration));\n    options.repeatDelay && (options.repeatDelay = secondsToMilliseconds(options.repeatDelay));\n    /**\n     * Support deprecated way to set initial value. Prefer keyframe syntax.\n     */\n    if (options.from !== undefined) {\n        options.keyframes[0] = options.from;\n    }\n    let shouldSkip = false;\n    if (options.type === false ||\n        (options.duration === 0 && !options.repeatDelay)) {\n        options.duration = 0;\n        if (options.delay === 0) {\n            shouldSkip = true;\n        }\n    }\n    if (MotionGlobalConfig.instantAnimations ||\n        MotionGlobalConfig.skipAnimations) {\n        shouldSkip = true;\n        options.duration = 0;\n        options.delay = 0;\n    }\n    /**\n     * If the transition type or easing has been explicitly set by the user\n     * then we don't want to allow flattening the animation.\n     */\n    options.allowFlatten = !valueTransition.type && !valueTransition.ease;\n    /**\n     * If we can or must skip creating the animation, and apply only\n     * the final keyframe, do so. We also check once keyframes are resolved but\n     * this early check prevents the need to create an animation at all.\n     */\n    if (shouldSkip && !isHandoff && value.get() !== undefined) {\n        const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n        if (finalKeyframe !== undefined) {\n            frame.update(() => {\n                options.onUpdate(finalKeyframe);\n                options.onComplete();\n            });\n            return;\n        }\n    }\n    return valueTransition.isSync\n        ? new JSAnimation(options)\n        : new AsyncMotionValueAnimation(options);\n};\n\nexport { animateMotionValue };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,KAAK,EAAEC,WAAW,EAAEC,yBAAyB,QAAQ,YAAY;AAC9F,SAASC,qBAAqB,EAAEC,kBAAkB,QAAQ,cAAc;AACxE,SAASC,gBAAgB,QAAQ,iDAAiD;AAClF,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,mBAAmB,QAAQ,oCAAoC;AAExE,MAAMC,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAEC,OAAO,EAAEC,SAAS,KAAMC,UAAU,IAAK;EACrG,MAAMC,eAAe,GAAGjB,kBAAkB,CAACa,UAAU,EAAEH,IAAI,CAAC,IAAI,CAAC,CAAC;EAClE;AACJ;AACA;AACA;AACA;EACI,MAAMQ,KAAK,GAAGD,eAAe,CAACC,KAAK,IAAIL,UAAU,CAACK,KAAK,IAAI,CAAC;EAC5D;AACJ;AACA;AACA;EACI,IAAI;IAAEC,OAAO,GAAG;EAAE,CAAC,GAAGN,UAAU;EAChCM,OAAO,GAAGA,OAAO,GAAGf,qBAAqB,CAACc,KAAK,CAAC;EAChD,MAAME,OAAO,GAAG;IACZC,SAAS,EAAEC,KAAK,CAACC,OAAO,CAACX,MAAM,CAAC,GAAGA,MAAM,GAAG,CAAC,IAAI,EAAEA,MAAM,CAAC;IAC1DY,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAEd,KAAK,CAACe,WAAW,CAAC,CAAC;IAC7B,GAAGT,eAAe;IAClBC,KAAK,EAAE,CAACC,OAAO;IACfQ,QAAQ,EAAGC,CAAC,IAAK;MACbjB,KAAK,CAACkB,GAAG,CAACD,CAAC,CAAC;MACZX,eAAe,CAACU,QAAQ,IAAIV,eAAe,CAACU,QAAQ,CAACC,CAAC,CAAC;IAC3D,CAAC;IACDZ,UAAU,EAAEA,CAAA,KAAM;MACdA,UAAU,CAAC,CAAC;MACZC,eAAe,CAACD,UAAU,IAAIC,eAAe,CAACD,UAAU,CAAC,CAAC;IAC9D,CAAC;IACDN,IAAI;IACJoB,WAAW,EAAEnB,KAAK;IAClBG,OAAO,EAAEC,SAAS,GAAGgB,SAAS,GAAGjB;EACrC,CAAC;EACD;AACJ;AACA;AACA;EACI,IAAI,CAACN,mBAAmB,CAACS,eAAe,CAAC,EAAE;IACvCe,MAAM,CAACC,MAAM,CAACb,OAAO,EAAEb,oBAAoB,CAACG,IAAI,EAAEU,OAAO,CAAC,CAAC;EAC/D;EACA;AACJ;AACA;AACA;AACA;EACIA,OAAO,CAACc,QAAQ,KAAKd,OAAO,CAACc,QAAQ,GAAG9B,qBAAqB,CAACgB,OAAO,CAACc,QAAQ,CAAC,CAAC;EAChFd,OAAO,CAACe,WAAW,KAAKf,OAAO,CAACe,WAAW,GAAG/B,qBAAqB,CAACgB,OAAO,CAACe,WAAW,CAAC,CAAC;EACzF;AACJ;AACA;EACI,IAAIf,OAAO,CAACgB,IAAI,KAAKL,SAAS,EAAE;IAC5BX,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACgB,IAAI;EACvC;EACA,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIjB,OAAO,CAACkB,IAAI,KAAK,KAAK,IACrBlB,OAAO,CAACc,QAAQ,KAAK,CAAC,IAAI,CAACd,OAAO,CAACe,WAAY,EAAE;IAClDf,OAAO,CAACc,QAAQ,GAAG,CAAC;IACpB,IAAId,OAAO,CAACF,KAAK,KAAK,CAAC,EAAE;MACrBmB,UAAU,GAAG,IAAI;IACrB;EACJ;EACA,IAAIhC,kBAAkB,CAACkC,iBAAiB,IACpClC,kBAAkB,CAACmC,cAAc,EAAE;IACnCH,UAAU,GAAG,IAAI;IACjBjB,OAAO,CAACc,QAAQ,GAAG,CAAC;IACpBd,OAAO,CAACF,KAAK,GAAG,CAAC;EACrB;EACA;AACJ;AACA;AACA;EACIE,OAAO,CAACqB,YAAY,GAAG,CAACxB,eAAe,CAACqB,IAAI,IAAI,CAACrB,eAAe,CAACO,IAAI;EACrE;AACJ;AACA;AACA;AACA;EACI,IAAIa,UAAU,IAAI,CAACtB,SAAS,IAAIJ,KAAK,CAAC+B,GAAG,CAAC,CAAC,KAAKX,SAAS,EAAE;IACvD,MAAMY,aAAa,GAAGrC,gBAAgB,CAACc,OAAO,CAACC,SAAS,EAAEJ,eAAe,CAAC;IAC1E,IAAI0B,aAAa,KAAKZ,SAAS,EAAE;MAC7B9B,KAAK,CAAC2C,MAAM,CAAC,MAAM;QACfxB,OAAO,CAACO,QAAQ,CAACgB,aAAa,CAAC;QAC/BvB,OAAO,CAACJ,UAAU,CAAC,CAAC;MACxB,CAAC,CAAC;MACF;IACJ;EACJ;EACA,OAAOC,eAAe,CAAC4B,MAAM,GACvB,IAAI3C,WAAW,CAACkB,OAAO,CAAC,GACxB,IAAIjB,yBAAyB,CAACiB,OAAO,CAAC;AAChD,CAAC;AAED,SAASX,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}