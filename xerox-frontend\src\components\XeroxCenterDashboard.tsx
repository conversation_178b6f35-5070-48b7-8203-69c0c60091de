import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BarChart3,
  FileText,
  Clock,
  CheckCircle,
  DollarSign,
  Download,
  MessageCircle,
  Info,
  Check,
  Star,
  MapPin,
  TrendingUp,
  Activity,
  Users,
  Printer,
  Eye,
  Send,
  X,
  Plus,
  Filter,
  Search,
  Calendar,
  Settings,
  AlertTriangle,
  ArrowUp,
  ArrowDown,
  Minus,
  ExternalLink,
  RefreshCw,
  Grid,
  List,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../services/api';
import { cn } from '../lib/utils';

interface PrintJob {
  id: number;
  jobNumber: string;
  fileName: string;
  status: string;
  cost?: number;
  estimatedCompletionTime?: string;
  studentName: string;
  studentEmail: string;
  printType: string;
  copies: number;
  colorType: string;
  paperSize: string;
  remarks?: string;
  created: string;
  priority?: 'Low' | 'Normal' | 'High' | 'Urgent';
  fileSize?: number;
  estimatedDuration?: number;
}

const XeroxCenterDashboard: React.FC = () => {
  const { user } = useAuth();
  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);
  const [selectedJob, setSelectedJob] = useState<PrintJob | null>(null);
  const [showQuoteModal, setShowQuoteModal] = useState(false);
  const [quoteData, setQuoteData] = useState({
    cost: '',
    estimatedHours: '',
    notes: ''
  });
  const [filterStatus, setFilterStatus] = useState('all');
  const [showChatModal, setShowChatModal] = useState(false);
  const [messages, setMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [sortBy, setSortBy] = useState<'created' | 'priority' | 'status' | 'cost'>('created');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [showJobDetailsModal, setShowJobDetailsModal] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch print jobs for this xerox center
        const printJobsResponse = await printJobApi.getXeroxCenterJobs();
        setPrintJobs(printJobsResponse.data);
      } catch (error) {
        console.error('Error fetching print jobs:', error);
        setPrintJobs([]);
      }
    };

    fetchData();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchData, 30000);
    setRefreshInterval(interval);

    // Cleanup interval on unmount
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
      clearInterval(interval);
    };
  }, []);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Requested': { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: Clock },
      'UnderReview': { color: 'bg-blue-100 text-blue-800 border-blue-200', icon: Eye },
      'Quoted': { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: DollarSign },
      'WaitingConfirmation': { color: 'bg-orange-100 text-orange-800 border-orange-200', icon: Clock },
      'Confirmed': { color: 'bg-indigo-100 text-indigo-800 border-indigo-200', icon: Check },
      'InProgress': { color: 'bg-purple-100 text-purple-800 border-purple-200', icon: Activity },
      'Completed': { color: 'bg-green-100 text-green-800 border-green-200', icon: CheckCircle },
      'Delivered': { color: 'bg-emerald-100 text-emerald-800 border-emerald-200', icon: CheckCircle },
      'Rejected': { color: 'bg-red-100 text-red-800 border-red-200', icon: X },
      'Cancelled': { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: X }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      color: 'bg-gray-100 text-gray-800 border-gray-200',
      icon: Info
    };

    const IconComponent = config.icon;

    return (
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className={cn(
          "inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border",
          config.color
        )}
      >
        <IconComponent className="w-3 h-3" />
        {status}
      </motion.div>
    );
  };

  const getPriorityBadge = (priority: string = 'Normal') => {
    const priorityConfig = {
      'Low': { color: 'bg-slate-100 text-slate-600 border-slate-200', icon: ArrowDown },
      'Normal': { color: 'bg-blue-100 text-blue-600 border-blue-200', icon: Minus },
      'High': { color: 'bg-orange-100 text-orange-600 border-orange-200', icon: ArrowUp },
      'Urgent': { color: 'bg-red-100 text-red-600 border-red-200', icon: AlertTriangle }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig['Normal'];
    const IconComponent = config.icon;

    return (
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className={cn(
          "inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border",
          config.color
        )}
      >
        <IconComponent className="w-3 h-3" />
        {priority}
      </motion.div>
    );
  };

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const formatFileSize = (bytes: number = 0) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleQuoteSubmit = async () => {
    if (selectedJob) {
      try {
        const estimatedCompletion = new Date();
        estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));

        await printJobApi.setJobQuote(
          selectedJob.id,
          parseFloat(quoteData.cost),
          estimatedCompletion.toISOString(),
          quoteData.notes
        );

        // Refresh print jobs after update
        const printJobsResponse = await printJobApi.getXeroxCenterJobs();
        setPrintJobs(printJobsResponse.data);
      } catch (error) {
        console.error('Error submitting quote:', error);
      }
    }

    setShowQuoteModal(false);
    setSelectedJob(null);
    setQuoteData({ cost: '', estimatedHours: '', notes: '' });
  };

  const handleStatusUpdate = async (jobId: number, newStatus: string) => {
    try {
      await printJobApi.updateJobStatus(jobId, newStatus);

      // Refresh print jobs after update
      const printJobsResponse = await printJobApi.getXeroxCenterJobs();
      setPrintJobs(printJobsResponse.data);
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleDownloadFile = async (jobId: number, fileName: string) => {
    try {
      const response = await fileUploadApi.downloadFile(jobId);

      // Create blob URL and trigger download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
      // You might want to show an error message to the user here
    }
  };

  const handleOpenChat = async (job: PrintJob) => {
    try {
      setSelectedJob(job);
      setShowChatModal(true);

      // Load messages for this job
      const response = await messageApi.getJobMessages(job.id);
      setMessages(response.data);
    } catch (error) {
      console.error('Error loading messages:', error);
      setMessages([]);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedJob) return;

    try {
      const response = await messageApi.sendMessage(selectedJob.id, newMessage.trim());

      // Add the new message to the list
      setMessages(prev => [...prev, response.data]);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const getSortedAndFilteredJobs = () => {
    let filtered = printJobs;

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(job => job.status === filterStatus);
    }

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(job =>
        job.jobNumber.toLowerCase().includes(term) ||
        job.fileName.toLowerCase().includes(term) ||
        job.studentName.toLowerCase().includes(term) ||
        job.studentEmail.toLowerCase().includes(term) ||
        job.printType.toLowerCase().includes(term)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'created':
          aValue = new Date(a.created).getTime();
          bValue = new Date(b.created).getTime();
          break;
        case 'priority':
          const priorityOrder = { 'Urgent': 4, 'High': 3, 'Normal': 2, 'Low': 1 };
          aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 2;
          bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 2;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'cost':
          aValue = a.cost || 0;
          bValue = b.cost || 0;
          break;
        default:
          aValue = a.created;
          bValue = b.created;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  };

  const filteredJobs = getSortedAndFilteredJobs();

  const stats = {
    total: printJobs.length,
    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,
    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,
    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,
    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)
  };

  const { isDarkMode } = useTheme();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
        >
          <div className="space-y-2">
            <motion.h1
              className="text-4xl font-bold bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              Xerox Center Dashboard
            </motion.h1>
            <motion.p
              className="text-slate-600 dark:text-slate-300 text-lg"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              Welcome back, <span className="font-semibold text-purple-600 dark:text-purple-400">{user?.username}</span>!
            </motion.p>
          </div>

          <motion.button
            className={cn(
              "group relative px-6 py-3 rounded-xl font-medium transition-all duration-300",
              "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700",
              "text-white shadow-lg hover:shadow-xl transform hover:scale-105",
              "border border-white/20 backdrop-blur-sm"
            )}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="flex items-center gap-2">
              <RefreshCw className="w-5 h-5" />
              <span>Refresh Data</span>
            </div>
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-600/20 to-blue-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </motion.button>
        </motion.div>

        {/* Statistics Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, staggerChildren: 0.1 }}
        >
          {[
            {
              title: "Total Jobs",
              value: stats.total,
              icon: FileText,
              color: "from-blue-500 to-blue-600",
              bgColor: "bg-blue-50 dark:bg-blue-900/20",
              textColor: "text-blue-600 dark:text-blue-400",
              change: "+12%",
              changeType: "positive"
            },
            {
              title: "Pending",
              value: stats.pending,
              icon: Clock,
              color: "from-orange-500 to-orange-600",
              bgColor: "bg-orange-50 dark:bg-orange-900/20",
              textColor: "text-orange-600 dark:text-orange-400",
              change: "+5%",
              changeType: "positive"
            },
            {
              title: "In Progress",
              value: stats.inProgress,
              icon: Activity,
              color: "from-purple-500 to-purple-600",
              bgColor: "bg-purple-50 dark:bg-purple-900/20",
              textColor: "text-purple-600 dark:text-purple-400",
              change: "+8%",
              changeType: "positive"
            },
            {
              title: "Revenue",
              value: `$${stats.revenue.toFixed(2)}`,
              icon: DollarSign,
              color: "from-green-500 to-green-600",
              bgColor: "bg-green-50 dark:bg-green-900/20",
              textColor: "text-green-600 dark:text-green-400",
              change: "+15%",
              changeType: "positive"
            }
          ].map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ delay: 0.6 + index * 0.1 }}
                whileHover={{ y: -5, scale: 1.02 }}
                className={cn(
                  "relative p-6 rounded-2xl border border-white/20 backdrop-blur-sm",
                  "bg-white/70 dark:bg-slate-800/70 shadow-lg hover:shadow-xl",
                  "transition-all duration-300 group cursor-pointer"
                )}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={cn("p-3 rounded-xl", stat.bgColor)}>
                    <IconComponent className={cn("w-6 h-6", stat.textColor)} />
                  </div>
                  <div className="flex items-center gap-1 text-sm">
                    <TrendingUp className="w-4 h-4 text-green-500" />
                    <span className="text-green-600 font-medium">{stat.change}</span>
                  </div>
                </div>

                <div className="space-y-1">
                  <p className="text-slate-600 dark:text-slate-300 text-sm font-medium">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-slate-900 dark:text-white">
                    {stat.value}
                  </p>
                </div>

                {/* Gradient overlay on hover */}
                <div className={cn(
                  "absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-10",
                  "bg-gradient-to-br transition-opacity duration-300",
                  stat.color
                )} />
              </motion.div>
            );
          })}
        </motion.div>

      {/* Job Queue */}
      <Card>
        <Card.Header>
          <Row className="align-items-center">
            <Col md={4}>
              <h5 className="mb-0">
                <i className="fas fa-tasks me-2"></i>
                Job Queue ({filteredJobs.length})
              </h5>
            </Col>
            <Col md={8}>
              <Row className="g-2">
                <Col md={3}>
                  <Form.Control
                    type="text"
                    placeholder="Search jobs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    size="sm"
                  />
                </Col>
                <Col md={3}>
                  <Form.Select
                    size="sm"
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <option value="all">All Status</option>
                    <option value="Requested">Requested</option>
                    <option value="UnderReview">Under Review</option>
                    <option value="Quoted">Quoted</option>
                    <option value="WaitingConfirmation">Waiting Confirmation</option>
                    <option value="Confirmed">Confirmed</option>
                    <option value="InProgress">In Progress</option>
                    <option value="Completed">Completed</option>
                  </Form.Select>
                </Col>
                <Col md={3}>
                  <Form.Select
                    size="sm"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                  >
                    <option value="created">Sort by Date</option>
                    <option value="priority">Sort by Priority</option>
                    <option value="status">Sort by Status</option>
                    <option value="cost">Sort by Cost</option>
                  </Form.Select>
                </Col>
                <Col md={3}>
                  <ButtonGroup size="sm">
                    <Button
                      variant={sortOrder === 'desc' ? 'primary' : 'outline-primary'}
                      onClick={() => setSortOrder('desc')}
                    >
                      <i className="fas fa-sort-amount-down"></i>
                    </Button>
                    <Button
                      variant={sortOrder === 'asc' ? 'primary' : 'outline-primary'}
                      onClick={() => setSortOrder('asc')}
                    >
                      <i className="fas fa-sort-amount-up"></i>
                    </Button>
                    <Button
                      variant={viewMode === 'table' ? 'primary' : 'outline-primary'}
                      onClick={() => setViewMode('table')}
                    >
                      <i className="fas fa-table"></i>
                    </Button>
                    <Button
                      variant={viewMode === 'cards' ? 'primary' : 'outline-primary'}
                      onClick={() => setViewMode('cards')}
                    >
                      <i className="fas fa-th-large"></i>
                    </Button>
                  </ButtonGroup>
                </Col>
              </Row>
            </Col>
          </Row>
        </Card.Header>
        <Card.Body>
          {filteredJobs.length > 0 ? (
            viewMode === 'table' ? (
              <Table responsive hover className="job-table">
                <thead>
                  <tr>
                    <th>Job #</th>
                    <th>Priority</th>
                    <th>Student</th>
                    <th>File Details</th>
                    <th>Print Specs</th>
                    <th>Status</th>
                    <th>Cost</th>
                    <th>Time</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredJobs.map(job => (
                    <tr key={job.id}>
                      <td>
                        <strong>{job.jobNumber}</strong>
                        <br />
                        <small className="text-muted">
                          {getTimeAgo(job.created)}
                        </small>
                      </td>
                      <td>
                        {getPriorityBadge(job.priority)}
                      </td>
                      <td>
                        <div>
                          <strong>{job.studentName}</strong>
                          <br />
                          <small className="text-muted">{job.studentEmail}</small>
                        </div>
                      </td>
                      <td>
                        <div>
                          <i className="fas fa-file-pdf me-2 text-danger"></i>
                          <strong>{job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName}</strong>
                          <br />
                          <small className="text-muted">
                            {job.fileSize ? formatFileSize(job.fileSize) : 'Unknown size'}
                          </small>
                        </div>
                      </td>
                      <td>
                        <div className="small">
                          <div><strong>Type:</strong> {job.printType}</div>
                          <div><strong>Copies:</strong> {job.copies}</div>
                          <div><strong>Color:</strong> {job.colorType}</div>
                          <div><strong>Size:</strong> {job.paperSize}</div>
                        </div>
                      </td>
                      <td>{getStatusBadge(job.status)}</td>
                      <td>
                        {job.cost ? `$${job.cost.toFixed(2)}` : '-'}
                      </td>
                      <td>
                        <div className="small">
                          <div><strong>Created:</strong></div>
                          <div>{new Date(job.created).toLocaleDateString()}</div>
                          {job.estimatedCompletionTime && (
                            <>
                              <div><strong>ETA:</strong></div>
                              <div>{new Date(job.estimatedCompletionTime).toLocaleDateString()}</div>
                            </>
                          )}
                        </div>
                      </td>
                    <td>
                      <div className="btn-group-vertical" role="group">
                        {/* Download button - always available */}
                        <Button
                          variant="outline-info"
                          size="sm"
                          onClick={() => handleDownloadFile(job.id, job.fileName)}
                          title="Download File"
                        >
                          <i className="fas fa-download me-1"></i>
                          Download
                        </Button>

                        {job.status === 'Requested' && (
                          <>
                            <Button
                              variant="outline-primary"
                              size="sm"
                              onClick={() => {
                                setSelectedJob(job);
                                setShowQuoteModal(true);
                              }}
                            >
                              <i className="fas fa-dollar-sign me-1"></i>
                              Quote
                            </Button>
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleStatusUpdate(job.id, 'Rejected')}
                            >
                              <i className="fas fa-times me-1"></i>
                              Reject
                            </Button>
                          </>
                        )}

                        {job.status === 'Confirmed' && (
                          <Button
                            variant="outline-info"
                            size="sm"
                            onClick={() => handleStatusUpdate(job.id, 'InProgress')}
                          >
                            <i className="fas fa-play me-1"></i>
                            Start
                          </Button>
                        )}

                        {job.status === 'InProgress' && (
                          <Button
                            variant="outline-success"
                            size="sm"
                            onClick={() => handleStatusUpdate(job.id, 'Completed')}
                          >
                            <i className="fas fa-check me-1"></i>
                            Complete
                          </Button>
                        )}

                        {job.status === 'Completed' && (
                          <Button
                            variant="outline-success"
                            size="sm"
                            onClick={() => handleStatusUpdate(job.id, 'Delivered')}
                          >
                            <i className="fas fa-truck me-1"></i>
                            Deliver
                          </Button>
                        )}

                        <Button
                          variant="outline-secondary"
                          size="sm"
                          onClick={() => handleOpenChat(job)}
                          title="Chat"
                        >
                          <i className="fas fa-comment"></i>
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
              </Table>
            ) : (
              // Card View
              <Row>
                {filteredJobs.map(job => (
                  <Col key={job.id} md={6} lg={4} className="mb-3">
                    <Card className={`job-card h-100 shadow-sm ${job.priority === 'Urgent' ? 'priority-urgent' : ''} priority-${job.priority?.toLowerCase() || 'normal'}`}>
                      <Card.Header className="d-flex justify-content-between align-items-center">
                        <div>
                          <strong>{job.jobNumber}</strong>
                          <br />
                          {getPriorityBadge(job.priority)}
                        </div>
                        {getStatusBadge(job.status)}
                      </Card.Header>
                      <Card.Body>
                        <div className="mb-2">
                          <strong>{job.studentName}</strong>
                          <br />
                          <small className="text-muted">{job.studentEmail}</small>
                        </div>
                        <div className="mb-2">
                          <i className="fas fa-file-pdf me-2 text-danger"></i>
                          <small>{job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName}</small>
                        </div>
                        <div className="mb-2 small">
                          <div><strong>Type:</strong> {job.printType}</div>
                          <div><strong>Copies:</strong> {job.copies}</div>
                          <div><strong>Color:</strong> {job.colorType}</div>
                          <div><strong>Size:</strong> {job.paperSize}</div>
                        </div>
                        {job.cost && (
                          <div className="mb-2">
                            <strong className="text-success">${job.cost.toFixed(2)}</strong>
                          </div>
                        )}
                        <div className="small text-muted">
                          {getTimeAgo(job.created)}
                        </div>
                      </Card.Body>
                      <Card.Footer>
                        <div className="action-buttons d-flex flex-wrap gap-1">
                          <Button
                            variant="outline-info"
                            size="sm"
                            onClick={() => handleDownloadFile(job.id, job.fileName)}
                            title="Download File"
                          >
                            <i className="fas fa-download"></i>
                          </Button>

                          {job.status === 'Requested' && (
                            <>
                              <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={() => {
                                  setSelectedJob(job);
                                  setShowQuoteModal(true);
                                }}
                                title="Quote"
                              >
                                <i className="fas fa-dollar-sign"></i>
                              </Button>
                              <Button
                                variant="outline-danger"
                                size="sm"
                                onClick={() => handleStatusUpdate(job.id, 'Rejected')}
                                title="Reject"
                              >
                                <i className="fas fa-times"></i>
                              </Button>
                            </>
                          )}

                          {job.status === 'Confirmed' && (
                            <Button
                              variant="outline-info"
                              size="sm"
                              onClick={() => handleStatusUpdate(job.id, 'InProgress')}
                              title="Start"
                            >
                              <i className="fas fa-play"></i>
                            </Button>
                          )}

                          {job.status === 'InProgress' && (
                            <Button
                              variant="outline-success"
                              size="sm"
                              onClick={() => handleStatusUpdate(job.id, 'Completed')}
                              title="Complete"
                            >
                              <i className="fas fa-check"></i>
                            </Button>
                          )}

                          {job.status === 'Completed' && (
                            <Button
                              variant="outline-success"
                              size="sm"
                              onClick={() => handleStatusUpdate(job.id, 'Delivered')}
                              title="Deliver"
                            >
                              <i className="fas fa-truck"></i>
                            </Button>
                          )}

                          <Button
                            variant="outline-secondary"
                            size="sm"
                            onClick={() => handleOpenChat(job)}
                            title="Chat"
                          >
                            <i className="fas fa-comment"></i>
                          </Button>

                          <Button
                            variant="outline-dark"
                            size="sm"
                            onClick={() => {
                              setSelectedJob(job);
                              setShowJobDetailsModal(true);
                            }}
                            title="View Details"
                          >
                            <i className="fas fa-eye"></i>
                          </Button>
                        </div>
                      </Card.Footer>
                    </Card>
                  </Col>
                ))}
              </Row>
            )
          ) : (
            <Alert variant="info">
              <i className="fas fa-info-circle me-2"></i>
              No jobs found for the selected filter.
            </Alert>
          )}
        </Card.Body>
      </Card>

      {/* Quote Modal */}
      <Modal show={showQuoteModal} onHide={() => setShowQuoteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-dollar-sign me-2"></i>
            Provide Quote - {selectedJob?.jobNumber}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedJob && (
            <>
              <div className="mb-3 p-3 bg-light rounded">
                <h6>Job Details:</h6>
                <div className="row">
                  <div className="col-6">
                    <strong>File:</strong> {selectedJob.fileName}<br />
                    <strong>Type:</strong> {selectedJob.printType}<br />
                    <strong>Copies:</strong> {selectedJob.copies}
                  </div>
                  <div className="col-6">
                    <strong>Color:</strong> {selectedJob.colorType}<br />
                    <strong>Size:</strong> {selectedJob.paperSize}<br />
                    <strong>Student:</strong> {selectedJob.studentName}
                  </div>
                </div>
                {selectedJob.remarks && (
                  <div className="mt-2">
                    <strong>Remarks:</strong> {selectedJob.remarks}
                  </div>
                )}
              </div>

              <Form>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Cost ($)</Form.Label>
                      <InputGroup>
                        <InputGroup.Text>$</InputGroup.Text>
                        <Form.Control
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          value={quoteData.cost}
                          onChange={(e) => setQuoteData(prev => ({ ...prev, cost: e.target.value }))}
                          required
                        />
                      </InputGroup>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Estimated Hours</Form.Label>
                      <Form.Control
                        type="number"
                        min="1"
                        placeholder="Hours to complete"
                        value={quoteData.estimatedHours}
                        onChange={(e) => setQuoteData(prev => ({ ...prev, estimatedHours: e.target.value }))}
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-3">
                  <Form.Label>Notes (Optional)</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    placeholder="Any additional notes for the student..."
                    value={quoteData.notes}
                    onChange={(e) => setQuoteData(prev => ({ ...prev, notes: e.target.value }))}
                  />
                </Form.Group>
              </Form>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowQuoteModal(false)}>
            Cancel
          </Button>
          <Button 
            variant="primary" 
            onClick={handleQuoteSubmit}
            disabled={!quoteData.cost || !quoteData.estimatedHours}
          >
            <i className="fas fa-paper-plane me-2"></i>
            Send Quote
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Chat Modal */}
      <Modal show={showChatModal} onHide={() => setShowChatModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-comment me-2"></i>
            Chat - {selectedJob?.jobNumber}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div style={{ height: '400px', overflowY: 'auto', border: '1px solid #dee2e6', borderRadius: '0.375rem', padding: '1rem', marginBottom: '1rem' }}>
            {messages.length > 0 ? (
              messages.map((message) => (
                <div key={message.id} className={`mb-3 ${message.isFromCurrentUser ? 'text-end' : 'text-start'}`}>
                  <div className={`d-inline-block p-2 rounded ${message.isFromCurrentUser ? 'bg-primary text-white' : 'bg-light'}`} style={{ maxWidth: '70%' }}>
                    <div>{message.content}</div>
                    <small className={`d-block mt-1 ${message.isFromCurrentUser ? 'text-light' : 'text-muted'}`}>
                      {message.senderName} - {new Date(message.sentAt).toLocaleString()}
                    </small>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-muted">
                <i className="fas fa-comments fa-3x mb-3"></i>
                <p>No messages yet. Start a conversation!</p>
              </div>
            )}
          </div>
          <div className="d-flex">
            <Form.Control
              type="text"
              placeholder="Type your message..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
              className="me-2"
            />
            <Button variant="primary" onClick={handleSendMessage} disabled={!newMessage.trim()}>
              <i className="fas fa-paper-plane"></i>
            </Button>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowChatModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Job Details Modal */}
      <Modal show={showJobDetailsModal} onHide={() => setShowJobDetailsModal(false)} size="lg" className="job-details-modal">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-info-circle me-2"></i>
            Job Details - {selectedJob?.jobNumber}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedJob && (
            <Row>
              <Col md={6}>
                <Card className="mb-3">
                  <Card.Header>
                    <h6 className="mb-0">
                      <i className="fas fa-user me-2"></i>
                      Student Information
                    </h6>
                  </Card.Header>
                  <Card.Body>
                    <div><strong>Name:</strong> {selectedJob.studentName}</div>
                    <div><strong>Email:</strong> {selectedJob.studentEmail}</div>
                  </Card.Body>
                </Card>

                <Card className="mb-3">
                  <Card.Header>
                    <h6 className="mb-0">
                      <i className="fas fa-file me-2"></i>
                      File Information
                    </h6>
                  </Card.Header>
                  <Card.Body>
                    <div><strong>File Name:</strong> {selectedJob.fileName}</div>
                    {selectedJob.fileSize && (
                      <div><strong>File Size:</strong> {formatFileSize(selectedJob.fileSize)}</div>
                    )}
                    <div className="mt-2">
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => handleDownloadFile(selectedJob.id, selectedJob.fileName)}
                      >
                        <i className="fas fa-download me-2"></i>
                        Download File
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>

              <Col md={6}>
                <Card className="mb-3">
                  <Card.Header>
                    <h6 className="mb-0">
                      <i className="fas fa-print me-2"></i>
                      Print Specifications
                    </h6>
                  </Card.Header>
                  <Card.Body>
                    <div><strong>Print Type:</strong> {selectedJob.printType}</div>
                    <div><strong>Copies:</strong> {selectedJob.copies}</div>
                    <div><strong>Color Type:</strong> {selectedJob.colorType}</div>
                    <div><strong>Paper Size:</strong> {selectedJob.paperSize}</div>
                    {selectedJob.remarks && (
                      <div className="mt-2">
                        <strong>Remarks:</strong>
                        <div className="p-2 bg-light rounded mt-1">
                          {selectedJob.remarks}
                        </div>
                      </div>
                    )}
                  </Card.Body>
                </Card>

                <Card className="mb-3">
                  <Card.Header>
                    <h6 className="mb-0">
                      <i className="fas fa-info me-2"></i>
                      Job Status
                    </h6>
                  </Card.Header>
                  <Card.Body>
                    <div className="mb-2">
                      <strong>Status:</strong> {getStatusBadge(selectedJob.status)}
                    </div>
                    <div className="mb-2">
                      <strong>Priority:</strong> {getPriorityBadge(selectedJob.priority)}
                    </div>
                    {selectedJob.cost && (
                      <div className="mb-2">
                        <strong>Cost:</strong> <span className="text-success">${selectedJob.cost.toFixed(2)}</span>
                      </div>
                    )}
                    <div className="mb-2">
                      <strong>Created:</strong> {new Date(selectedJob.created).toLocaleString()}
                    </div>
                    {selectedJob.estimatedCompletionTime && (
                      <div>
                        <strong>Estimated Completion:</strong> {new Date(selectedJob.estimatedCompletionTime).toLocaleString()}
                      </div>
                    )}
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowJobDetailsModal(false)}>
            Close
          </Button>
          {selectedJob && (
            <Button
              variant="primary"
              onClick={() => {
                setShowJobDetailsModal(false);
                handleOpenChat(selectedJob);
              }}
            >
              <i className="fas fa-comment me-2"></i>
              Start Chat
            </Button>
          )}
        </Modal.Footer>
      </Modal>
      </Container>
    </div>
  );
};

export default XeroxCenterDashboard;
