{"ast": null, "code": "\"use client\";\n\nimport { jsx } from 'react/jsx-runtime';\nimport { isHTMLElement } from 'motion-dom';\nimport * as React from 'react';\nimport { useId, useRef, useContext, useInsertionEffect } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends React.Component {\n  getSnapshotBeforeUpdate(prevProps) {\n    const element = this.props.childRef.current;\n    if (element && prevProps.isPresent && !this.props.isPresent) {\n      const parent = element.offsetParent;\n      const parentWidth = isHTMLElement(parent) ? parent.offsetWidth || 0 : 0;\n      const size = this.props.sizeRef.current;\n      size.height = element.offsetHeight || 0;\n      size.width = element.offsetWidth || 0;\n      size.top = element.offsetTop;\n      size.left = element.offsetLeft;\n      size.right = parentWidth - size.width - size.left;\n    }\n    return null;\n  }\n  /**\n   * Required with getSnapshotBeforeUpdate to stop React complaining.\n   */\n  componentDidUpdate() {}\n  render() {\n    return this.props.children;\n  }\n}\nfunction PopChild({\n  children,\n  isPresent,\n  anchorX\n}) {\n  const id = useId();\n  const ref = useRef(null);\n  const size = useRef({\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0,\n    right: 0\n  });\n  const {\n    nonce\n  } = useContext(MotionConfigContext);\n  /**\n   * We create and inject a style block so we can apply this explicit\n   * sizing in a non-destructive manner by just deleting the style block.\n   *\n   * We can't apply size via render as the measurement happens\n   * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n   * styles directly on the DOM node, we might be overwriting\n   * styles set via the style prop.\n   */\n  useInsertionEffect(() => {\n    const {\n      width,\n      height,\n      top,\n      left,\n      right\n    } = size.current;\n    if (isPresent || !ref.current || !width || !height) return;\n    const x = anchorX === \"left\" ? `left: ${left}` : `right: ${right}`;\n    ref.current.dataset.motionPopId = id;\n    const style = document.createElement(\"style\");\n    if (nonce) style.nonce = nonce;\n    document.head.appendChild(style);\n    if (style.sheet) {\n      style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            ${x}px !important;\n            top: ${top}px !important;\n          }\n        `);\n    }\n    return () => {\n      if (document.head.contains(style)) {\n        document.head.removeChild(style);\n      }\n    };\n  }, [isPresent]);\n  return jsx(PopChildMeasure, {\n    isPresent: isPresent,\n    childRef: ref,\n    sizeRef: size,\n    children: React.cloneElement(children, {\n      ref\n    })\n  });\n}\nexport { PopChild };", "map": {"version": 3, "names": ["jsx", "isHTMLElement", "React", "useId", "useRef", "useContext", "useInsertionEffect", "MotionConfigContext", "PopChildMeasure", "Component", "getSnapshotBeforeUpdate", "prevProps", "element", "props", "childRef", "current", "isPresent", "parent", "offsetParent", "parentWidth", "offsetWidth", "size", "sizeRef", "height", "offsetHeight", "width", "top", "offsetTop", "left", "offsetLeft", "right", "componentDidUpdate", "render", "children", "PopChild", "anchorX", "id", "ref", "nonce", "x", "dataset", "motionPopId", "style", "document", "createElement", "head", "append<PERSON><PERSON><PERSON>", "sheet", "insertRule", "contains", "<PERSON><PERSON><PERSON><PERSON>", "cloneElement"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { isHTMLElement } from 'motion-dom';\nimport * as React from 'react';\nimport { useId, useRef, useContext, useInsertionEffect } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends React.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const parent = element.offsetParent;\n            const parentWidth = isHTMLElement(parent)\n                ? parent.offsetWidth || 0\n                : 0;\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n            size.right = parentWidth - size.width - size.left;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */\n    componentDidUpdate() { }\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild({ children, isPresent, anchorX }) {\n    const id = useId();\n    const ref = useRef(null);\n    const size = useRef({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        right: 0,\n    });\n    const { nonce } = useContext(MotionConfigContext);\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */\n    useInsertionEffect(() => {\n        const { width, height, top, left, right } = size.current;\n        if (isPresent || !ref.current || !width || !height)\n            return;\n        const x = anchorX === \"left\" ? `left: ${left}` : `right: ${right}`;\n        ref.current.dataset.motionPopId = id;\n        const style = document.createElement(\"style\");\n        if (nonce)\n            style.nonce = nonce;\n        document.head.appendChild(style);\n        if (style.sheet) {\n            style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            ${x}px !important;\n            top: ${top}px !important;\n          }\n        `);\n        }\n        return () => {\n            if (document.head.contains(style)) {\n                document.head.removeChild(style);\n            }\n        };\n    }, [isPresent]);\n    return (jsx(PopChildMeasure, { isPresent: isPresent, childRef: ref, sizeRef: size, children: React.cloneElement(children, { ref }) }));\n}\n\nexport { PopChild };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,aAAa,QAAQ,YAAY;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,kBAAkB,QAAQ,OAAO;AACrE,SAASC,mBAAmB,QAAQ,uCAAuC;;AAE3E;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASN,KAAK,CAACO,SAAS,CAAC;EAC1CC,uBAAuBA,CAACC,SAAS,EAAE;IAC/B,MAAMC,OAAO,GAAG,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,OAAO;IAC3C,IAAIH,OAAO,IAAID,SAAS,CAACK,SAAS,IAAI,CAAC,IAAI,CAACH,KAAK,CAACG,SAAS,EAAE;MACzD,MAAMC,MAAM,GAAGL,OAAO,CAACM,YAAY;MACnC,MAAMC,WAAW,GAAGlB,aAAa,CAACgB,MAAM,CAAC,GACnCA,MAAM,CAACG,WAAW,IAAI,CAAC,GACvB,CAAC;MACP,MAAMC,IAAI,GAAG,IAAI,CAACR,KAAK,CAACS,OAAO,CAACP,OAAO;MACvCM,IAAI,CAACE,MAAM,GAAGX,OAAO,CAACY,YAAY,IAAI,CAAC;MACvCH,IAAI,CAACI,KAAK,GAAGb,OAAO,CAACQ,WAAW,IAAI,CAAC;MACrCC,IAAI,CAACK,GAAG,GAAGd,OAAO,CAACe,SAAS;MAC5BN,IAAI,CAACO,IAAI,GAAGhB,OAAO,CAACiB,UAAU;MAC9BR,IAAI,CAACS,KAAK,GAAGX,WAAW,GAAGE,IAAI,CAACI,KAAK,GAAGJ,IAAI,CAACO,IAAI;IACrD;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIG,kBAAkBA,CAAA,EAAG,CAAE;EACvBC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACnB,KAAK,CAACoB,QAAQ;EAC9B;AACJ;AACA,SAASC,QAAQA,CAAC;EAAED,QAAQ;EAAEjB,SAAS;EAAEmB;AAAQ,CAAC,EAAE;EAChD,MAAMC,EAAE,GAAGjC,KAAK,CAAC,CAAC;EAClB,MAAMkC,GAAG,GAAGjC,MAAM,CAAC,IAAI,CAAC;EACxB,MAAMiB,IAAI,GAAGjB,MAAM,CAAC;IAChBqB,KAAK,EAAE,CAAC;IACRF,MAAM,EAAE,CAAC;IACTG,GAAG,EAAE,CAAC;IACNE,IAAI,EAAE,CAAC;IACPE,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAM;IAAEQ;EAAM,CAAC,GAAGjC,UAAU,CAACE,mBAAmB,CAAC;EACjD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACID,kBAAkB,CAAC,MAAM;IACrB,MAAM;MAAEmB,KAAK;MAAEF,MAAM;MAAEG,GAAG;MAAEE,IAAI;MAAEE;IAAM,CAAC,GAAGT,IAAI,CAACN,OAAO;IACxD,IAAIC,SAAS,IAAI,CAACqB,GAAG,CAACtB,OAAO,IAAI,CAACU,KAAK,IAAI,CAACF,MAAM,EAC9C;IACJ,MAAMgB,CAAC,GAAGJ,OAAO,KAAK,MAAM,GAAG,SAASP,IAAI,EAAE,GAAG,UAAUE,KAAK,EAAE;IAClEO,GAAG,CAACtB,OAAO,CAACyB,OAAO,CAACC,WAAW,GAAGL,EAAE;IACpC,MAAMM,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7C,IAAIN,KAAK,EACLI,KAAK,CAACJ,KAAK,GAAGA,KAAK;IACvBK,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,KAAK,CAAC;IAChC,IAAIA,KAAK,CAACK,KAAK,EAAE;MACbL,KAAK,CAACK,KAAK,CAACC,UAAU,CAAC;AACnC,iCAAiCZ,EAAE;AACnC;AACA,qBAAqBX,KAAK;AAC1B,sBAAsBF,MAAM;AAC5B,cAAcgB,CAAC;AACf,mBAAmBb,GAAG;AACtB;AACA,SAAS,CAAC;IACF;IACA,OAAO,MAAM;MACT,IAAIiB,QAAQ,CAACE,IAAI,CAACI,QAAQ,CAACP,KAAK,CAAC,EAAE;QAC/BC,QAAQ,CAACE,IAAI,CAACK,WAAW,CAACR,KAAK,CAAC;MACpC;IACJ,CAAC;EACL,CAAC,EAAE,CAAC1B,SAAS,CAAC,CAAC;EACf,OAAQhB,GAAG,CAACQ,eAAe,EAAE;IAAEQ,SAAS,EAAEA,SAAS;IAAEF,QAAQ,EAAEuB,GAAG;IAAEf,OAAO,EAAED,IAAI;IAAEY,QAAQ,EAAE/B,KAAK,CAACiD,YAAY,CAAClB,QAAQ,EAAE;MAAEI;IAAI,CAAC;EAAE,CAAC,CAAC;AACzI;AAEA,SAASH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}