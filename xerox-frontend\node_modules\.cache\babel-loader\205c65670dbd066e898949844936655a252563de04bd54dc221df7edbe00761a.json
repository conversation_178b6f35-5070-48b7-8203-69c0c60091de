{"ast": null, "code": "import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}", "map": {"version": 3, "names": ["clsx", "twMerge", "cn", "inputs"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "mappings": "AAAA,SAA0BA,IAAI,QAAQ,MAAM;AAC5C,SAASC,OAAO,QAAQ,gBAAgB;AAExC,OAAO,SAASC,EAAEA,CAAC,GAAGC,MAAoB,EAAE;EAC1C,OAAOF,OAAO,CAACD,IAAI,CAACG,MAAM,CAAC,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}