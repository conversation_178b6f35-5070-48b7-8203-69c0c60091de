{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\AceternityXeroxDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Modal } from 'react-bootstrap';\nimport { motion } from 'framer-motion';\nimport { FileText, Clock, Cog, DollarSign, Download, MessageCircle, Eye, Play, CheckCircle, Truck, X, Search, Grid3X3, List, Activity, RefreshCw } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { printJobApi, fileUploadApi } from '../../services/api';\n// import { AceternityCard, AceternityStatsCard, AceternityButton, AceternityBadge, AceternityInput } from './AceternityCard';\nimport { cn } from '../../lib/utils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AceternityXeroxDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [jobs, setJobs] = useState([]);\n  const [filteredJobs, setFilteredJobs] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('All');\n  const [sortBy, setSortBy] = useState('created');\n  const [sortDirection, setSortDirection] = useState('desc');\n  const [viewMode, setViewMode] = useState('cards');\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [showJobDetailsModal, setShowJobDetailsModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [quoteAmount, setQuoteAmount] = useState('');\n  const [estimatedTime, setEstimatedTime] = useState('');\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const [stats, setStats] = useState({\n    total: 0,\n    pending: 0,\n    inProgress: 0,\n    completed: 0,\n    revenue: 0\n  });\n  useEffect(() => {\n    fetchJobs();\n    const interval = setInterval(fetchJobs, 30000); // Auto-refresh every 30 seconds\n    return () => clearInterval(interval);\n  }, []);\n  useEffect(() => {\n    filterAndSortJobs();\n  }, [jobs, searchTerm, statusFilter, sortBy, sortDirection]);\n  const fetchJobs = async () => {\n    setIsRefreshing(true);\n    try {\n      const response = await printJobApi.getXeroxCenterJobs();\n      setJobs(response.data);\n\n      // Calculate stats\n      const totalJobs = response.data.length;\n      const pendingJobs = response.data.filter(job => ['Requested', 'UnderReview'].includes(job.status)).length;\n      const inProgressJobs = response.data.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length;\n      const completedJobs = response.data.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n      const totalRevenue = response.data.reduce((sum, job) => sum + (job.cost || 0), 0);\n      setStats({\n        total: totalJobs,\n        pending: pendingJobs,\n        inProgress: inProgressJobs,\n        completed: completedJobs,\n        revenue: totalRevenue\n      });\n    } catch (error) {\n      console.error('Error fetching jobs:', error);\n      setJobs([]);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n  const filterAndSortJobs = () => {\n    let filtered = jobs.filter(job => {\n      const matchesSearch = searchTerm === '' || job.jobNumber.toLowerCase().includes(searchTerm.toLowerCase()) || job.fileName.toLowerCase().includes(searchTerm.toLowerCase()) || job.studentName.toLowerCase().includes(searchTerm.toLowerCase()) || job.studentEmail.toLowerCase().includes(searchTerm.toLowerCase()) || job.printType.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesStatus = statusFilter === 'All' || job.status === statusFilter;\n      return matchesSearch && matchesStatus;\n    });\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n      if (sortBy === 'created') {\n        aValue = new Date(a.created).getTime();\n        bValue = new Date(b.created).getTime();\n      }\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        return sortDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);\n      }\n      if (typeof aValue === 'number' && typeof bValue === 'number') {\n        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;\n      }\n      return 0;\n    });\n    setFilteredJobs(filtered);\n  };\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        variant: 'default'\n      },\n      'UnderReview': {\n        variant: 'info'\n      },\n      'Quoted': {\n        variant: 'warning'\n      },\n      'WaitingConfirmation': {\n        variant: 'warning'\n      },\n      'Confirmed': {\n        variant: 'info'\n      },\n      'InProgress': {\n        variant: 'info'\n      },\n      'Completed': {\n        variant: 'success'\n      },\n      'Delivered': {\n        variant: 'success'\n      },\n      'Rejected': {\n        variant: 'error'\n      },\n      'Cancelled': {\n        variant: 'default'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'default'\n    };\n    return /*#__PURE__*/_jsxDEV(AceternityBadge, {\n      variant: config.variant,\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this);\n  };\n  const getPriorityBadge = priority => {\n    const priorityConfig = {\n      'Low': {\n        variant: 'success'\n      },\n      'Normal': {\n        variant: 'default'\n      },\n      'High': {\n        variant: 'warning'\n      },\n      'Urgent': {\n        variant: 'error'\n      }\n    };\n    const config = priorityConfig[priority] || {\n      variant: 'default'\n    };\n    return /*#__PURE__*/_jsxDEV(AceternityBadge, {\n      variant: config.variant,\n      size: \"sm\",\n      children: priority\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this);\n  };\n  const handleStatusUpdate = async (jobId, newStatus) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n      fetchJobs();\n    } catch (error) {\n      console.error('Error updating job status:', error);\n    }\n  };\n  const handleDownloadFile = async (jobId, fileName) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n    }\n  };\n  const handleOpenChat = async job => {\n    // Implementation for opening chat\n    console.log('Opening chat for job:', job.id);\n  };\n  const handleSubmitQuote = async () => {\n    if (!selectedJob || !quoteAmount) return;\n    try {\n      await printJobApi.submitQuote(selectedJob.id, {\n        amount: parseFloat(quoteAmount),\n        estimatedCompletionTime: estimatedTime\n      });\n      setShowQuoteModal(false);\n      setQuoteAmount('');\n      setEstimatedTime('');\n      setSelectedJob(null);\n      fetchJobs();\n    } catch (error) {\n      console.error('Error submitting quote:', error);\n    }\n  };\n  const getTimeAgo = dateString => {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n    return `${Math.floor(diffInMinutes / 1440)}d ago`;\n  };\n  const formatFileSize = bytes => {\n    if (!bytes) return 'Unknown';\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      className: \"py-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2\",\n          children: \"Xerox Center Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 text-lg mb-6\",\n          children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: user === null || user === void 0 ? void 0 : user.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 27\n          }, this), \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: /*#__PURE__*/_jsxDEV(AceternityButton, {\n              onClick: fetchJobs,\n              variant: \"outline\",\n              disabled: isRefreshing,\n              className: \"px-6\",\n              children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: cn(\"w-4 h-4 mr-2\", isRefreshing && \"animate-spin\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), \"Refresh\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AceternityBadge, {\n            variant: \"info\",\n            children: [\"Last updated: \", new Date().toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-6 g-4\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"Total Jobs\",\n            value: stats.total,\n            icon: /*#__PURE__*/_jsxDEV(FileText, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 21\n            }, this),\n            color: \"blue\",\n            trend: {\n              value: 12,\n              isPositive: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"Pending Jobs\",\n            value: stats.pending,\n            icon: /*#__PURE__*/_jsxDEV(Clock, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 21\n            }, this),\n            color: \"orange\",\n            trend: {\n              value: 5,\n              isPositive: false\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"In Progress\",\n            value: stats.inProgress,\n            icon: /*#__PURE__*/_jsxDEV(Cog, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 21\n            }, this),\n            color: \"purple\",\n            trend: {\n              value: 8,\n              isPositive: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(AceternityStatsCard, {\n            title: \"Revenue\",\n            value: `$${stats.revenue.toFixed(2)}`,\n            icon: /*#__PURE__*/_jsxDEV(DollarSign, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 21\n            }, this),\n            color: \"green\",\n            trend: {\n              value: 15,\n              isPositive: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AceternityCard, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n            children: [/*#__PURE__*/_jsxDEV(Activity, {\n              className: \"w-5 h-5 inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), \"Job Queue\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(AceternityBadge, {\n              variant: \"info\",\n              children: [filteredJobs.length, \" jobs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex rounded-lg bg-white/10 dark:bg-black/20 p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('cards'),\n                className: cn(\"p-2 rounded-md transition-all duration-200\", viewMode === 'cards' ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white\" : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"),\n                children: /*#__PURE__*/_jsxDEV(Grid3X3, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('table'),\n                className: cn(\"p-2 rounded-md transition-all duration-200\", viewMode === 'table' ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white\" : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"),\n                children: /*#__PURE__*/_jsxDEV(List, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(AceternityInput, {\n            placeholder: \"Search jobs...\",\n            value: searchTerm,\n            onChange: setSearchTerm,\n            icon: /*#__PURE__*/_jsxDEV(Search, {\n              className: \"w-4 h-4 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              value: statusFilter,\n              onChange: e => setStatusFilter(e.target.value),\n              className: \"w-full px-4 py-3 rounded-xl border-0 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 bg-white/50 dark:bg-black/20 backdrop-blur-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"All\",\n                children: \"All Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Requested\",\n                children: \"Requested\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"UnderReview\",\n                children: \"Under Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Quoted\",\n                children: \"Quoted\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Confirmed\",\n                children: \"Confirmed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"InProgress\",\n                children: \"In Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Completed\",\n                children: \"Completed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Delivered\",\n                children: \"Delivered\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              className: \"w-full px-4 py-3 rounded-xl border-0 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 bg-white/50 dark:bg-black/20 backdrop-blur-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"created\",\n                children: \"Sort by Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"priority\",\n                children: \"Sort by Priority\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"status\",\n                children: \"Sort by Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"cost\",\n                children: \"Sort by Cost\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AceternityButton, {\n            onClick: () => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc'),\n            variant: \"outline\",\n            className: \"w-full\",\n            children: [sortDirection === 'asc' ? '↑' : '↓', \" \", sortDirection === 'asc' ? 'Ascending' : 'Descending']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), filteredJobs.length > 0 ? viewMode === 'cards' ? /*#__PURE__*/_jsxDEV(Row, {\n          className: \"g-4\",\n          children: filteredJobs.map((job, index) => /*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: index * 0.05\n              },\n              className: cn(\"p-4 rounded-xl border transition-all duration-200 hover:shadow-lg\", \"bg-white/50 dark:bg-black/20 border-white/20 dark:border-white/10\", job.priority === 'Urgent' && \"ring-2 ring-red-500/50 animate-pulse\"),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900 dark:text-white\",\n                    children: job.jobNumber\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 27\n                  }, this), getPriorityBadge(job.priority)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 25\n                }, this), getStatusBadge(job.status)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-gray-900 dark:text-white\",\n                  children: job.studentName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 dark:text-gray-400\",\n                  children: job.studentEmail\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(FileText, {\n                    size: 14,\n                    className: \"mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 27\n                  }, this), job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Type:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 32\n                    }, this), \" \", job.printType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Copies:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 32\n                    }, this), \" \", job.copies]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Color:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 468,\n                      columnNumber: 32\n                    }, this), \" \", job.colorType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Size:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 469,\n                      columnNumber: 32\n                    }, this), \" \", job.paperSize]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 23\n              }, this), job.cost && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-semibold text-green-600 dark:text-green-400\",\n                  children: [\"$\", job.cost.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500 mb-4\",\n                children: getTimeAgo(job.created)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => handleDownloadFile(job.id, job.fileName),\n                  className: \"p-2 rounded-lg bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 transition-colors\",\n                  title: \"Download\",\n                  children: /*#__PURE__*/_jsxDEV(Download, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 25\n                }, this), job.status === 'Requested' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    onClick: () => {\n                      setSelectedJob(job);\n                      setShowQuoteModal(true);\n                    },\n                    className: \"p-2 rounded-lg bg-green-500/10 text-green-600 hover:bg-green-500/20 transition-colors\",\n                    title: \"Quote\",\n                    children: /*#__PURE__*/_jsxDEV(DollarSign, {\n                      size: 14\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    onClick: () => handleStatusUpdate(job.id, 'Rejected'),\n                    className: \"p-2 rounded-lg bg-red-500/10 text-red-600 hover:bg-red-500/20 transition-colors\",\n                    title: \"Reject\",\n                    children: /*#__PURE__*/_jsxDEV(X, {\n                      size: 14\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 517,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true), job.status === 'Confirmed' && /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => handleStatusUpdate(job.id, 'InProgress'),\n                  className: \"p-2 rounded-lg bg-purple-500/10 text-purple-600 hover:bg-purple-500/20 transition-colors\",\n                  title: \"Start\",\n                  children: /*#__PURE__*/_jsxDEV(Play, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 27\n                }, this), job.status === 'InProgress' && /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => handleStatusUpdate(job.id, 'Completed'),\n                  className: \"p-2 rounded-lg bg-green-500/10 text-green-600 hover:bg-green-500/20 transition-colors\",\n                  title: \"Complete\",\n                  children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 27\n                }, this), job.status === 'Completed' && /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => handleStatusUpdate(job.id, 'Delivered'),\n                  className: \"p-2 rounded-lg bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 transition-colors\",\n                  title: \"Deliver\",\n                  children: /*#__PURE__*/_jsxDEV(Truck, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => handleOpenChat(job),\n                  className: \"p-2 rounded-lg bg-gray-500/10 text-gray-600 hover:bg-gray-500/20 transition-colors\",\n                  title: \"Chat\",\n                  children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => {\n                    setSelectedJob(job);\n                    setShowJobDetailsModal(true);\n                  },\n                  className: \"p-2 rounded-lg bg-indigo-500/10 text-indigo-600 hover:bg-indigo-500/20 transition-colors\",\n                  title: \"Details\",\n                  children: /*#__PURE__*/_jsxDEV(Eye, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 21\n            }, this)\n          }, job.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 15\n        }, this) :\n        /*#__PURE__*/\n        // Table view implementation would go here\n        _jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 dark:text-gray-400\",\n            children: \"Table view coming soon...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(FileText, {\n              className: \"w-10 h-10 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n            children: \"No jobs found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 dark:text-gray-400\",\n            children: \"No jobs match your current filter criteria.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showQuoteModal,\n        onHide: () => setShowQuoteModal(false),\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: \"Submit Quote\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: selectedJob && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Job:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 20\n              }, this), \" \", selectedJob.jobNumber]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Student:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 20\n              }, this), \" \", selectedJob.studentName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"File:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 20\n              }, this), \" \", selectedJob.fileName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Quote Amount ($)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                step: \"0.01\",\n                value: quoteAmount,\n                onChange: e => setQuoteAmount(e.target.value),\n                placeholder: \"Enter quote amount\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Estimated Completion Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"datetime-local\",\n                value: estimatedTime,\n                onChange: e => setEstimatedTime(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowQuoteModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleSubmitQuote,\n            children: \"Submit Quote\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showJobDetailsModal,\n        onHide: () => setShowJobDetailsModal(false),\n        size: \"lg\",\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: \"Job Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: selectedJob && /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Student Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.studentName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.studentEmail]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mt-3\",\n                children: \"File Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"File Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.fileName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 19\n              }, this), selectedJob.fileSize && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"File Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 24\n                }, this), \" \", formatFileSize(selectedJob.fileSize)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Print Specifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.printType]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Copies:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.copies]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Color:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.colorType]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Paper Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.paperSize]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mt-3\",\n                children: \"Job Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 22\n                }, this), \" \", getStatusBadge(selectedJob.status)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Priority:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 22\n                }, this), \" \", getPriorityBadge(selectedJob.priority)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 19\n              }, this), selectedJob.cost && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Cost:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 24\n                }, this), \" $\", selectedJob.cost.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Created:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 22\n                }, this), \" \", new Date(selectedJob.created).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowJobDetailsModal(false),\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 5\n  }, this);\n};\n_s(AceternityXeroxDashboard, \"+dnV9CQmRxH8nT1Kov+7MTxG5Cs=\", false, function () {\n  return [useAuth];\n});\n_c = AceternityXeroxDashboard;\nexport default AceternityXeroxDashboard;\nvar _c;\n$RefreshReg$(_c, \"AceternityXeroxDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Form", "Modal", "motion", "FileText", "Clock", "Cog", "DollarSign", "Download", "MessageCircle", "Eye", "Play", "CheckCircle", "Truck", "X", "Search", "Grid3X3", "List", "Activity", "RefreshCw", "useAuth", "printJobApi", "fileUploadApi", "cn", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AceternityXeroxDashboard", "_s", "user", "jobs", "setJobs", "filteredJobs", "setFilteredJobs", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "sortBy", "setSortBy", "sortDirection", "setSortDirection", "viewMode", "setViewMode", "showQuoteModal", "setShowQuoteModal", "showJobDetailsModal", "setShowJobDetailsModal", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "quoteAmount", "setQuoteAmount", "estimatedTime", "setEstimatedTime", "isRefreshing", "setIsRefreshing", "stats", "setStats", "total", "pending", "inProgress", "completed", "revenue", "fetchJobs", "interval", "setInterval", "clearInterval", "filterAndSortJobs", "response", "getXeroxCenterJobs", "data", "totalJobs", "length", "pendingJobs", "filter", "job", "includes", "status", "inProgressJobs", "completedJobs", "totalRevenue", "reduce", "sum", "cost", "error", "console", "filtered", "matchesSearch", "jobNumber", "toLowerCase", "fileName", "studentName", "studentEmail", "printType", "matchesStatus", "sort", "a", "b", "aValue", "bValue", "Date", "created", "getTime", "localeCompare", "getStatusBadge", "statusConfig", "variant", "config", "AceternityBadge", "children", "_jsxFileName", "lineNumber", "columnNumber", "getPriorityBadge", "priority", "priorityConfig", "size", "handleStatusUpdate", "jobId", "newStatus", "updateJobStatus", "handleDownloadFile", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleOpenChat", "log", "id", "handleSubmitQuote", "submitQuote", "amount", "parseFloat", "estimatedCompletionTime", "getTimeAgo", "dateString", "now", "date", "diffInMinutes", "Math", "floor", "formatFileSize", "bytes", "sizes", "i", "round", "pow", "className", "fluid", "div", "initial", "opacity", "y", "animate", "transition", "duration", "username", "whileHover", "scale", "whileTap", "AceternityButton", "onClick", "disabled", "toLocaleTimeString", "md", "AceternityStatsCard", "title", "value", "icon", "color", "trend", "isPositive", "toFixed", "AceternityCard", "AceternityInput", "placeholder", "onChange", "e", "target", "map", "index", "lg", "delay", "slice", "copies", "colorType", "paperSize", "button", "show", "onHide", "Header", "closeButton", "Title", "Body", "Group", "Label", "Control", "type", "step", "Footer", "fileSize", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/AceternityXeroxDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Table, Badge, Alert, Form, Modal, InputGroup, Tabs, Tab, ProgressBar, Dropdown, ButtonGroup } from 'react-bootstrap';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  FileText, \n  Clock, \n  Cog, \n  DollarSign, \n  Download, \n  MessageCircle, \n  Eye, \n  Play, \n  CheckCircle, \n  Truck, \n  X, \n  Search,\n  Filter,\n  Grid3X3,\n  List,\n  TrendingUp,\n  Activity,\n  Users,\n  Printer,\n  BarChart3,\n  RefreshCw,\n  Calendar,\n  Star\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../../services/api';\n// import { AceternityCard, AceternityStatsCard, AceternityButton, AceternityBadge, AceternityInput } from './AceternityCard';\nimport { cn } from '../../lib/utils';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  fileSize?: number;\n  status: string;\n  priority: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  studentName: string;\n  studentEmail: string;\n  printType: string;\n  copies: number;\n  colorType: string;\n  paperSize: string;\n  remarks?: string;\n  created: string;\n}\n\nconst AceternityXeroxDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [jobs, setJobs] = useState<PrintJob[]>([]);\n  const [filteredJobs, setFilteredJobs] = useState<PrintJob[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('All');\n  const [sortBy, setSortBy] = useState('created');\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');\n  const [viewMode, setViewMode] = useState<'table' | 'cards'>('cards');\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [showJobDetailsModal, setShowJobDetailsModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState<PrintJob | null>(null);\n  const [quoteAmount, setQuoteAmount] = useState('');\n  const [estimatedTime, setEstimatedTime] = useState('');\n  const [isRefreshing, setIsRefreshing] = useState(false);\n\n  const [stats, setStats] = useState({\n    total: 0,\n    pending: 0,\n    inProgress: 0,\n    completed: 0,\n    revenue: 0\n  });\n\n  useEffect(() => {\n    fetchJobs();\n    const interval = setInterval(fetchJobs, 30000); // Auto-refresh every 30 seconds\n    return () => clearInterval(interval);\n  }, []);\n\n  useEffect(() => {\n    filterAndSortJobs();\n  }, [jobs, searchTerm, statusFilter, sortBy, sortDirection]);\n\n  const fetchJobs = async () => {\n    setIsRefreshing(true);\n    try {\n      const response = await printJobApi.getXeroxCenterJobs();\n      setJobs(response.data);\n      \n      // Calculate stats\n      const totalJobs = response.data.length;\n      const pendingJobs = response.data.filter((job: PrintJob) => ['Requested', 'UnderReview'].includes(job.status)).length;\n      const inProgressJobs = response.data.filter((job: PrintJob) => ['Confirmed', 'InProgress'].includes(job.status)).length;\n      const completedJobs = response.data.filter((job: PrintJob) => ['Completed', 'Delivered'].includes(job.status)).length;\n      const totalRevenue = response.data.reduce((sum: number, job: PrintJob) => sum + (job.cost || 0), 0);\n\n      setStats({\n        total: totalJobs,\n        pending: pendingJobs,\n        inProgress: inProgressJobs,\n        completed: completedJobs,\n        revenue: totalRevenue\n      });\n    } catch (error) {\n      console.error('Error fetching jobs:', error);\n      setJobs([]);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  const filterAndSortJobs = () => {\n    let filtered = jobs.filter(job => {\n      const matchesSearch = searchTerm === '' || \n        job.jobNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        job.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        job.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        job.studentEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        job.printType.toLowerCase().includes(searchTerm.toLowerCase());\n      \n      const matchesStatus = statusFilter === 'All' || job.status === statusFilter;\n      \n      return matchesSearch && matchesStatus;\n    });\n\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy as keyof PrintJob];\n      let bValue = b[sortBy as keyof PrintJob];\n      \n      if (sortBy === 'created') {\n        aValue = new Date(a.created).getTime();\n        bValue = new Date(b.created).getTime();\n      }\n      \n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        return sortDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);\n      }\n      \n      if (typeof aValue === 'number' && typeof bValue === 'number') {\n        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;\n      }\n      \n      return 0;\n    });\n\n    setFilteredJobs(filtered);\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { variant: 'default' as const },\n      'UnderReview': { variant: 'info' as const },\n      'Quoted': { variant: 'warning' as const },\n      'WaitingConfirmation': { variant: 'warning' as const },\n      'Confirmed': { variant: 'info' as const },\n      'InProgress': { variant: 'info' as const },\n      'Completed': { variant: 'success' as const },\n      'Delivered': { variant: 'success' as const },\n      'Rejected': { variant: 'error' as const },\n      'Cancelled': { variant: 'default' as const }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'default' as const };\n    \n    return (\n      <AceternityBadge variant={config.variant}>\n        {status}\n      </AceternityBadge>\n    );\n  };\n\n  const getPriorityBadge = (priority: string) => {\n    const priorityConfig = {\n      'Low': { variant: 'success' as const },\n      'Normal': { variant: 'default' as const },\n      'High': { variant: 'warning' as const },\n      'Urgent': { variant: 'error' as const }\n    };\n\n    const config = priorityConfig[priority as keyof typeof priorityConfig] || { variant: 'default' as const };\n    \n    return (\n      <AceternityBadge variant={config.variant} size=\"sm\">\n        {priority}\n      </AceternityBadge>\n    );\n  };\n\n  const handleStatusUpdate = async (jobId: number, newStatus: string) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n      fetchJobs();\n    } catch (error) {\n      console.error('Error updating job status:', error);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n    }\n  };\n\n  const handleOpenChat = async (job: PrintJob) => {\n    // Implementation for opening chat\n    console.log('Opening chat for job:', job.id);\n  };\n\n  const handleSubmitQuote = async () => {\n    if (!selectedJob || !quoteAmount) return;\n\n    try {\n      await printJobApi.submitQuote(selectedJob.id, {\n        amount: parseFloat(quoteAmount),\n        estimatedCompletionTime: estimatedTime\n      });\n\n      setShowQuoteModal(false);\n      setQuoteAmount('');\n      setEstimatedTime('');\n      setSelectedJob(null);\n      fetchJobs();\n    } catch (error) {\n      console.error('Error submitting quote:', error);\n    }\n  };\n\n  const getTimeAgo = (dateString: string) => {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    \n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n    return `${Math.floor(diffInMinutes / 1440)}d ago`;\n  };\n\n  const formatFileSize = (bytes?: number) => {\n    if (!bytes) return 'Unknown';\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"min-h-screen\">\n      <Container fluid className=\"py-6\">\n        {/* Header Section */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-8\"\n        >\n          <h1 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2\">\n            Xerox Center Dashboard\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 text-lg mb-6\">\n            Welcome back, <span className=\"font-semibold\">{user?.username}</span>! \n          </p>\n          \n          <div className=\"flex items-center justify-center space-x-4\">\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <AceternityButton\n                onClick={fetchJobs}\n                variant=\"outline\"\n                disabled={isRefreshing}\n                className=\"px-6\"\n              >\n                <RefreshCw className={cn(\"w-4 h-4 mr-2\", isRefreshing && \"animate-spin\")} />\n                Refresh\n              </AceternityButton>\n            </motion.div>\n            \n            <AceternityBadge variant=\"info\">\n              Last updated: {new Date().toLocaleTimeString()}\n            </AceternityBadge>\n          </div>\n        </motion.div>\n\n        {/* Statistics Cards */}\n        <Row className=\"mb-6 g-4\">\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"Total Jobs\"\n              value={stats.total}\n              icon={<FileText size={24} />}\n              color=\"blue\"\n              trend={{ value: 12, isPositive: true }}\n            />\n          </Col>\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"Pending Jobs\"\n              value={stats.pending}\n              icon={<Clock size={24} />}\n              color=\"orange\"\n              trend={{ value: 5, isPositive: false }}\n            />\n          </Col>\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"In Progress\"\n              value={stats.inProgress}\n              icon={<Cog size={24} />}\n              color=\"purple\"\n              trend={{ value: 8, isPositive: true }}\n            />\n          </Col>\n          <Col md={3}>\n            <AceternityStatsCard\n              title=\"Revenue\"\n              value={`$${stats.revenue.toFixed(2)}`}\n              icon={<DollarSign size={24} />}\n              color=\"green\"\n              trend={{ value: 15, isPositive: true }}\n            />\n          </Col>\n        </Row>\n\n        {/* Job Queue Section */}\n        <AceternityCard>\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n              <Activity className=\"w-5 h-5 inline mr-2\" />\n              Job Queue\n            </h3>\n            <div className=\"flex items-center space-x-3\">\n              <AceternityBadge variant=\"info\">\n                {filteredJobs.length} jobs\n              </AceternityBadge>\n              \n              <div className=\"flex rounded-lg bg-white/10 dark:bg-black/20 p-1\">\n                <button\n                  onClick={() => setViewMode('cards')}\n                  className={cn(\n                    \"p-2 rounded-md transition-all duration-200\",\n                    viewMode === 'cards' \n                      ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white\" \n                      : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n                  )}\n                >\n                  <Grid3X3 size={16} />\n                </button>\n                <button\n                  onClick={() => setViewMode('table')}\n                  className={cn(\n                    \"p-2 rounded-md transition-all duration-200\",\n                    viewMode === 'table' \n                      ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white\" \n                      : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n                  )}\n                >\n                  <List size={16} />\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Search and Filter Controls */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n            <AceternityInput\n              placeholder=\"Search jobs...\"\n              value={searchTerm}\n              onChange={setSearchTerm}\n              icon={<Search className=\"w-4 h-4 text-gray-400\" />}\n            />\n            \n            <div>\n              <select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                className=\"w-full px-4 py-3 rounded-xl border-0 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 bg-white/50 dark:bg-black/20 backdrop-blur-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 transition-all duration-200\"\n              >\n                <option value=\"All\">All Status</option>\n                <option value=\"Requested\">Requested</option>\n                <option value=\"UnderReview\">Under Review</option>\n                <option value=\"Quoted\">Quoted</option>\n                <option value=\"Confirmed\">Confirmed</option>\n                <option value=\"InProgress\">In Progress</option>\n                <option value=\"Completed\">Completed</option>\n                <option value=\"Delivered\">Delivered</option>\n              </select>\n            </div>\n            \n            <div>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"w-full px-4 py-3 rounded-xl border-0 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 bg-white/50 dark:bg-black/20 backdrop-blur-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 transition-all duration-200\"\n              >\n                <option value=\"created\">Sort by Date</option>\n                <option value=\"priority\">Sort by Priority</option>\n                <option value=\"status\">Sort by Status</option>\n                <option value=\"cost\">Sort by Cost</option>\n              </select>\n            </div>\n            \n            <AceternityButton\n              onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}\n              variant=\"outline\"\n              className=\"w-full\"\n            >\n              {sortDirection === 'asc' ? '↑' : '↓'} {sortDirection === 'asc' ? 'Ascending' : 'Descending'}\n            </AceternityButton>\n          </div>\n\n          {/* Job Display */}\n          {filteredJobs.length > 0 ? (\n            viewMode === 'cards' ? (\n              <Row className=\"g-4\">\n                {filteredJobs.map((job, index) => (\n                  <Col key={job.id} md={6} lg={4}>\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: index * 0.05 }}\n                      className={cn(\n                        \"p-4 rounded-xl border transition-all duration-200 hover:shadow-lg\",\n                        \"bg-white/50 dark:bg-black/20 border-white/20 dark:border-white/10\",\n                        job.priority === 'Urgent' && \"ring-2 ring-red-500/50 animate-pulse\"\n                      )}\n                    >\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <div>\n                          <h4 className=\"font-semibold text-gray-900 dark:text-white\">\n                            {job.jobNumber}\n                          </h4>\n                          {getPriorityBadge(job.priority)}\n                        </div>\n                        {getStatusBadge(job.status)}\n                      </div>\n                      \n                      <div className=\"mb-3\">\n                        <p className=\"font-medium text-gray-900 dark:text-white\">\n                          {job.studentName}\n                        </p>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {job.studentEmail}\n                        </p>\n                      </div>\n                      \n                      <div className=\"mb-3\">\n                        <div className=\"flex items-center text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                          <FileText size={14} className=\"mr-1\" />\n                          {job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName}\n                        </div>\n                        <div className=\"text-xs text-gray-500 space-y-1\">\n                          <div><strong>Type:</strong> {job.printType}</div>\n                          <div><strong>Copies:</strong> {job.copies}</div>\n                          <div><strong>Color:</strong> {job.colorType}</div>\n                          <div><strong>Size:</strong> {job.paperSize}</div>\n                        </div>\n                      </div>\n                      \n                      {job.cost && (\n                        <div className=\"mb-3\">\n                          <span className=\"text-lg font-semibold text-green-600 dark:text-green-400\">\n                            ${job.cost.toFixed(2)}\n                          </span>\n                        </div>\n                      )}\n                      \n                      <div className=\"text-xs text-gray-500 mb-4\">\n                        {getTimeAgo(job.created)}\n                      </div>\n                      \n                      <div className=\"flex flex-wrap gap-2\">\n                        <motion.button\n                          whileHover={{ scale: 1.05 }}\n                          whileTap={{ scale: 0.95 }}\n                          onClick={() => handleDownloadFile(job.id, job.fileName)}\n                          className=\"p-2 rounded-lg bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 transition-colors\"\n                          title=\"Download\"\n                        >\n                          <Download size={14} />\n                        </motion.button>\n                        \n                        {job.status === 'Requested' && (\n                          <>\n                            <motion.button\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                              onClick={() => {\n                                setSelectedJob(job);\n                                setShowQuoteModal(true);\n                              }}\n                              className=\"p-2 rounded-lg bg-green-500/10 text-green-600 hover:bg-green-500/20 transition-colors\"\n                              title=\"Quote\"\n                            >\n                              <DollarSign size={14} />\n                            </motion.button>\n                            <motion.button\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                              onClick={() => handleStatusUpdate(job.id, 'Rejected')}\n                              className=\"p-2 rounded-lg bg-red-500/10 text-red-600 hover:bg-red-500/20 transition-colors\"\n                              title=\"Reject\"\n                            >\n                              <X size={14} />\n                            </motion.button>\n                          </>\n                        )}\n\n                        {job.status === 'Confirmed' && (\n                          <motion.button\n                            whileHover={{ scale: 1.05 }}\n                            whileTap={{ scale: 0.95 }}\n                            onClick={() => handleStatusUpdate(job.id, 'InProgress')}\n                            className=\"p-2 rounded-lg bg-purple-500/10 text-purple-600 hover:bg-purple-500/20 transition-colors\"\n                            title=\"Start\"\n                          >\n                            <Play size={14} />\n                          </motion.button>\n                        )}\n\n                        {job.status === 'InProgress' && (\n                          <motion.button\n                            whileHover={{ scale: 1.05 }}\n                            whileTap={{ scale: 0.95 }}\n                            onClick={() => handleStatusUpdate(job.id, 'Completed')}\n                            className=\"p-2 rounded-lg bg-green-500/10 text-green-600 hover:bg-green-500/20 transition-colors\"\n                            title=\"Complete\"\n                          >\n                            <CheckCircle size={14} />\n                          </motion.button>\n                        )}\n\n                        {job.status === 'Completed' && (\n                          <motion.button\n                            whileHover={{ scale: 1.05 }}\n                            whileTap={{ scale: 0.95 }}\n                            onClick={() => handleStatusUpdate(job.id, 'Delivered')}\n                            className=\"p-2 rounded-lg bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 transition-colors\"\n                            title=\"Deliver\"\n                          >\n                            <Truck size={14} />\n                          </motion.button>\n                        )}\n\n                        <motion.button\n                          whileHover={{ scale: 1.05 }}\n                          whileTap={{ scale: 0.95 }}\n                          onClick={() => handleOpenChat(job)}\n                          className=\"p-2 rounded-lg bg-gray-500/10 text-gray-600 hover:bg-gray-500/20 transition-colors\"\n                          title=\"Chat\"\n                        >\n                          <MessageCircle size={14} />\n                        </motion.button>\n                        \n                        <motion.button\n                          whileHover={{ scale: 1.05 }}\n                          whileTap={{ scale: 0.95 }}\n                          onClick={() => {\n                            setSelectedJob(job);\n                            setShowJobDetailsModal(true);\n                          }}\n                          className=\"p-2 rounded-lg bg-indigo-500/10 text-indigo-600 hover:bg-indigo-500/20 transition-colors\"\n                          title=\"Details\"\n                        >\n                          <Eye size={14} />\n                        </motion.button>\n                      </div>\n                    </motion.div>\n                  </Col>\n                ))}\n              </Row>\n            ) : (\n              // Table view implementation would go here\n              <div className=\"text-center py-8\">\n                <p className=\"text-gray-600 dark:text-gray-400\">Table view coming soon...</p>\n              </div>\n            )\n          ) : (\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              className=\"text-center py-12\"\n            >\n              <div className=\"w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mx-auto mb-4\">\n                <FileText className=\"w-10 h-10 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                No jobs found\n              </h3>\n              <p className=\"text-gray-600 dark:text-gray-400\">\n                No jobs match your current filter criteria.\n              </p>\n            </motion.div>\n          )}\n        </AceternityCard>\n\n        {/* Quote Modal */}\n        <Modal show={showQuoteModal} onHide={() => setShowQuoteModal(false)}>\n          <Modal.Header closeButton>\n            <Modal.Title>Submit Quote</Modal.Title>\n          </Modal.Header>\n          <Modal.Body>\n            {selectedJob && (\n              <div>\n                <p><strong>Job:</strong> {selectedJob.jobNumber}</p>\n                <p><strong>Student:</strong> {selectedJob.studentName}</p>\n                <p><strong>File:</strong> {selectedJob.fileName}</p>\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Quote Amount ($)</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={quoteAmount}\n                    onChange={(e) => setQuoteAmount(e.target.value)}\n                    placeholder=\"Enter quote amount\"\n                  />\n                </Form.Group>\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Estimated Completion Time</Form.Label>\n                  <Form.Control\n                    type=\"datetime-local\"\n                    value={estimatedTime}\n                    onChange={(e) => setEstimatedTime(e.target.value)}\n                  />\n                </Form.Group>\n              </div>\n            )}\n          </Modal.Body>\n          <Modal.Footer>\n            <Button variant=\"secondary\" onClick={() => setShowQuoteModal(false)}>\n              Cancel\n            </Button>\n            <Button variant=\"primary\" onClick={handleSubmitQuote}>\n              Submit Quote\n            </Button>\n          </Modal.Footer>\n        </Modal>\n\n        {/* Job Details Modal */}\n        <Modal show={showJobDetailsModal} onHide={() => setShowJobDetailsModal(false)} size=\"lg\">\n          <Modal.Header closeButton>\n            <Modal.Title>Job Details</Modal.Title>\n          </Modal.Header>\n          <Modal.Body>\n            {selectedJob && (\n              <Row>\n                <Col md={6}>\n                  <h6>Student Information</h6>\n                  <p><strong>Name:</strong> {selectedJob.studentName}</p>\n                  <p><strong>Email:</strong> {selectedJob.studentEmail}</p>\n\n                  <h6 className=\"mt-3\">File Information</h6>\n                  <p><strong>File Name:</strong> {selectedJob.fileName}</p>\n                  {selectedJob.fileSize && (\n                    <p><strong>File Size:</strong> {formatFileSize(selectedJob.fileSize)}</p>\n                  )}\n                </Col>\n                <Col md={6}>\n                  <h6>Print Specifications</h6>\n                  <p><strong>Type:</strong> {selectedJob.printType}</p>\n                  <p><strong>Copies:</strong> {selectedJob.copies}</p>\n                  <p><strong>Color:</strong> {selectedJob.colorType}</p>\n                  <p><strong>Paper Size:</strong> {selectedJob.paperSize}</p>\n\n                  <h6 className=\"mt-3\">Job Status</h6>\n                  <p><strong>Status:</strong> {getStatusBadge(selectedJob.status)}</p>\n                  <p><strong>Priority:</strong> {getPriorityBadge(selectedJob.priority)}</p>\n                  {selectedJob.cost && (\n                    <p><strong>Cost:</strong> ${selectedJob.cost.toFixed(2)}</p>\n                  )}\n                  <p><strong>Created:</strong> {new Date(selectedJob.created).toLocaleString()}</p>\n                </Col>\n              </Row>\n            )}\n          </Modal.Body>\n          <Modal.Footer>\n            <Button variant=\"secondary\" onClick={() => setShowJobDetailsModal(false)}>\n              Close\n            </Button>\n          </Modal.Footer>\n        </Modal>\n      </Container>\n    </div>\n  );\n};\n\nexport default AceternityXeroxDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAuBC,IAAI,EAAEC,KAAK,QAAmE,iBAAiB;AAC1J,SAASC,MAAM,QAAyB,eAAe;AACvD,SACEC,QAAQ,EACRC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,aAAa,EACbC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,CAAC,EACDC,MAAM,EAENC,OAAO,EACPC,IAAI,EAEJC,QAAQ,EAIRC,SAAS,QAGJ,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,EAAkBC,aAAa,QAAoB,oBAAoB;AAC3F;AACA,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqBrC,MAAMC,wBAAkC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/C,MAAM;IAAEC;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACW,IAAI,EAAEC,OAAO,CAAC,GAAGrC,QAAQ,CAAa,EAAE,CAAC;EAChD,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAa,EAAE,CAAC;EAChE,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4C,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC,SAAS,CAAC;EAC/C,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAiB,MAAM,CAAC;EAC1E,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAoB,OAAO,CAAC;EACpE,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAkB,IAAI,CAAC;EACrE,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM,CAAC8D,KAAK,EAAEC,QAAQ,CAAC,GAAG/D,QAAQ,CAAC;IACjCgE,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE,CAAC;IACVC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EAEFnE,SAAS,CAAC,MAAM;IACdoE,SAAS,CAAC,CAAC;IACX,MAAMC,QAAQ,GAAGC,WAAW,CAACF,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;IAChD,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAENrE,SAAS,CAAC,MAAM;IACdwE,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACrC,IAAI,EAAEI,UAAU,EAAEE,YAAY,EAAEE,MAAM,EAAEE,aAAa,CAAC,CAAC;EAE3D,MAAMuB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BR,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAMhD,WAAW,CAACiD,kBAAkB,CAAC,CAAC;MACvDtC,OAAO,CAACqC,QAAQ,CAACE,IAAI,CAAC;;MAEtB;MACA,MAAMC,SAAS,GAAGH,QAAQ,CAACE,IAAI,CAACE,MAAM;MACtC,MAAMC,WAAW,GAAGL,QAAQ,CAACE,IAAI,CAACI,MAAM,CAAEC,GAAa,IAAK,CAAC,WAAW,EAAE,aAAa,CAAC,CAACC,QAAQ,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,CAACL,MAAM;MACrH,MAAMM,cAAc,GAAGV,QAAQ,CAACE,IAAI,CAACI,MAAM,CAAEC,GAAa,IAAK,CAAC,WAAW,EAAE,YAAY,CAAC,CAACC,QAAQ,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,CAACL,MAAM;MACvH,MAAMO,aAAa,GAAGX,QAAQ,CAACE,IAAI,CAACI,MAAM,CAAEC,GAAa,IAAK,CAAC,WAAW,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,CAACL,MAAM;MACrH,MAAMQ,YAAY,GAAGZ,QAAQ,CAACE,IAAI,CAACW,MAAM,CAAC,CAACC,GAAW,EAAEP,GAAa,KAAKO,GAAG,IAAIP,GAAG,CAACQ,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAEnG1B,QAAQ,CAAC;QACPC,KAAK,EAAEa,SAAS;QAChBZ,OAAO,EAAEc,WAAW;QACpBb,UAAU,EAAEkB,cAAc;QAC1BjB,SAAS,EAAEkB,aAAa;QACxBjB,OAAO,EAAEkB;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CrD,OAAO,CAAC,EAAE,CAAC;IACb,CAAC,SAAS;MACRwB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMY,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAImB,QAAQ,GAAGxD,IAAI,CAAC4C,MAAM,CAACC,GAAG,IAAI;MAChC,MAAMY,aAAa,GAAGrD,UAAU,KAAK,EAAE,IACrCyC,GAAG,CAACa,SAAS,CAACC,WAAW,CAAC,CAAC,CAACb,QAAQ,CAAC1C,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC,IAC9Dd,GAAG,CAACe,QAAQ,CAACD,WAAW,CAAC,CAAC,CAACb,QAAQ,CAAC1C,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC,IAC7Dd,GAAG,CAACgB,WAAW,CAACF,WAAW,CAAC,CAAC,CAACb,QAAQ,CAAC1C,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC,IAChEd,GAAG,CAACiB,YAAY,CAACH,WAAW,CAAC,CAAC,CAACb,QAAQ,CAAC1C,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC,IACjEd,GAAG,CAACkB,SAAS,CAACJ,WAAW,CAAC,CAAC,CAACb,QAAQ,CAAC1C,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC;MAEhE,MAAMK,aAAa,GAAG1D,YAAY,KAAK,KAAK,IAAIuC,GAAG,CAACE,MAAM,KAAKzC,YAAY;MAE3E,OAAOmD,aAAa,IAAIO,aAAa;IACvC,CAAC,CAAC;IAEFR,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAM,GAAGF,CAAC,CAAC1D,MAAM,CAAmB;MACxC,IAAI6D,MAAM,GAAGF,CAAC,CAAC3D,MAAM,CAAmB;MAExC,IAAIA,MAAM,KAAK,SAAS,EAAE;QACxB4D,MAAM,GAAG,IAAIE,IAAI,CAACJ,CAAC,CAACK,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC;QACtCH,MAAM,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACI,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC;MACxC;MAEA,IAAI,OAAOJ,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC5D,OAAO3D,aAAa,KAAK,KAAK,GAAG0D,MAAM,CAACK,aAAa,CAACJ,MAAM,CAAC,GAAGA,MAAM,CAACI,aAAa,CAACL,MAAM,CAAC;MAC9F;MAEA,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC5D,OAAO3D,aAAa,KAAK,KAAK,GAAG0D,MAAM,GAAGC,MAAM,GAAGA,MAAM,GAAGD,MAAM;MACpE;MAEA,OAAO,CAAC;IACV,CAAC,CAAC;IAEFjE,eAAe,CAACqD,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMkB,cAAc,GAAI3B,MAAc,IAAK;IACzC,MAAM4B,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,OAAO,EAAE;MAAmB,CAAC;MAC5C,aAAa,EAAE;QAAEA,OAAO,EAAE;MAAgB,CAAC;MAC3C,QAAQ,EAAE;QAAEA,OAAO,EAAE;MAAmB,CAAC;MACzC,qBAAqB,EAAE;QAAEA,OAAO,EAAE;MAAmB,CAAC;MACtD,WAAW,EAAE;QAAEA,OAAO,EAAE;MAAgB,CAAC;MACzC,YAAY,EAAE;QAAEA,OAAO,EAAE;MAAgB,CAAC;MAC1C,WAAW,EAAE;QAAEA,OAAO,EAAE;MAAmB,CAAC;MAC5C,WAAW,EAAE;QAAEA,OAAO,EAAE;MAAmB,CAAC;MAC5C,UAAU,EAAE;QAAEA,OAAO,EAAE;MAAiB,CAAC;MACzC,WAAW,EAAE;QAAEA,OAAO,EAAE;MAAmB;IAC7C,CAAC;IAED,MAAMC,MAAM,GAAGF,YAAY,CAAC5B,MAAM,CAA8B,IAAI;MAAE6B,OAAO,EAAE;IAAmB,CAAC;IAEnG,oBACElF,OAAA,CAACoF,eAAe;MAACF,OAAO,EAAEC,MAAM,CAACD,OAAQ;MAAAG,QAAA,EACtChC;IAAM;MAAAa,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAEtB,CAAC;EAED,MAAMC,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,MAAMC,cAAc,GAAG;MACrB,KAAK,EAAE;QAAET,OAAO,EAAE;MAAmB,CAAC;MACtC,QAAQ,EAAE;QAAEA,OAAO,EAAE;MAAmB,CAAC;MACzC,MAAM,EAAE;QAAEA,OAAO,EAAE;MAAmB,CAAC;MACvC,QAAQ,EAAE;QAAEA,OAAO,EAAE;MAAiB;IACxC,CAAC;IAED,MAAMC,MAAM,GAAGQ,cAAc,CAACD,QAAQ,CAAgC,IAAI;MAAER,OAAO,EAAE;IAAmB,CAAC;IAEzG,oBACElF,OAAA,CAACoF,eAAe;MAACF,OAAO,EAAEC,MAAM,CAACD,OAAQ;MAACU,IAAI,EAAC,IAAI;MAAAP,QAAA,EAChDK;IAAQ;MAAAxB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAEtB,CAAC;EAED,MAAMK,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAEC,SAAiB,KAAK;IACrE,IAAI;MACF,MAAMnG,WAAW,CAACoG,eAAe,CAACF,KAAK,EAAEC,SAAS,CAAC;MACnDxD,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAMqC,kBAAkB,GAAG,MAAAA,CAAOH,KAAa,EAAE5B,QAAgB,KAAK;IACpE,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAM/C,aAAa,CAACqG,YAAY,CAACJ,KAAK,CAAC;MACxD,MAAMK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACxD,QAAQ,CAACE,IAAI,CAAC,CAAC;MACtC,MAAMuD,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAG3C,QAAQ;MACxBwC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMuD,cAAc,GAAG,MAAOhE,GAAa,IAAK;IAC9C;IACAU,OAAO,CAACuD,GAAG,CAAC,uBAAuB,EAAEjE,GAAG,CAACkE,EAAE,CAAC;EAC9C,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC9F,WAAW,IAAI,CAACE,WAAW,EAAE;IAElC,IAAI;MACF,MAAM9B,WAAW,CAAC2H,WAAW,CAAC/F,WAAW,CAAC6F,EAAE,EAAE;QAC5CG,MAAM,EAAEC,UAAU,CAAC/F,WAAW,CAAC;QAC/BgG,uBAAuB,EAAE9F;MAC3B,CAAC,CAAC;MAEFP,iBAAiB,CAAC,KAAK,CAAC;MACxBM,cAAc,CAAC,EAAE,CAAC;MAClBE,gBAAgB,CAAC,EAAE,CAAC;MACpBJ,cAAc,CAAC,IAAI,CAAC;MACpBc,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAM+D,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,GAAG,GAAG,IAAIjD,IAAI,CAAC,CAAC;IACtB,MAAMkD,IAAI,GAAG,IAAIlD,IAAI,CAACgD,UAAU,CAAC;IACjC,MAAMG,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,CAAC/C,OAAO,CAAC,CAAC,GAAGgD,IAAI,CAAChD,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIiD,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,OAAO;IACtD,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC,OAAO;IACzE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC,OAAO;EACnD,CAAC;EAED,MAAMG,cAAc,GAAIC,KAAc,IAAK;IACzC,IAAI,CAACA,KAAK,EAAE,OAAO,SAAS;IAC5B,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGL,IAAI,CAACC,KAAK,CAACD,IAAI,CAACZ,GAAG,CAACe,KAAK,CAAC,GAAGH,IAAI,CAACZ,GAAG,CAAC,IAAI,CAAC,CAAC;IACtD,OAAOY,IAAI,CAACM,KAAK,CAACH,KAAK,GAAGH,IAAI,CAACO,GAAG,CAAC,IAAI,EAAEF,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGD,KAAK,CAACC,CAAC,CAAC;EAC3E,CAAC;EAED,oBACErI,OAAA;IAAKwI,SAAS,EAAC,cAAc;IAAAnD,QAAA,eAC3BrF,OAAA,CAAC5B,SAAS;MAACqK,KAAK;MAACD,SAAS,EAAC,MAAM;MAAAnD,QAAA,gBAE/BrF,OAAA,CAACtB,MAAM,CAACgK,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,kBAAkB;QAAAnD,QAAA,gBAE5BrF,OAAA;UAAIwI,SAAS,EAAC,oGAAoG;UAAAnD,QAAA,EAAC;QAEnH;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxF,OAAA;UAAGwI,SAAS,EAAC,+CAA+C;UAAAnD,QAAA,GAAC,gBAC7C,eAAArF,OAAA;YAAMwI,SAAS,EAAC,eAAe;YAAAnD,QAAA,EAAEhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4I;UAAQ;YAAA/E,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,KACvE;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJxF,OAAA;UAAKwI,SAAS,EAAC,4CAA4C;UAAAnD,QAAA,gBACzDrF,OAAA,CAACtB,MAAM,CAACgK,GAAG;YACTQ,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAA9D,QAAA,eAE1BrF,OAAA,CAACqJ,gBAAgB;cACfC,OAAO,EAAE/G,SAAU;cACnB2C,OAAO,EAAC,SAAS;cACjBqE,QAAQ,EAAEzH,YAAa;cACvB0G,SAAS,EAAC,MAAM;cAAAnD,QAAA,gBAEhBrF,OAAA,CAACN,SAAS;gBAAC8I,SAAS,EAAE1I,EAAE,CAAC,cAAc,EAAEgC,YAAY,IAAI,cAAc;cAAE;gBAAAoC,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAE9E;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAkB;UAAC;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEbxF,OAAA,CAACoF,eAAe;YAACF,OAAO,EAAC,MAAM;YAAAG,QAAA,GAAC,gBAChB,EAAC,IAAIT,IAAI,CAAC,CAAC,CAAC4E,kBAAkB,CAAC,CAAC;UAAA;YAAAtF,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbxF,OAAA,CAAC3B,GAAG;QAACmK,SAAS,EAAC,UAAU;QAAAnD,QAAA,gBACvBrF,OAAA,CAAC1B,GAAG;UAACmL,EAAE,EAAE,CAAE;UAAApE,QAAA,eACTrF,OAAA,CAAC0J,mBAAmB;YAClBC,KAAK,EAAC,YAAY;YAClBC,KAAK,EAAE5H,KAAK,CAACE,KAAM;YACnB2H,IAAI,eAAE7J,OAAA,CAACrB,QAAQ;cAACiH,IAAI,EAAE;YAAG;cAAA1B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BsE,KAAK,EAAC,MAAM;YACZC,KAAK,EAAE;cAAEH,KAAK,EAAE,EAAE;cAAEI,UAAU,EAAE;YAAK;UAAE;YAAA9F,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxF,OAAA,CAAC1B,GAAG;UAACmL,EAAE,EAAE,CAAE;UAAApE,QAAA,eACTrF,OAAA,CAAC0J,mBAAmB;YAClBC,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAE5H,KAAK,CAACG,OAAQ;YACrB0H,IAAI,eAAE7J,OAAA,CAACpB,KAAK;cAACgH,IAAI,EAAE;YAAG;cAAA1B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BsE,KAAK,EAAC,QAAQ;YACdC,KAAK,EAAE;cAAEH,KAAK,EAAE,CAAC;cAAEI,UAAU,EAAE;YAAM;UAAE;YAAA9F,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxF,OAAA,CAAC1B,GAAG;UAACmL,EAAE,EAAE,CAAE;UAAApE,QAAA,eACTrF,OAAA,CAAC0J,mBAAmB;YAClBC,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE5H,KAAK,CAACI,UAAW;YACxByH,IAAI,eAAE7J,OAAA,CAACnB,GAAG;cAAC+G,IAAI,EAAE;YAAG;cAAA1B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxBsE,KAAK,EAAC,QAAQ;YACdC,KAAK,EAAE;cAAEH,KAAK,EAAE,CAAC;cAAEI,UAAU,EAAE;YAAK;UAAE;YAAA9F,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxF,OAAA,CAAC1B,GAAG;UAACmL,EAAE,EAAE,CAAE;UAAApE,QAAA,eACTrF,OAAA,CAAC0J,mBAAmB;YAClBC,KAAK,EAAC,SAAS;YACfC,KAAK,EAAE,IAAI5H,KAAK,CAACM,OAAO,CAAC2H,OAAO,CAAC,CAAC,CAAC,EAAG;YACtCJ,IAAI,eAAE7J,OAAA,CAAClB,UAAU;cAAC8G,IAAI,EAAE;YAAG;cAAA1B,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BsE,KAAK,EAAC,OAAO;YACbC,KAAK,EAAE;cAAEH,KAAK,EAAE,EAAE;cAAEI,UAAU,EAAE;YAAK;UAAE;YAAA9F,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxF,OAAA,CAACkK,cAAc;QAAA7E,QAAA,gBACbrF,OAAA;UAAKwI,SAAS,EAAC,wCAAwC;UAAAnD,QAAA,gBACrDrF,OAAA;YAAIwI,SAAS,EAAC,kGAAkG;YAAAnD,QAAA,gBAC9GrF,OAAA,CAACP,QAAQ;cAAC+I,SAAS,EAAC;YAAqB;cAAAtE,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAE9C;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxF,OAAA;YAAKwI,SAAS,EAAC,6BAA6B;YAAAnD,QAAA,gBAC1CrF,OAAA,CAACoF,eAAe;cAACF,OAAO,EAAC,MAAM;cAAAG,QAAA,GAC5B7E,YAAY,CAACwC,MAAM,EAAC,OACvB;YAAA;cAAAkB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eAElBxF,OAAA;cAAKwI,SAAS,EAAC,kDAAkD;cAAAnD,QAAA,gBAC/DrF,OAAA;gBACEsJ,OAAO,EAAEA,CAAA,KAAMnI,WAAW,CAAC,OAAO,CAAE;gBACpCqH,SAAS,EAAE1I,EAAE,CACX,4CAA4C,EAC5CoB,QAAQ,KAAK,OAAO,GAChB,yDAAyD,GACzD,4EACN,CAAE;gBAAAmE,QAAA,eAEFrF,OAAA,CAACT,OAAO;kBAACqG,IAAI,EAAE;gBAAG;kBAAA1B,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACTxF,OAAA;gBACEsJ,OAAO,EAAEA,CAAA,KAAMnI,WAAW,CAAC,OAAO,CAAE;gBACpCqH,SAAS,EAAE1I,EAAE,CACX,4CAA4C,EAC5CoB,QAAQ,KAAK,OAAO,GAChB,yDAAyD,GACzD,4EACN,CAAE;gBAAAmE,QAAA,eAEFrF,OAAA,CAACR,IAAI;kBAACoG,IAAI,EAAE;gBAAG;kBAAA1B,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxF,OAAA;UAAKwI,SAAS,EAAC,4CAA4C;UAAAnD,QAAA,gBACzDrF,OAAA,CAACmK,eAAe;YACdC,WAAW,EAAC,gBAAgB;YAC5BR,KAAK,EAAElJ,UAAW;YAClB2J,QAAQ,EAAE1J,aAAc;YACxBkJ,IAAI,eAAE7J,OAAA,CAACV,MAAM;cAACkJ,SAAS,EAAC;YAAuB;cAAAtE,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eAEFxF,OAAA;YAAAqF,QAAA,eACErF,OAAA;cACE4J,KAAK,EAAEhJ,YAAa;cACpByJ,QAAQ,EAAGC,CAAC,IAAKzJ,eAAe,CAACyJ,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;cACjDpB,SAAS,EAAC,kOAAkO;cAAAnD,QAAA,gBAE5OrF,OAAA;gBAAQ4J,KAAK,EAAC,KAAK;gBAAAvE,QAAA,EAAC;cAAU;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvCxF,OAAA;gBAAQ4J,KAAK,EAAC,WAAW;gBAAAvE,QAAA,EAAC;cAAS;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxF,OAAA;gBAAQ4J,KAAK,EAAC,aAAa;gBAAAvE,QAAA,EAAC;cAAY;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjDxF,OAAA;gBAAQ4J,KAAK,EAAC,QAAQ;gBAAAvE,QAAA,EAAC;cAAM;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCxF,OAAA;gBAAQ4J,KAAK,EAAC,WAAW;gBAAAvE,QAAA,EAAC;cAAS;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxF,OAAA;gBAAQ4J,KAAK,EAAC,YAAY;gBAAAvE,QAAA,EAAC;cAAW;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/CxF,OAAA;gBAAQ4J,KAAK,EAAC,WAAW;gBAAAvE,QAAA,EAAC;cAAS;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxF,OAAA;gBAAQ4J,KAAK,EAAC,WAAW;gBAAAvE,QAAA,EAAC;cAAS;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENxF,OAAA;YAAAqF,QAAA,eACErF,OAAA;cACE4J,KAAK,EAAE9I,MAAO;cACduJ,QAAQ,EAAGC,CAAC,IAAKvJ,SAAS,CAACuJ,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;cAC3CpB,SAAS,EAAC,kOAAkO;cAAAnD,QAAA,gBAE5OrF,OAAA;gBAAQ4J,KAAK,EAAC,SAAS;gBAAAvE,QAAA,EAAC;cAAY;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7CxF,OAAA;gBAAQ4J,KAAK,EAAC,UAAU;gBAAAvE,QAAA,EAAC;cAAgB;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDxF,OAAA;gBAAQ4J,KAAK,EAAC,QAAQ;gBAAAvE,QAAA,EAAC;cAAc;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9CxF,OAAA;gBAAQ4J,KAAK,EAAC,MAAM;gBAAAvE,QAAA,EAAC;cAAY;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENxF,OAAA,CAACqJ,gBAAgB;YACfC,OAAO,EAAEA,CAAA,KAAMrI,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAE;YAC1EkE,OAAO,EAAC,SAAS;YACjBsD,SAAS,EAAC,QAAQ;YAAAnD,QAAA,GAEjBrE,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,EAAC,GAAC,EAACA,aAAa,KAAK,KAAK,GAAG,WAAW,GAAG,YAAY;UAAA;YAAAkD,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,EAGLhF,YAAY,CAACwC,MAAM,GAAG,CAAC,GACtB9B,QAAQ,KAAK,OAAO,gBAClBlB,OAAA,CAAC3B,GAAG;UAACmK,SAAS,EAAC,KAAK;UAAAnD,QAAA,EACjB7E,YAAY,CAACgK,GAAG,CAAC,CAACrH,GAAG,EAAEsH,KAAK,kBAC3BzK,OAAA,CAAC1B,GAAG;YAAcmL,EAAE,EAAE,CAAE;YAACiB,EAAE,EAAE,CAAE;YAAArF,QAAA,eAC7BrF,OAAA,CAACtB,MAAM,CAACgK,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAE4B,KAAK,EAAEF,KAAK,GAAG;cAAK,CAAE;cACpCjC,SAAS,EAAE1I,EAAE,CACX,mEAAmE,EACnE,mEAAmE,EACnEqD,GAAG,CAACuC,QAAQ,KAAK,QAAQ,IAAI,sCAC/B,CAAE;cAAAL,QAAA,gBAEFrF,OAAA;gBAAKwI,SAAS,EAAC,uCAAuC;gBAAAnD,QAAA,gBACpDrF,OAAA;kBAAAqF,QAAA,gBACErF,OAAA;oBAAIwI,SAAS,EAAC,6CAA6C;oBAAAnD,QAAA,EACxDlC,GAAG,CAACa;kBAAS;oBAAAE,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,EACJC,gBAAgB,CAACtC,GAAG,CAACuC,QAAQ,CAAC;gBAAA;kBAAAxB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,EACLR,cAAc,CAAC7B,GAAG,CAACE,MAAM,CAAC;cAAA;gBAAAa,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eAENxF,OAAA;gBAAKwI,SAAS,EAAC,MAAM;gBAAAnD,QAAA,gBACnBrF,OAAA;kBAAGwI,SAAS,EAAC,2CAA2C;kBAAAnD,QAAA,EACrDlC,GAAG,CAACgB;gBAAW;kBAAAD,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACJxF,OAAA;kBAAGwI,SAAS,EAAC,0CAA0C;kBAAAnD,QAAA,EACpDlC,GAAG,CAACiB;gBAAY;kBAAAF,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENxF,OAAA;gBAAKwI,SAAS,EAAC,MAAM;gBAAAnD,QAAA,gBACnBrF,OAAA;kBAAKwI,SAAS,EAAC,iEAAiE;kBAAAnD,QAAA,gBAC9ErF,OAAA,CAACrB,QAAQ;oBAACiH,IAAI,EAAE,EAAG;oBAAC4C,SAAS,EAAC;kBAAM;oBAAAtE,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACtCrC,GAAG,CAACe,QAAQ,CAAClB,MAAM,GAAG,EAAE,GAAGG,GAAG,CAACe,QAAQ,CAAC0G,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGzH,GAAG,CAACe,QAAQ;gBAAA;kBAAAA,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACNxF,OAAA;kBAAKwI,SAAS,EAAC,iCAAiC;kBAAAnD,QAAA,gBAC9CrF,OAAA;oBAAAqF,QAAA,gBAAKrF,OAAA;sBAAAqF,QAAA,EAAQ;oBAAK;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACrC,GAAG,CAACkB,SAAS;kBAAA;oBAAAH,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjDxF,OAAA;oBAAAqF,QAAA,gBAAKrF,OAAA;sBAAAqF,QAAA,EAAQ;oBAAO;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACrC,GAAG,CAAC0H,MAAM;kBAAA;oBAAA3G,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChDxF,OAAA;oBAAAqF,QAAA,gBAAKrF,OAAA;sBAAAqF,QAAA,EAAQ;oBAAM;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACrC,GAAG,CAAC2H,SAAS;kBAAA;oBAAA5G,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDxF,OAAA;oBAAAqF,QAAA,gBAAKrF,OAAA;sBAAAqF,QAAA,EAAQ;oBAAK;sBAAAnB,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACrC,GAAG,CAAC4H,SAAS;kBAAA;oBAAA7G,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAELrC,GAAG,CAACQ,IAAI,iBACP3D,OAAA;gBAAKwI,SAAS,EAAC,MAAM;gBAAAnD,QAAA,eACnBrF,OAAA;kBAAMwI,SAAS,EAAC,0DAA0D;kBAAAnD,QAAA,GAAC,GACxE,EAAClC,GAAG,CAACQ,IAAI,CAACsG,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAA/F,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN,eAEDxF,OAAA;gBAAKwI,SAAS,EAAC,4BAA4B;gBAAAnD,QAAA,EACxCsC,UAAU,CAACxE,GAAG,CAAC0B,OAAO;cAAC;gBAAAX,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eAENxF,OAAA;gBAAKwI,SAAS,EAAC,sBAAsB;gBAAAnD,QAAA,gBACnCrF,OAAA,CAACtB,MAAM,CAACsM,MAAM;kBACZ9B,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BG,OAAO,EAAEA,CAAA,KAAMrD,kBAAkB,CAAC9C,GAAG,CAACkE,EAAE,EAAElE,GAAG,CAACe,QAAQ,CAAE;kBACxDsE,SAAS,EAAC,oFAAoF;kBAC9FmB,KAAK,EAAC,UAAU;kBAAAtE,QAAA,eAEhBrF,OAAA,CAACjB,QAAQ;oBAAC6G,IAAI,EAAE;kBAAG;oBAAA1B,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EAEfrC,GAAG,CAACE,MAAM,KAAK,WAAW,iBACzBrD,OAAA,CAAAE,SAAA;kBAAAmF,QAAA,gBACErF,OAAA,CAACtB,MAAM,CAACsM,MAAM;oBACZ9B,UAAU,EAAE;sBAAEC,KAAK,EAAE;oBAAK,CAAE;oBAC5BC,QAAQ,EAAE;sBAAED,KAAK,EAAE;oBAAK,CAAE;oBAC1BG,OAAO,EAAEA,CAAA,KAAM;sBACb7H,cAAc,CAAC0B,GAAG,CAAC;sBACnB9B,iBAAiB,CAAC,IAAI,CAAC;oBACzB,CAAE;oBACFmH,SAAS,EAAC,uFAAuF;oBACjGmB,KAAK,EAAC,OAAO;oBAAAtE,QAAA,eAEbrF,OAAA,CAAClB,UAAU;sBAAC8G,IAAI,EAAE;oBAAG;sBAAA1B,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eAChBxF,OAAA,CAACtB,MAAM,CAACsM,MAAM;oBACZ9B,UAAU,EAAE;sBAAEC,KAAK,EAAE;oBAAK,CAAE;oBAC5BC,QAAQ,EAAE;sBAAED,KAAK,EAAE;oBAAK,CAAE;oBAC1BG,OAAO,EAAEA,CAAA,KAAMzD,kBAAkB,CAAC1C,GAAG,CAACkE,EAAE,EAAE,UAAU,CAAE;oBACtDmB,SAAS,EAAC,iFAAiF;oBAC3FmB,KAAK,EAAC,QAAQ;oBAAAtE,QAAA,eAEdrF,OAAA,CAACX,CAAC;sBAACuG,IAAI,EAAE;oBAAG;sBAAA1B,QAAA,EAAAoB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAtB,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA,eAChB,CACH,EAEArC,GAAG,CAACE,MAAM,KAAK,WAAW,iBACzBrD,OAAA,CAACtB,MAAM,CAACsM,MAAM;kBACZ9B,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BG,OAAO,EAAEA,CAAA,KAAMzD,kBAAkB,CAAC1C,GAAG,CAACkE,EAAE,EAAE,YAAY,CAAE;kBACxDmB,SAAS,EAAC,0FAA0F;kBACpGmB,KAAK,EAAC,OAAO;kBAAAtE,QAAA,eAEbrF,OAAA,CAACd,IAAI;oBAAC0G,IAAI,EAAE;kBAAG;oBAAA1B,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAChB,EAEArC,GAAG,CAACE,MAAM,KAAK,YAAY,iBAC1BrD,OAAA,CAACtB,MAAM,CAACsM,MAAM;kBACZ9B,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BG,OAAO,EAAEA,CAAA,KAAMzD,kBAAkB,CAAC1C,GAAG,CAACkE,EAAE,EAAE,WAAW,CAAE;kBACvDmB,SAAS,EAAC,uFAAuF;kBACjGmB,KAAK,EAAC,UAAU;kBAAAtE,QAAA,eAEhBrF,OAAA,CAACb,WAAW;oBAACyG,IAAI,EAAE;kBAAG;oBAAA1B,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAChB,EAEArC,GAAG,CAACE,MAAM,KAAK,WAAW,iBACzBrD,OAAA,CAACtB,MAAM,CAACsM,MAAM;kBACZ9B,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BG,OAAO,EAAEA,CAAA,KAAMzD,kBAAkB,CAAC1C,GAAG,CAACkE,EAAE,EAAE,WAAW,CAAE;kBACvDmB,SAAS,EAAC,oFAAoF;kBAC9FmB,KAAK,EAAC,SAAS;kBAAAtE,QAAA,eAEfrF,OAAA,CAACZ,KAAK;oBAACwG,IAAI,EAAE;kBAAG;oBAAA1B,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAChB,eAEDxF,OAAA,CAACtB,MAAM,CAACsM,MAAM;kBACZ9B,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BG,OAAO,EAAEA,CAAA,KAAMnC,cAAc,CAAChE,GAAG,CAAE;kBACnCqF,SAAS,EAAC,oFAAoF;kBAC9FmB,KAAK,EAAC,MAAM;kBAAAtE,QAAA,eAEZrF,OAAA,CAAChB,aAAa;oBAAC4G,IAAI,EAAE;kBAAG;oBAAA1B,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eAEhBxF,OAAA,CAACtB,MAAM,CAACsM,MAAM;kBACZ9B,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BG,OAAO,EAAEA,CAAA,KAAM;oBACb7H,cAAc,CAAC0B,GAAG,CAAC;oBACnB5B,sBAAsB,CAAC,IAAI,CAAC;kBAC9B,CAAE;kBACFiH,SAAS,EAAC,0FAA0F;kBACpGmB,KAAK,EAAC,SAAS;kBAAAtE,QAAA,eAEfrF,OAAA,CAACf,GAAG;oBAAC2G,IAAI,EAAE;kBAAG;oBAAA1B,QAAA,EAAAoB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAtB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAtB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC,GAvJLrC,GAAG,CAACkE,EAAE;YAAAnD,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwJX,CACN;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;QAAA;QAEN;QACAxF,OAAA;UAAKwI,SAAS,EAAC,kBAAkB;UAAAnD,QAAA,eAC/BrF,OAAA;YAAGwI,SAAS,EAAC,kCAAkC;YAAAnD,QAAA,EAAC;UAAyB;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CACN,gBAEDxF,OAAA,CAACtB,MAAM,CAACgK,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEO,KAAK,EAAE;UAAI,CAAE;UACpCL,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEO,KAAK,EAAE;UAAE,CAAE;UAClCX,SAAS,EAAC,mBAAmB;UAAAnD,QAAA,gBAE7BrF,OAAA;YAAKwI,SAAS,EAAC,oHAAoH;YAAAnD,QAAA,eACjIrF,OAAA,CAACrB,QAAQ;cAAC6J,SAAS,EAAC;YAAsB;cAAAtE,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNxF,OAAA;YAAIwI,SAAS,EAAC,0DAA0D;YAAAnD,QAAA,EAAC;UAEzE;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxF,OAAA;YAAGwI,SAAS,EAAC,kCAAkC;YAAAnD,QAAA,EAAC;UAEhD;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACb;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,eAGjBxF,OAAA,CAACvB,KAAK;QAACwM,IAAI,EAAE7J,cAAe;QAAC8J,MAAM,EAAEA,CAAA,KAAM7J,iBAAiB,CAAC,KAAK,CAAE;QAAAgE,QAAA,gBAClErF,OAAA,CAACvB,KAAK,CAAC0M,MAAM;UAACC,WAAW;UAAA/F,QAAA,eACvBrF,OAAA,CAACvB,KAAK,CAAC4M,KAAK;YAAAhG,QAAA,EAAC;UAAY;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACfxF,OAAA,CAACvB,KAAK,CAAC6M,IAAI;UAAAjG,QAAA,EACR7D,WAAW,iBACVxB,OAAA;YAAAqF,QAAA,gBACErF,OAAA;cAAAqF,QAAA,gBAAGrF,OAAA;gBAAAqF,QAAA,EAAQ;cAAI;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChE,WAAW,CAACwC,SAAS;YAAA;cAAAE,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDxF,OAAA;cAAAqF,QAAA,gBAAGrF,OAAA;gBAAAqF,QAAA,EAAQ;cAAQ;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChE,WAAW,CAAC2C,WAAW;YAAA;cAAAD,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1DxF,OAAA;cAAAqF,QAAA,gBAAGrF,OAAA;gBAAAqF,QAAA,EAAQ;cAAK;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChE,WAAW,CAAC0C,QAAQ;YAAA;cAAAA,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEpDxF,OAAA,CAACxB,IAAI,CAAC+M,KAAK;cAAC/C,SAAS,EAAC,MAAM;cAAAnD,QAAA,gBAC1BrF,OAAA,CAACxB,IAAI,CAACgN,KAAK;gBAAAnG,QAAA,EAAC;cAAgB;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzCxF,OAAA,CAACxB,IAAI,CAACiN,OAAO;gBACXC,IAAI,EAAC,QAAQ;gBACbC,IAAI,EAAC,MAAM;gBACX/B,KAAK,EAAElI,WAAY;gBACnB2I,QAAQ,EAAGC,CAAC,IAAK3I,cAAc,CAAC2I,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;gBAChDQ,WAAW,EAAC;cAAoB;gBAAAlG,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAEbxF,OAAA,CAACxB,IAAI,CAAC+M,KAAK;cAAC/C,SAAS,EAAC,MAAM;cAAAnD,QAAA,gBAC1BrF,OAAA,CAACxB,IAAI,CAACgN,KAAK;gBAAAnG,QAAA,EAAC;cAAyB;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClDxF,OAAA,CAACxB,IAAI,CAACiN,OAAO;gBACXC,IAAI,EAAC,gBAAgB;gBACrB9B,KAAK,EAAEhI,aAAc;gBACrByI,QAAQ,EAAGC,CAAC,IAAKzI,gBAAgB,CAACyI,CAAC,CAACC,MAAM,CAACX,KAAK;cAAE;gBAAA1F,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eACbxF,OAAA,CAACvB,KAAK,CAACmN,MAAM;UAAAvG,QAAA,gBACXrF,OAAA,CAACzB,MAAM;YAAC2G,OAAO,EAAC,WAAW;YAACoE,OAAO,EAAEA,CAAA,KAAMjI,iBAAiB,CAAC,KAAK,CAAE;YAAAgE,QAAA,EAAC;UAErE;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxF,OAAA,CAACzB,MAAM;YAAC2G,OAAO,EAAC,SAAS;YAACoE,OAAO,EAAEhC,iBAAkB;YAAAjC,QAAA,EAAC;UAEtD;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGRxF,OAAA,CAACvB,KAAK;QAACwM,IAAI,EAAE3J,mBAAoB;QAAC4J,MAAM,EAAEA,CAAA,KAAM3J,sBAAsB,CAAC,KAAK,CAAE;QAACqE,IAAI,EAAC,IAAI;QAAAP,QAAA,gBACtFrF,OAAA,CAACvB,KAAK,CAAC0M,MAAM;UAACC,WAAW;UAAA/F,QAAA,eACvBrF,OAAA,CAACvB,KAAK,CAAC4M,KAAK;YAAAhG,QAAA,EAAC;UAAW;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACfxF,OAAA,CAACvB,KAAK,CAAC6M,IAAI;UAAAjG,QAAA,EACR7D,WAAW,iBACVxB,OAAA,CAAC3B,GAAG;YAAAgH,QAAA,gBACFrF,OAAA,CAAC1B,GAAG;cAACmL,EAAE,EAAE,CAAE;cAAApE,QAAA,gBACTrF,OAAA;gBAAAqF,QAAA,EAAI;cAAmB;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BxF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAK;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChE,WAAW,CAAC2C,WAAW;cAAA;gBAAAD,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDxF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAM;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChE,WAAW,CAAC4C,YAAY;cAAA;gBAAAF,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEzDxF,OAAA;gBAAIwI,SAAS,EAAC,MAAM;gBAAAnD,QAAA,EAAC;cAAgB;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1CxF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAU;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChE,WAAW,CAAC0C,QAAQ;cAAA;gBAAAA,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACxDhE,WAAW,CAACqK,QAAQ,iBACnB7L,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAU;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC0C,cAAc,CAAC1G,WAAW,CAACqK,QAAQ,CAAC;cAAA;gBAAA3H,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACzE;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNxF,OAAA,CAAC1B,GAAG;cAACmL,EAAE,EAAE,CAAE;cAAApE,QAAA,gBACTrF,OAAA;gBAAAqF,QAAA,EAAI;cAAoB;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BxF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAK;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChE,WAAW,CAAC6C,SAAS;cAAA;gBAAAH,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDxF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAO;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChE,WAAW,CAACqJ,MAAM;cAAA;gBAAA3G,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpDxF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAM;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChE,WAAW,CAACsJ,SAAS;cAAA;gBAAA5G,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtDxF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAW;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChE,WAAW,CAACuJ,SAAS;cAAA;gBAAA7G,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE3DxF,OAAA;gBAAIwI,SAAS,EAAC,MAAM;gBAAAnD,QAAA,EAAC;cAAU;gBAAAnB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpCxF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAO;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACR,cAAc,CAACxD,WAAW,CAAC6B,MAAM,CAAC;cAAA;gBAAAa,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpExF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAS;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACC,gBAAgB,CAACjE,WAAW,CAACkE,QAAQ,CAAC;cAAA;gBAAAxB,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACzEhE,WAAW,CAACmC,IAAI,iBACf3D,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAK;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,MAAE,EAAChE,WAAW,CAACmC,IAAI,CAACsG,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAA/F,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC5D,eACDxF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAQ;kBAAAnB,QAAA,EAAAoB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIZ,IAAI,CAACpD,WAAW,CAACqD,OAAO,CAAC,CAACiH,cAAc,CAAC,CAAC;cAAA;gBAAA5H,QAAA,EAAAoB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAtB,QAAA,EAAAoB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAtB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eACbxF,OAAA,CAACvB,KAAK,CAACmN,MAAM;UAAAvG,QAAA,eACXrF,OAAA,CAACzB,MAAM;YAAC2G,OAAO,EAAC,WAAW;YAACoE,OAAO,EAAEA,CAAA,KAAM/H,sBAAsB,CAAC,KAAK,CAAE;YAAA8D,QAAA,EAAC;UAE1E;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAtB,QAAA,EAAAoB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACpF,EAAA,CAvoBID,wBAAkC;EAAA,QACrBR,OAAO;AAAA;AAAAoM,EAAA,GADpB5L,wBAAkC;AAyoBxC,eAAeA,wBAAwB;AAAC,IAAA4L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}